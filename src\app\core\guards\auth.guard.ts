import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { NotificationsService } from 'angular2-notifications';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { LoginService } from 'src/app/services/controllers/login.service';
@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router,
    private notificationService: NotificationsService,
    private _auth: LoginService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    /* Token Expiry to be cheched against idToken */
    const idToken = localStorage.getItem('idToken');
    if (idToken && idToken.length) {
      const expirationTime = JSON.parse(atob(idToken.split('.')[1])).exp;
      const isExpiredToken = Math.floor(new Date().getTime() / 1000) >= expirationTime;
      if (isExpiredToken) {
        const refreshToken = localStorage.getItem('refreshToken');
        const token = localStorage.getItem('idToken');
        if (refreshToken) {
          const payload = {
            refreshToken,
            token,
          };
          return this._auth.refreshToken(payload).pipe(
            map((res: any) => {
              if (Object.values(res).length) {
                const { idToken, refreshToken } = res?.data;
                const itemsToRemove = ['idToken', 'refreshToken'];

                if (idToken && refreshToken && res?.data) {
                  itemsToRemove.forEach((k) => localStorage.removeItem(k));
                  localStorage.setItem('idToken', idToken);
                  localStorage.setItem('refreshToken', refreshToken);
                }
                return true;
              } else {
                this.notificationService.warn('Login', 'Please login');
                let returningUrl = state.url;
                this.router.navigateByUrl(
                  `/login?returnUrl=${encodeURIComponent(returningUrl)}`
                );
                return false;
              }
            }), catchError((err: any)=>{
              throwError(err);
              let returningUrl = state.url;
              return this.router.navigateByUrl(
                `/login?returnUrl=${encodeURIComponent(returningUrl)}`
              );
            })
          );
        } else {
          this.notificationService.warn('Login', 'Please login');
          let returningUrl = state.url;
          return this.router.navigateByUrl(
            `/login?returnUrl=${encodeURIComponent(returningUrl)}`
          );
        }
      } else {
        const returnUrl = route.queryParams['returnUrl'];
        if (returnUrl) {
          this.router.navigateByUrl(returnUrl);
        }
        return true;
      }
    } else {
      let returningUrl = state.url;
      this.notificationService.warn('Login', 'Please login');
      return this.router.navigateByUrl(
        `/login?returnUrl=${encodeURIComponent(returningUrl)}`
      );
    }
  }
}
