import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import {
  getAddPermissions,
  getDeletePermissions,
  getEditPermissions,
  getExportPermissions,
  getViewAllPermissions,
  getViewPermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
@Injectable({
  providedIn: 'root',
})
export class PermissionsGuard implements CanActivate {
  canView: string[] = [];
  canAdd: string[] = [];
  canEdit: string[] = [];
  canExport: string[] = [];
  permissions: string[] = [];
  canViewAll: string[] = [];
  canDelete: string[] = [];
  constructor(private router: Router, private _store: Store<AppState>) {
    this._store.select(getEditPermissions).subscribe((canEdit: any) => {
      this.canEdit = canEdit;
    });
    this._store.select(getViewPermissions).subscribe((canView: any) => {
      this.canView = canView;
    });
    this._store.select(getAddPermissions).subscribe((canAdd: any) => {
      this.canAdd = canAdd;
    });
    this._store.select(getExportPermissions).subscribe((canExport: any) => {
      this.canExport = canExport;
    });
    this._store.select(getViewAllPermissions).subscribe((canViewAll: any) => {
      this.canViewAll = canViewAll;
    });
    this._store.select(getDeletePermissions).subscribe((canDelete: any) => {
      this.canDelete = canDelete;
    });
  }
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    /* Token Expiry to be cheched against idToken */
    if (
      state.url.includes('global-config') ||
      state.url.includes('leads') ||
      state.url.includes('task') ||
      state.url.includes('manage-role') ||
      state.url.includes('manage-user') ||
      state.url.includes('properties') ||
      state.url.includes('profile/profile-dashboard') ||
      state.url.includes('add-lead') ||
      state.url.includes('edit-lead') ||
      state.url.includes('add-property') ||
      state.url.includes('edit-property') ||
      state.url.includes('edit-user') ||
      state.url.includes('edit-user') ||
      state.url.includes('reports') ||
      state.url.includes('attendance') ||
      state.url.includes('listing-management')
    ) {
      if (
        state.url.includes('global-config') &&
        this.canView.includes('Integration')
      ) {
        return true;
      }
      if (state.url.includes('leads') && this.canView.includes('Leads')) {
        return true;
      }
      if (state.url.includes('task') && this.canView.includes('Todos')) {
        return true;
      }
      if (
        state.url.includes('properties') &&
        this.canView.includes('Properties')
      ) {
        return true;
      }
      if (state.url.includes('manage-role') && this.canView.includes('Roles')) {
        return true;
      }
      if (state.url.includes('manage-user') && this.canView.includes('Users')) {
        return true;
      }
      if (
        state.url.includes('profile/profile-dashboard') &&
        this.canView.includes('Profile')
      ) {
        return true;
      }
      if (state.url.includes('add-lead') && this.canAdd.includes('Leads')) {
        return true;
      }
      if (state.url.includes('edit-lead') && this.canEdit.includes('Leads')) {
        return true;
      }
      if (state.url.includes('add-user') && this.canAdd.includes('Users')) {
        return true;
      }
      if (state.url.includes('edit-user') && this.canEdit.includes('Users')) {
        return true;
      }
      if (
        state.url.includes('add-property') &&
        this.canAdd.includes('Properties')
      ) {
        return true;
      }
      if (
        state.url.includes('edit-property') &&
        this.canEdit.includes('Properties')
      ) {
        return true;
      }
      if (
        state.url.includes('reports') &&
        this.canView.includes('Reports')
      ) {
        return true;
      }
      if (
        state.url.includes('attendance') &&
        this.canViewAll.includes('Attendance')
      ) {
        return true;
      }
      if (
        state.url.includes('listing-management') &&
        this.canViewAll.includes('Properties')
      ) {
        return true;
      }
    }
    return true;
  }
}
