import { <PERSON><PERSON>p<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable()
export class TrimInterceptor implements HttpInterceptor {

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    if (req.urlWithParams && (req.urlWithParams.startsWith('https://') || req.urlWithParams.startsWith('http://')) && req.headers.get('Content-Type')) {
      const trimmedQueryString = this.getTrimmedQueryString(req.urlWithParams);

      const trimmedBody = this.trimBody(req.body);

      const baseUrl = req.url.split('?')[0];
      const trimmedUrlWithParams = `${baseUrl}?${trimmedQueryString}`;

      const clonedRequest = req.clone({ url: trimmedUrlWithParams, body: trimmedBody });
      return next.handle(clonedRequest);
    }

    return next.handle(req);
  }

  private getTrimmedQueryString(url: string): string {
    const urlObj = new URL(url);
    const queryParams = urlObj.search.slice(1).split('&');
    const trimmedParams = queryParams
      .map(param => {
        const [key, value] = param.split('=');
        if (value?.trim()) {
          return `${key}=${value.trim()}`;
        }
        return null;
      })
      .filter(param => param !== null);

    return trimmedParams.join('&');
  }

  private trimBody(body: any): any {
    if (body == null || this.isBinary(body)) {
      return body;
    }
    if (Array.isArray(body)) {
      return body.map(item => this.trimBody(item));
    }
    if (typeof body === 'object') {
      const trimmedBody: { [key: string]: any } = {};

      for (const key in body) {
        if (body.hasOwnProperty(key)) {
          const value = body[key];
          if (typeof value === 'string') {
            trimmedBody[key] = value.trim();
          } else {
            trimmedBody[key] = this.trimBody(value);
          }
        }
      }
      return trimmedBody;
    }
    if (typeof body === 'string') {
      return body.trim();
    }
    return body;
  }

  private isBinary(value: any): boolean {
    return value instanceof Blob || value instanceof File || value instanceof ArrayBuffer;
  }
}
