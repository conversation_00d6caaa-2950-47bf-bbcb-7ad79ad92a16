interface Document {
    documentName: string;
    filePath: string;
    type: number;
    bookedDocumentType: number;
    uploadedOn: string;
}

interface BrokerageInfo {
    soldPrice: number;
    agreementValue: number;
    brokerageCharges: number;
    netBrokerageAmount: number;
    gst: number;
    totalBrokerage: number;
    referralNumber: string;
    commission: number;
    commissionUnit: number;
    brokerageType: number;
    earnedBrokerage: number;
}

interface Booking {
    leadId: string;
    bookedDate: string;
    lastModifiedBy: string;
    lastModifiedOn: string;
    bookedBy: string;
    bookedByUser: string;
    bookedUnderName: string;
    userId: string;
    soldPrice: number;
    notes: string;
    projectsList: string[];
    propertiesList: string[];
    teamHead: string;
    agreementValue: number;
    documents: Document[];
    carParkingCharges: number;
    additionalCharges: number;
    tokenAmount: any;
    paymentMode: number;
    discount: string;
    discountUnit: number;
    discountMode: number;
    remainingAmount: number;
    paymentType: number;
    brokerageInfo: BrokerageInfo;
}