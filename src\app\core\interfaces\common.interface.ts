export interface Entity extends EntityBase {
  createdBy: string;
  lastModifiedBy: string;
  assignedTo: string;
  tenantId: string;
  userId: string;
}

export interface EntityBase {
  id: string;
  created: string;
  lastModified: string;
  isDeleted: boolean;
}

export interface Response<T> {
  succeeded: boolean;
  message: string;
  errors: string[];
  data: T;
}
export interface PagedResponse<T> extends Response<T> {
  items: T[];
  itemsCount: number;
  totalCount: number;
}

export interface Icon {
  name: string;
  id: string;
  description: any;
  isEnabled: boolean;
  inActiveImagePath: string;
  activeImagePath: string;
}