export interface Dashboard {
  allLeads: AllLead,
  newLeads: number,
  siteVisites: SiteVisites,
  leadDropped: number,
  meetings: Meeting,
  leadConverted: number,
  leadsInConversationWith: number,
  salesFunnel: SalesFunnel,
  source: {},
  tasks: Tasks,
  meetingsAndSiteVisits: MeetingsAndSiteVisits,
}

export interface AllLead{
  totalLeads: number,
  pendingLeads: number,
}

export interface Meeting{
  meetingsDone: number,
  totalMeeting: number,
  meetingsScheduled: number
}

export interface SiteVisites{
  siteVisitesDone: number,
  siteVisitesSheduled: number,
  totalSitesVisites: number,
}

export interface SalesFunnel {
  opportunities: number,
  proposals: number,
  negotiations: number,
  booked: number
}

export interface Tasks {
  tasks: {}
}

export interface MeetingsAndSiteVisits {
  meetings: {},
  siteVisits: {}
}
