export interface DataTopFilter {
    count:            number;
    enumValue:        number;
    name:             string;
    displayName:      string;
    filterPayloadKey: FilterPayloadKey;
    iconClass:        string;
    logoUrl:          string;
    isConverted?:     boolean;
    isDeleted?:       boolean;
    isDefault?:       boolean;
    children:         DataTopFilter[] | null;
    id: string | number;
}

export enum FilterPayloadKey {
    ProspectVisiblity = "ProspectVisiblity",
    FirstLevelFilter = "FirstLevelFilter",
    SecondLevelFilter = "SecondLevelFilter",
}

export interface ConversionStatus {
    id:                 string;
    baseId:             null | string;
    level:              number;
    status:             string;
    displayName:        string;
    actionName:         null | string;
    masterLeadStatusId: string;
    childTypes:         ConversionStatus[];
    lastModifiedOn:     Date;
}