import { Entity } from "./common.interface";

export interface GlobalSettings {
  notificationSettings: NotificationSettings,
  callSettings: CallSettings,
  hasInternationalSupport: boolean
}

export interface NotificationSettings {
  moduleSettings: ModuleSetting[]
}

export interface ModuleSetting {
  moduleName: number,
  event: number,
  minutesBefore: number[]
}

export interface CallSettings {
  callType: number
}
export interface WhatsappTemplate {
  description: string,
}

export interface GlobalSettings extends Entity {
  WhatsappTemplates: WhatsappTemplate[]
}

export interface AllowDuplicateOption {
  label: string;
  description: string;
  key: string;
  value: boolean;
  disabled: boolean;
}

export interface MandatoryNotesOption {
  label: string;
  key: string;
  value: boolean;
  disabled: boolean;
}

export interface MandatoryProjectOption {
  label: string;
  key: string;
  value: boolean;
  disabled: boolean;
}

export interface MandatoryPropertyOption {
  label: string;
  key: string;
  value: boolean;
  disabled: boolean;
}

export interface ModuleSettings {
  name: string;
  label: string;
  description: string;
  iconColor: string;
  iconClass: string;
  path: string;
}