import { EntityBase } from 'src/app/core/interfaces/common.interface';

export interface LeadSourceType extends EntityBase {
  displayName: string;
  value: number;
}

export interface MasterAreaUnitType extends EntityBase {
  areaUnit: string;
  conversionFactor: number;
}

export interface MasterBuilderInfoType extends EntityBase {
  brandName: string;
  imageURL: any;
}

export interface MasterLeadStatusType extends EntityBase {
  displayName: string;
  actionName: string;
  childTypes: MasterLeadStatusTypeChild[];
}

export interface MasterLeadStatusTypeChild extends EntityBase {
  displayName: string;
  actionName: string;
}

export interface MasterPropertyAmenityListType extends EntityBase {
  Basic: AmenityType[];
  Featured: AmenityType[];
  Nearby: AmenityType[];
}

export interface AmenityType extends EntityBase {
  id: string;
  amenityName: string;
  amenityDisplayName: string;
  imageURL: string;
  amenityType: AmenityType;
  category: string;
  propertyType: number[];
}

export interface MasterPropertyAttributeType extends EntityBase {
  attributeName: string;
  attributeDisplayName: string;
  attributeType: string;
  fieldType: number;
  defaultValue: string;
  basePropertyType: number[];
}

export interface MasterPropertyType extends EntityBase {
  displayName: string;
  childTypes: MasterPropertyTypeChild[];
}

export interface MasterPropertyTypeChild extends EntityBase {
  displayName: string;
}

export interface MasterUserServiceType extends EntityBase {
  displayName: string;
  description: any;
  imageURL: string;
}
