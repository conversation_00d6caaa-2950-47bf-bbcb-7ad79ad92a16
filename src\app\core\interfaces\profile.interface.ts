import { Entity } from "./common.interface";

export interface Address {
  id?: string,
  placeId: string,
  subLocality?: string,
  locality?: string,
  district?: string,
  city?: string,
  state?: string,
  country?: string,
  isGoogleMapLocation?: boolean,
}

export interface AboutUs extends Entity {
  aboutUs: string,
  missionAndVision: string,
  bio: string,
  socialMedias: SocialMedia[],
}

export interface SocialMedia extends Entity {
  socialMediaPlatform: string,
  socialMediaId: string
}

export interface Testimonial {
  id?: string,
  givenBy: string,
  companyName: string,
  description: string,
  imageURL: string
}

export interface Profile extends Entity {
  gstNumber: any;
  id: string,
  displayName: string,
  aboutUs: string,
  missionAndVision: string,
  bio: string,
  reraNumber: string,
  phoneNumber: string,
  email: string,
  website: string,
  yearsOfExperience: number,
  address: Address,
  logoImgUrl: string,
  bannerImgUrl: string,
  totalUnitsSold: number,
  projectsDeveloped: number,
  totalPropertiesSold: number,
  propertiesDeveloped: number,
  testimonials: Testimonial[],
  socialMedias: SocialMedia[],
  brochurePath: string,
}

export interface Recognition {
  id?: string,
  name: string,
  imageUrl: string
}

export interface Subscription {
  dateOfSubscription: string,
  paymentDueDate: string,
  planExpireDate: string,
  nextBillingType: string,
  paymentDue: number,
  billingType: number,
  daysLeft: number,
  daysCompleted: number,
  licenseBought: number,
  inActiveUsers: number,
  activeUsers: number,
  pricePerUserPerMonth: number,
  pricePerUserPerMonthWithoutGST: number,
  dueAmount: number,
  dueDate: string,
  licenseValidity: string,
  totalAmount: number,
}

export interface Transaction {
  transactionDate: string,
  planPrice: number,
  paidAmount: number,
  gst: number,
  total: number,
  amountDue: number,
  dueDate: number,
  mode: number
}