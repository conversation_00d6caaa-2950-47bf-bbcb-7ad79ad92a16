export interface UnitInfoFilter {
    ProjectId: string;
    Search: string;
    Area: number;
    AreaUnitId: string;
    pageNumber: number;
    PageSize: number;
    userSearch: string;
    path: string;
    CarpetArea: number;
    CarpetAreaUnitId: string
    BuiltupArea: number;
    BuiltupAreaUnitId: string;
    SuperBuiltupArea: number;
    SuperBuiltupAreaUnitId: string;
    MaintenanceCost: number;
    PricePerUnit: number;
    TotalPrice: number;
    Currency: string;
    UnitType: string[];
    UnitSubType: string[];
    BHKs: number[];
    BHKTypes: string[];
    Facings: number[]
    FurnishingStatuses: number[];
    NoOfBalconies: number[];
    NoOfBathrooms: number[];
    NoOfLivingrooms: number[];
    NoOfBedrooms: number[];
    NoOfUtilites: number[];
    NoOfKitchens: number[];
    NoOfMaximumOccupants: number[];
    OrderBy: string[];
    maxPrice: number
  }