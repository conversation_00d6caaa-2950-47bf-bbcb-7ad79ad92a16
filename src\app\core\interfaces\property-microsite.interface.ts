export interface AreaUnits {
  areaUnit: string;
  conversionFactor: number;
  id: string;
  created: string;
  lastModified: string;
  isDeleted: boolean;
}

export interface MonetaryInfo {
  expectedPrice?: number;
  isNegotiable?: boolean;
  brokerage?: number;
  brokerageUnit?: number;
}

export interface OwnerDetails {
  name?: string;
  phone?: string;
  email?: string;
}

export interface DimensionInfo {
  area?: number;
  areaUnitId?: string;
  areaUnit?: any;
}

export interface TagInfo {
  isFeatured?: boolean;
}

export interface Amenity {
  masterPropertyAmenityId: string;
  amenityName: string;
  imageURL: string;
}

export interface PropertyType {
  noOfBHK?: number;
  bhkType?: string;
  bhkTypeId?: string;
  baseTypeId?: string;
  baseType?: string;
  subTypeId?: string;
  subType?: string;
}

export interface Address {
  placeId?: string;
  subLocality?: string;
  locality?: string;
  district?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  longitude?: string;
  latitude?: string;
  isGoogleMapLocation?: string;
}

export interface Attribute {
  masterPropertyAttributeId?: string;
  attributeName?: string;
  value?: number;
}

export interface Property {
  monetaryInfo?: MonetaryInfo;
  ownerDetails?: OwnerDetails;
  dimensionInfo?: DimensionInfo;
  dimension?: DimensionInfo;
  tagInfo: TagInfo;
  propertyType?: PropertyType;
  address?: Address;
  imageUrls?: any[];
  attributes?: Attribute[];
  amenities?: any[];
  micrositeURL?: string;
  saleType?: number;
  enquiredFor?: number;
  notes?: string;
  furnishStatus?: number;
  status?: number;
  rating?: string;
  shareCount?: number;
  possessionDate?: any;
  facing: number;
  propertyTypeId?: string;
  locationId?: string;
  ownerDetailsId?: string;
  dimensionInfoId?: string;
  monetaryInfoId?: string;
  propertyTagInfoId: string;
  userId?: string;
  tenantId?: string;
  createdBy?: string;
  lastModifiedBy?: string;
  id?: string;
  created?: string;
  lastModified?: string;
  isDeleted?: boolean;
}
