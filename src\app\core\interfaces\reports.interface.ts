export interface ReportsFilter {
    toDateForSource: any;
    fromDateForSource: any;
    toDateForProject: any;
    fromDateForProject: any;
    toDateForSubSource: any;
    fromDateForSubSource: any;
    toDateForAgency: any;
    fromDateForAgency: any;
    generatingFrom: any;
    toDateForLeadReceived: any;
    fromDateForLeadReceived: any;
    ProspectSourceIds: string;
    SourceIds: string;
    callLogToDate: any;
    callLogFromDate: any;
    userStatus: any;
    fromDateForMeetingOrVisit: any;
    toDateForMeetingOrVisit: any;
    toDate: any;
    fromDate: any;
    dateType: any;
    pageNumber: number;
    pageSize: number;
    path: string;
    CreatedFrom: any;
    CreatedTo: any;
    UpdatedFrom: any;
    UpdatedTo: any;
    FromDate: any;
    ToDate: any;
    ScheduledFrom: any;
    ScheduledTo: any;
    IsWithTeam: boolean;
    UserIds: string;
    SearchText: string;
    Sources: string;
    SubSources: any;
    AgencyNames: any;
    Locations: any;
    Cities: any;
    States: any;
    Countries: any;
    Projects: string;
    IsDateFilter: any;
    ReportPermission: number;
    ExportPermission: number;
    shouldShowDataReport: boolean;
    ShouldShowAll?: boolean;
    ShouldShowPercentage: boolean;
    timeZoneId:any;
    baseUTcOffset:any;
}
