import { Priority } from 'src/app/app.enum';
import { Entity } from 'src/app/core/interfaces/common.interface';
import { TodoState } from 'src/app/reducers/todo/todo.reducer';

export interface Todo extends Entity {
  title: string;
  notes: string;
  priority: Priority;
  isMarkedDone: boolean;
  scheduledDateTime: string | null;
}

export interface TasksFilter {
  pageNumber: number;
  pageSize?: number;
  todoFilterType?: number;
  noOfRecords?: number;
  path: string;
  baseUTcOffset?: any;
  timeZoneId?: any
}

export interface TodoFiltersCount {
  today: number;
  upcoming: number;
  overdue: number;
  completed: number;
  all: number;
}
export interface FetchTasksResponse {
  items?: Todo[];
  itemsCount?: number;
  totalCount?: number;
  succeeded?: boolean;
  message?: any;
  errors?: any;
  data?: TodoState[];
}

export interface TodoPayload {
  title: string;
  notes: string;
  priority: number;
  assignedToUser: string;
  IsMarkedDone: boolean;
  scheduledDateTime: string | null;
  id?: number;
}

export interface AssignedTodoUser {
  id: string;
  displayName: string
}

export interface FilterApplied {
  todoFilterType: string;
  pageNumber: number;
  pageSize: number;
  timeZoneId?: any
  baseUTcOffset?: any;
}