import {
  ANDROID_APP_DOWNLOAD_LINK,
  WHATSAPP_SHARE_API,
} from 'src/app/app.constants';

export const platformShareLinks = (
  platform: string,
  data: string,
  name?: string,
  contactDetail?: string
) => {
  let linkType: string;
  let format = {
    greet: 'Hi',
    greetings: 'Hey',
    iam: "I'm",
    profession: 'a Real Estate Professional.',
    brand: 'Powered by',
    courtesy: 'Thanks',
    profileMessage:
      'I have properties listed on my Business Portfolio that I think you might be interested in. Click on the link down below to view your desired properties.',
    emailPropertyMessage:
      'We selected property that might be matching to your interest. Check it out now.',
    whatsAppPropertyMessage:
      'Visit my property(s) that might be of interest to you.',
    profileEmailSubject: 'Business portfolio',
    propertyEmailSubject: 'Property(s) for Rent/Sale',
    referFriend: 'Download',
  };
  // let info = data?.split('/');
  // let shareType = info ? info[info?.length - 1].length : '';
  // if (shareType == 10) {
  //   linkType = 'profile';
  // } else if (shareType == 36) {
  //   linkType = 'property';
  // } else if (shareType == 39) {
  //   linkType = 'refer-friend';
  // } else {
  //   linkType = 'chat';
  // }
  switch (platform) {
    case 'whatsApp':
      if (linkType == 'refer-friend') {
        return `${WHATSAPP_SHARE_API}?phone=${
          contactDetail ? contactDetail : ''
        }&text=${ANDROID_APP_DOWNLOAD_LINK}  %0A`;
      } else if (linkType == 'profile') {
        return `
          ${WHATSAPP_SHARE_API}?phone=${
          contactDetail ? contactDetail : ''
        }&text=${format.greet}, ${format.iam} ${name} ${format.profession} ${
          format.profileMessage
        } %0A ${data}%0A%0A ${format?.brand}  %0A`;
      } else if (linkType == 'property') {
        return `
          ${WHATSAPP_SHARE_API}?phone=${
          contactDetail ? contactDetail : ''
        }&text=${format.greetings}, ${
          format.emailPropertyMessage
        } %0A ${data} %0A ${format.brand}  %0A`;
      } else {
        // return `
        //   ${WHATSAPP_SHARE_API}${contactDetail ? contactDetail : ''}`;
        return `
          ${WHATSAPP_SHARE_API}?phone=${
          contactDetail ? contactDetail : ''
        }&text=${encodeURIComponent(data)}`;
      }
    case 'email':
      if (linkType == 'refer-friend') {
        return `mailto:${contactDetail ? contactDetail : ''}?subject= ${
          format.referFriend
        }&body= %0A  %0A ${ANDROID_APP_DOWNLOAD_LINK}  %0A`;
      } else if (linkType == 'profile') {
        return `
        mailto:${contactDetail ? contactDetail : ''}?subject= ${name} ${
          format.profileEmailSubject
        }&body=${format.greet},%0A%0A${format.iam} ${name}, ${
          format.profession
        } ${format.profileMessage} %0A%0A${data} %0A %0A${
          format.courtesy
        }, %0A${name} %0A${format.brand}%0A`;
      } else if (linkType == 'property') {
        return `
        mailto:${contactDetail ? contactDetail : ''}?subject=${
          format.propertyEmailSubject
        }&body=${format.greet}, %0A %0A${
          format.emailPropertyMessage
        } %0A${data} %0A %0A${format.courtesy}, %0A${name} %0A${
          format.brand
        }  %0A`;
      } else {
        // return `
        // mailto:${contactDetail ? contactDetail : ''}?subject=${
        //   format.greet
        // }&body=`;
        return `
        mailto:${contactDetail ? contactDetail : ''}?body=${encodeURIComponent(data)}`;
      }
    default:
      return '';
  }
};

export const constructPortfolioLink = () => {
  return '';
  // return `${env.micrositeURL}${(localStorage.getItem('phoneNumber') || '').substring(
  //   localStorage.getItem('phoneNumber').length - 10
  // )}`;
};
