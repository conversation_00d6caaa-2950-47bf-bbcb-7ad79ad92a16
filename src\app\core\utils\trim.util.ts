// Trim the multiple or trailing spaces for a given string
export const trimString = (str: any = ''): string =>
  !!str.length ? str.replace(/\s{2,}/g, ' ').trim() : str;

// Applies trimObjectSpaces to remove multiple or trailing spaces from the given array
export const trimArray = (arr: any[]): any => arr.map((e) => trimObjectSpaces(e));

// Applies trimObjectSpaces to remove multiple or trailing spaces from a given object
export const trimObject = (obj: any): any => {
  return Object.keys(obj).reduce((acc: any, element) => {
    acc[element] = trimObjectSpaces(obj[element]);
    return acc;
  }, {});
};

export const trimObjectSpaces = (arrOrObj: any): any => {
  if (arrOrObj) {
    if (typeof arrOrObj === 'undefined' || typeof arrOrObj === 'function') {
      return arrOrObj;
    } else if (typeof arrOrObj === 'string' || arrOrObj instanceof String) {
      return trimString(arrOrObj);
    } else if (arrOrObj instanceof Array) {
      return trimArray(arrOrObj);
    } else if (arrOrObj !== null && typeof arrOrObj === 'object') {
      return trimObject(arrOrObj);
    } else if (typeof arrOrObj === 'boolean' || Number.isInteger(arrOrObj)) {
      // If the given object is boolean or number, then return it as is
      return arrOrObj;
    } else {
      return trimString(arrOrObj);
    }
  }
  return arrOrObj;
};

export const getConvertedString = (str: string = ''): any => {
  return str.trim().toLowerCase().replace(/ /g, '-');
};
