import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AttendanceComponent } from './attendance.component';
import { attendanceRootLayoutComponent } from './attendance-root.component';
import { UserDetailsComponent } from './user-details/user-details.component';
import { ClockInOutComponent } from './clock-in-out/clock-in-out.component';


export const routes: Routes = [
  { path: '', component: AttendanceComponent, pathMatch: 'full' },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class attendanceRoutingModule { }

export const ATTENDANCE_DECLARATIONS = [
  attendanceRootLayoutComponent,
  AttendanceComponent,
  UserDetailsComponent,
  ClockInOutComponent
];