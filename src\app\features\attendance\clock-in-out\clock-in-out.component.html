<ng-container *ngIf="params.value[0]?.pastDate else futureDate">
    <ng-container *ngIf="clockData?.length else noEntry">
        <div class="bg-light-pearl w-110 cursor-pointer py-6" (click)="openLocationModal(location)">
            <div class="flex-between pl-10">
                <div>
                    <span class="align-center">
                        <span class="icon ic-clock-nine ic-accent-green cursor-pointer ic-xxs"></span>
                        <span class="text-accent-green fw-600 ml-4 text-xs">{{clockData?.[0].clockInTime ?
                            getTimeZoneDate(clockData?.[0].clockInTime, userData?.timeZoneInfo?.baseUTcOffset,
                            'timeWithMeridiem') : '--'}}</span>
                    </span>
                    <span class="align-center mt-4">
                        <span class="icon ic-clock-eight ic-red-350 cursor-pointer ic-xxs"></span>
                        <span class="text-red-350 fw-600 ml-4 text-xs">{{clockData?.[clockData.length - 1].clockOutTime
                            ? getTimeZoneDate(clockData?.[clockData.length - 1].clockOutTime,
                            userData?.timeZoneInfo?.baseUTcOffset,
                            'timeWithMeridiem') : '--'}}</span>
                    </span>
                </div>
                <div class="dot dot-lg bg-gray-dark ml-10">
                    <span class="icon ic-dark"
                        [ngClass]="clockData?.length > 1 ? 'ic-multi-location ic-sm' : 'ic-location-dot ic-xs'"></span>
                </div>
            </div>
            <div class="text-xs text-nowrap mt-4 pl-4" *ngIf="workingHours != '00:00:00'">Working Hours -
                {{workingHours.slice(0, 5)}}</div>
        </div>
    </ng-container>
    <ng-template #noEntry>
        <div class="px-36 py-24 text-black-200 bg-pink-700 text-xs">no entries</div>
    </ng-template>
    <ng-template #location>
        <div class="bg-white h-100vh tb-w-100-40">
            <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
                <h3 class="fw-semi-bold fv-sm-caps">Clock in/out location</h3>
                <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
            </div>
            <div class="d-flex">
                <div class="flex-column h-100-80 scrollbar">
                    <ng-container *ngFor="let entry of clockData; index as i">
                        <div *ngIf="i != 0" class="border-bottom mx-12 mt-12 bg-light-pearl"></div>
                        <div class="field-label fw-600 text-mud px-12">Clock {{i+1}}</div>
                        <div class="flex-between mt-12">
                            <div [title]="entry?.clockInLocation ? entry?.clockInLocation : 'Location not available'"
                                class="mx-12 p-8 cursor-pointer w-180 bg-light-pearl border-right border-0 br-6"
                                (click)="showLocation(entry?.clockInLatitude,entry?.clockInLongitude,entry?.clockInLocation, i+1)">
                                <div class="flex-between">
                                    <div class="flex-column">
                                        <h5 class="fw-600 text-mud">Clock in</h5>
                                        <div class="text-dark-gray text-xxs"><span class="fw-700">{{entry?.clockInTime ?
                                                getTimeZoneDate(entry.clockInTime,
                                                userData?.timeZoneInfo?.baseUTcOffset) : ''}}</span>
                                        </div>
                                    </div>
                                    <div class="icon ic-location-solid ic-accent-green ic-sm mr-4"></div>
                                </div>
                            </div>
                            <div [ngClass]="entry.clockInImageUrl ? 'cursor-pointer' : 'pe-none'"
                                (click)="openImage(entry.clockInImageUrl, imageView, 'Clock in', entry?.clockInTime)">
                                <img [appImage]="entry.clockInImageUrl ? s3BucketUrl+entry.clockInImageUrl : ''"
                                    alt="" [type]="'defaultAvatar'" class="br-50 obj-cover mr-12" width="30"
                                    height="30">
                            </div>
                        </div>
                        <div class="flex-between mt-12">
                            <div
                                [title]="entry?.clockOutLocation ? entry?.clockOutLocation : 'Location not available'"
                                class="mx-12 p-8 cursor-pointer w-180 bg-light-pearl border-right border-0 br-6"
                                (click)="showLocation(entry?.clockOutLatitude,entry?.clockOutLongitude,entry?.clockOutLocation, i+1)">
                                <div class="flex-between">
                                    <div class="flex-column">
                                        <h5 class="fw-600 text-mud">Clock out</h5>
                                        <div class="text-dark-gray text-xxs"><span class="fw-700">{{entry?.clockOutTime
                                                ? getTimeZoneDate(entry.clockOutTime,
                                                userData?.timeZoneInfo?.baseUTcOffset)
                                                : ''}}</span>
                                        </div>
                                    </div>
                                    <div class="icon ic-location-solid ic-accent-green ic-sm mr-4"></div>
                                </div>
                            </div>
                            <div [ngClass]="entry.clockOutImageUrl ? 'cursor-pointer' : 'pe-none'"
                                (click)="openImage(entry.clockOutImageUrl, imageView, 'Clock out', entry?.clockOutTime)">
                                <img [appImage]="entry.clockOutImageUrl ? s3BucketUrl+entry.clockOutImageUrl : ''"
                                    alt="" [type]="'defaultAvatar'" class="br-50 obj-cover mr-12" width="30"
                                    height="30">
                            </div>
                        </div>
                    </ng-container>
                </div>
                <div class="w-80pr responsive-map">
                    <google-map [center]="{lat: center?.lat, lng: center?.lng}">
                        <map-marker #mapMarker="mapMarker" (mapClick)="openInfoWindow(mapMarker)"
                            *ngFor="let marker of markers" [position]="{lat:marker?.latitude, lng:marker?.longitude}"
                            [label]="marker?.label">
                        </map-marker>
                        <map-info-window>{{selectedAddress}}</map-info-window>
                    </google-map>
                </div>
            </div>
        </div>
    </ng-template>
</ng-container>
<ng-template #futureDate>
    <div class="bg-blue-900 pt-9 pb-10 px-53">--</div>
</ng-template>
<ng-template #imageView>
    <div class="flex-center position-relative">
        <img [appImage]="currentImage ? s3BucketUrl+currentImage : ''" [type]="'defaultAvatar'" alt="image"
            class="w-300 position-relative h-300" [style.transform]="'rotate(' + rotationAngle + 'deg)'">
        <div class="w-100 bg-blur position-absolute flex-center">
            <img [appImage]="currentImage ? s3BucketUrl+currentImage : ''" [type]="'defaultAvatar'" alt="image"
                class="max-w-300 h-300" [style.transform]="'rotate(' + rotationAngle + 'deg)'"
                [style.scale]="currentZoom">
        </div>
        <!-- <div class="position-absolute top-0 right-4 ph-d-none">
            <div (click)="rotateImage(90)" class="dot dot-lg bg-accent-green shadow cursor-pointer mt-4">
                <span class="icon ic-xxxs ic-refresh"></span>
            </div>
            <div (click)="zoomImage(1.2)" class="dot dot-lg bg-accent-green shadow cursor-pointer mt-4">
                <span class="icon ic-xxxs ic-plus"></span>
            </div>
            <div (click)="zoomImage(0.8)" class="dot dot-lg bg-accent-green shadow cursor-pointer mt-4">
                <span class="icon ic-xxxs ic-minus"></span>
            </div>
            <div (click)="currentZoom = 1" class="dot dot-lg bg-accent-green shadow cursor-pointer mt-4">
                <span class="icon ic-xxxs ic-expand"></span>
            </div>
            <div (click)="downloadImage()" class="dot dot-lg bg-accent-green shadow cursor-pointer mt-4">
                <span class="icon ic-xxxs ic-download"></span>
            </div>
        </div> -->
    </div>
</ng-template>