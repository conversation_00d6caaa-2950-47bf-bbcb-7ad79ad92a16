import { Component, EventEmitter, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MapInfoWindow } from '@angular/google-maps';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getTimeZoneDate } from 'src/app/core/utils/common.util';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'clock-in-out',
  templateUrl: './clock-in-out.component.html',
})
export class ClockInOutComponent implements OnInit {
  @ViewChild(MapInfoWindow) infoWindow: MapInfoWindow;
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  params: any;
  clockData: any;
  moment = moment;
  markers: any[] = [];
  selectedAddress: string;
  center = {
    lat: 12.9106262,
    lng: 77.6405173,
  };
  latitude: number = 12.8898194;
  longitude: number = 77.64237;
  label = 1;
  s3BucketUrl: string = env.s3ImageBucketURL;
  currentImage: string;
  workingHours: any;
  title: string;
  viewTime: string;
  rotationAngle: number = 0;
  currentZoom = 1;
  userData: any;
  getTimeZoneDate = getTimeZoneDate;
  constructor(
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private _store: Store<AppState>
  ) { }

  ngOnInit(): void {
    const entry = this.clockData?.[0];
    this.showLocation(entry?.clockInLatitude, entry?.clockInLongitude, entry?.clockInLocation, 1);
  }

  agInit(params: any): void {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.params = params;
        this.clockData = this.params.value[0]?.data?.logByDayDtos.filter((item: any) => {
          const date = getTimeZoneDate(item.day, this.userData?.timeZoneInfo?.baseUTcOffset, 'ISO');
          return date === this.params.value[0]?.date;
        })?.[0]?.logDtos;

        if (this.clockData) {
          this.clockData = [...this.clockData]?.reverse();
        }

        this.workingHours = this.params.value[0]?.data?.logByDayDtos.filter(
          (item: any) => getTimeZoneDate(item.day, this.userData?.timeZoneInfo?.baseUTcOffset, 'ISO') === this.params.value[0]?.date
        )?.[0]?.workingHours;
      });
  }

  openLocationModal(locationModal: any) {
    this.modalRef = this.modalService.show(locationModal, {
      class: 'right-modal modal-650 ip-modal-unset',
    });
  }

  openInfoWindow(marker: any) {
    this.infoWindow.open(marker);
  }

  showLocation(lat: any, long: any, loc: any, label: any) {
    this.markers[label - 1] = {
      latitude: Number(lat),
      longitude: Number(long),
      label: String(label),
      address: loc,
    };
    this.selectedAddress = this.markers[label - 1]?.address;
    this.center = {
      lat: Number(lat),
      lng: Number(long),
    };
  }

  openImage(image: any, modal: TemplateRef<any>, title: string, time: string) {
    this.currentImage = image;
    this.title = title;
    this.viewTime = time;
    this.modalRef = this.modalService.show(modal, {
      class: 'modal-350 modal-dialog-centered ip-modal-unset',
      keyboard: false,
    });
  }

  rotateImage(angle: number) {
    this.rotationAngle += angle;
  }

  zoomImage(scale: number) {
    this.currentZoom *= scale;
  }

  downloadImage() {
    let image = this.s3BucketUrl + this.currentImage;
    const link = document.createElement('a');
    link.href = image;
    link.download = 'image';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
