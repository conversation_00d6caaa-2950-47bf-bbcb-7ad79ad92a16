<div [ngClass]="{'pe-none' : !canViewUser && !isLoggedInUser}" class="py-10 px-20 align-center cursor-pointer"
    (click)="navigateToUserDetails()">
    <div>
        <img [appImage]="params.value[0]?.imageUrl ? s3BucketUrl+params.value[0]?.imageUrl : ''"
            [type]="'defaultAvatar'" class="br-50 obj-cover" width="30" height="30">
    </div>
    <div class="ml-10">
        <div class="fw-600 text-large text-coal cursor-pointer text-truncate-1 break-all" [title]="params.value[0].userName">
            {{params.value[0].userName}}</div>
        <div class="text-sm fw-semi-bold text-dark-gray text-truncate-1 break-all">{{params.value[0].designation}}</div>
        <div class="text-sm fw-semi-bold text-dark-gray text-truncate-1 break-all">{{params.value[0].department}}</div>
    </div>
</div>