import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { AppState } from 'src/app/app.reducer';
import { getViewPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'user-details',
  templateUrl: './user-details.component.html',
})
export class UserDetailsComponent implements ICellRendererAngularComp,OnInit {
  params: any;
  s3BucketUrl: string = env.s3ImageBucketURL;
  canViewUser: boolean;
  isLoggedInUser: boolean;

  constructor(public router: Router, public store: Store<AppState>) { }

  ngOnInit(): void {
    this.store.select(getViewPermissions).subscribe((canView: any) => {
      if (canView?.includes('Users')) this.canViewUser = true;
    });
    let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    if (userId == this.params.value[0]?.userId) {
      this.isLoggedInUser = true;
    }
  }

  agInit(params: any): void {
    this.params = params;
  }

  refresh(): boolean {
    return false;
  }

  navigateToUserDetails() {
    this.router.navigate(['teams/user-details', this.params.value[0]?.userId]);
  }
}
