import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { DashboardV2Component } from './dashboard-v2/dashboard-v2.component';
import { DashboardV3Component } from './dashboard-v3/dashboard-v3.component';
import { LeadsReportComponent } from './dashboard-v3/leads-report/leads-report.component';
import { CallsReportComponent } from './dashboard-v3/calls-report/calls-report.component';
import { DataReportComponent } from './dashboard-v3/data-report/data-report.component';
import { WhatsappReportComponent } from './dashboard-v3/whatsapp-report/whatsapp-report.component';
import { CplTrackingComponent } from './cpl-tracking/cpl-tracking.component';

const routes: Routes = [
  // { path: '', redirectTo: 'stats', pathMatch: 'full' },
  { path: 'stats', component: DashboardV2Component },
  { path: 'metrics', component: DashboardV3Component },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DashboardRoutingModule { }

export const DASHBOARD_DECLARATIONS = [
  DashboardV2Component,
  DashboardV3Component,
  LeadsReportComponent,
  DataReportComponent,
  CallsReportComponent,
  WhatsappReportComponent,
  CplTrackingComponent
];
