import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit
} from '@angular/core';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';

import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getAssignedToDetails,
  getPages,
  getTimeZoneDate,
} from 'src/app/core/utils/common.util';
import {
  FetchDashboardCalls,
  UpdateCallsFilterPayload,
} from 'src/app/reducers/dashboard/dashboard.actions';
import {
  getCalls,
  getCallsIsLoading,
  getFiltersPayloadV1,
} from 'src/app/reducers/dashboard/dashboard.reducers';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';

@Component({
  selector: 'calls-report',
  templateUrl: './calls-report.component.html',
})
export class CallsReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() usersByDesignation: Array<any>;
  isCallLoading: boolean;
  public pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  offset: number = 0;
  gridOptions: any;
  gridApi: any;
  searchTerm: string;
  gridColumnApi: any;
  rowData: any = [];
  defaultColDef: any;
  totalCount: number;
  getPages = getPages;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: this.pageSize,
  };
  selectedPageSize: number;
  userBasicDetails: any;
  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
  }

  ngOnInit() {
    this.selectedPageSize = 10;

    this.store
      .select(getCallsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCallLoading = isLoading;
      });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
      });

    this.store
      .select(getCalls)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.totalCount = data?.totalCount;
        this.rowData = data?.items;
        if (this.rowData?.length) {
          this.initializeGridSettings();
        }
      });

    this.store
      .select(getFiltersPayloadV1)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: any) => {
        this.filtersPayload = filters.global;
        if (this.rowData && this.rowData?.length === 0 && this.offset > 0) {
          this.filtersPayload = {
            ...this.filtersPayload,
            pageNumber: this.offset,
          };
          this.offset = this.offset - 1;
        }
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Lead Name',
        field: 'Lead Name',
        valueGetter: (params: any) => `${params.data.leadName || ''}`,
        cellRenderer: (params: any) => `<p>${params.value}</p>`,
      },
      {
        headerName: 'Primary No',
        field: 'Primary No',
        valueGetter: (params: any) => `${params.data.contactNo || ''}`,
        cellRenderer: (params: any) => `<p>${params.value}</p>`,
      },
      {
        headerName: 'Assigned',
        field: 'Assigned',
        valueGetter: (params: any) => `${params.data.userName || ''}`,
        cellRenderer: (params: any) => `<p>${params.value}</p>`,
      },
      {
        headerName: 'Call Status',
        field: 'Call Status',
        valueGetter: (params: any) => `${params.data.callStatus || ''}`,
        cellRenderer: (params: any) => `<p>${params.value}</p>`,
      },
      {
        headerName: 'Call Done',
        field: 'Call Done',
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.callDoneBy,
            this.usersByDesignation,
            true
          ) || '',
          params.data?.callDoneOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.callDoneOn,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 text-sm">${params.value[0]}</p>
            <p class="text-sm">${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userBasicDetails?.timeZoneInfo?.timeZoneName &&
              this.userBasicDetails?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userBasicDetails?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        minWidth: 180,
      },
      {
        headerName: 'Created',
        field: 'Created',
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.createdBy,
            this.usersByDesignation,
            true
          ) || '',
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 text-sm">${params.value[0]}</p>
            <p class="text-sm">${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userBasicDetails?.timeZoneInfo?.timeZoneName &&
              this.userBasicDetails?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userBasicDetails?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        minWidth: 180,
      },
      {
        headerName: 'Modified',
        field: 'Modified',
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.modifiedBy,
            this.usersByDesignation,
            true
          ) || '',
          params.data?.modifiedOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.modifiedOn,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 text-sm">${params.value[0]}</p>
            <p class="text-sm">${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userBasicDetails?.timeZoneInfo?.timeZoneName &&
              this.userBasicDetails?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userBasicDetails?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        minWidth: 180,
      },
      {
        headerName: 'Recording',
        field: 'Recording',
        valueGetter: (params: any) => params.data.callRecordingURL,
        cellRenderer: (params: any) => {
          if (params.value) {
            return `<audio preload="auto" controls class="py-4 mt-16">
                      <source src="${params.value}" type="audio/mp3">
                      Your browser does not support the audio element.
                    </audio>`;
          } else {
            return 'No Call Recordings';
          }
        },
        minWidth: 350,
      },
    ];
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridOptions.api = params.api;
    params.api.sizeColumnsToFit();
  }

  onPageChange(e: any) {
    this.offset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.pageSize,
      PageNumber: e + 1,
    };
    this.store.dispatch(new UpdateCallsFilterPayload(this.filtersPayload));
    this.store.dispatch(new FetchDashboardCalls());
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.store.dispatch(new UpdateCallsFilterPayload(this.filtersPayload));
    this.store.dispatch(new FetchDashboardCalls());
    this.gridOptions.paginationPageSize = this.pageSize;
    this.gridOptions.api?.paginationSetPageSize(this.selectedPageSize);
    this.gridApi.setRowData([]);
    this.gridApi.applyTransaction({ add: this.rowData });
    this.offset = 0;
  }

  search(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      if (!this.searchTerm || this.searchTerm.trim() === '') {
        this.gridApi.setQuickFilter(null);
        return
      }
      this.filtersPayload = {
        ...this.filtersPayload,
        pageNumber: 1,
        SearchText: this.searchTerm,
      };
      this.store.dispatch(new UpdateCallsFilterPayload(this.filtersPayload));
      this.store.dispatch(new FetchDashboardCalls());
      this.offset = 0;
    }
  }

  clearSearch() {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.gridApi.setQuickFilter(null);
      this.filtersPayload = {
        ...this.filtersPayload,
        SearchText: this.searchTerm,
      };
      this.store.dispatch(new UpdateCallsFilterPayload(this.filtersPayload));
      this.store.dispatch(new FetchDashboardCalls());
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
