import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { firstValueFrom, Observable, skipWhile, takeUntil } from 'rxjs';

import {
  DASHBOARD_FILTERS_KEY_LABEL,
  DASHBOARD_VISIBILITY,
  DATE_TYPE,
  EMPTY_GUID,
  LEAD_VISIBILITY_IMAGE,
  PAGE_SIZE,
} from 'src/app/app.constants';
import { LeadDateType, LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate
} from 'src/app/core/utils/common.util';
import {
  FetchCPLTracking,
  FetchCustomStatusCount,
  FetchDashboardCalls,
  FetchDashboardDataStatus,
  FetchDashboardLeadStatus,
  FetchDashboardSource,
  FetchDashboardWhatsapp,
  FetchLeadStatusTotalCount,
  UpdateCallsFilterPayload,
  UpdateCPLFilterPayload,
  UpdateDataStatusFilterPayload,
  UpdateGlobalFilterPayload,
  UpdateLeadStatusFilterPayload,
  UpdateWhatsappFilterPayload,
} from 'src/app/reducers/dashboard/dashboard.actions';
import {
  getCustomStatusCount,
  getCustomStatusCountIsLoading,
  getFiltersPayloadV1,
  getSourceV1,
  getSourceV1IsLoading,
} from 'src/app/reducers/dashboard/dashboard.reducers';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchFbAccount } from 'src/app/reducers/Integration/integration.actions';
import { getFbAccount } from 'src/app/reducers/Integration/integration.reducer';
import { FetchProjectList } from 'src/app/reducers/lead/lead.actions';
import { getProjectList } from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchGeneralManagerList,
  FetchUsersByDesignation,
} from 'src/app/reducers/teams/teams.actions';
import {
  getGeneralManagerList,
  getUserBasicDetails,
  getUsersByDesignation,
} from 'src/app/reducers/teams/teams.reducer';
import { TourService } from 'src/app/services/shared/appTour.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'dashboard-v3',
  templateUrl: './dashboard-v3.component.html',
})
export class DashboardV3Component implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  s3BucketUrl: string = env.s3ImageBucketURL;
  globalForm: FormGroup;

  leadsCountByStatus: any;
  totalCount: number;

  dashboardTypes = DASHBOARD_VISIBILITY.slice(0, 1);
  dateTypeFilterList = DATE_TYPE.slice(0, 4);
  projectList: Array<any>;
  othersSource: any;
  socialProfileSource: any;
  thirdPartySource: any;
  public pageSize: number = PAGE_SIZE;

  filtersPayload: any;
  LeadSource = LeadSource;
  showLeftNav: boolean = true;
  customStatusCount: any;
  allStatusCount: any;
  usersByDesignation: any;
  generalManagerList: any;
  designationList: any;
  filteredUsers: any;
  showFilters: boolean = false;
  screen = window;

  isStatusCountLoading: boolean;
  isSourceLoading: boolean;
  isAssociatedData: boolean = false;
  searchTermWA: string;
  moment = moment;
  EMPTY_GUID = EMPTY_GUID;
  showAppliedFilters: boolean;
  filtersKeyLabel = DASHBOARD_FILTERS_KEY_LABEL;
  hasOthersData: any;
  hasSPData: any;
  hasTPData: any;
  hasAssociatedData: any;
  leadsFiltersPayload: any;
  dataFiltersPayload: any;
  callFiltersPayload: any;
  cplFilterPayload: any;
  whatsappFiltersPayload: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened
  userBasicDetails: any;
  permissionsSet: Set<unknown>;
  accountDetails: any;
  globalsettings: any;

  constructor(
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private shareDataService: ShareDataService,
    private store: Store<AppState>,
    private router: Router,
    private fb: FormBuilder,
    private tourService: TourService
  ) {
    this.globalForm = this.fb.group({
      dashboardVisibility: [null],
      generalManager: [null],
      withTeamGM: [null],
      designation: [null],
      userIds: [null],
      withTeamUsers: [null],
      projects: [null],

      globalDType: [null],
      globalDate: [null],
    });

    this.metaTitle.setTitle('CRM | Dashboard');
    this.headerTitle.setTitle('DashboardV3');
    this.store.dispatch(new FetchProjectList());
    this.store.dispatch(new FetchUsersByDesignation());
    this.store.dispatch(new FetchGeneralManagerList());

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
  }

  async ngOnInit(): Promise<void> {
    this.globalsettings = await firstValueFrom(this.store.select(getGlobalSettingsAnonymous).pipe(skipWhile((data: any) => !Object.keys(data).length)));    
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.permissionsSet = new Set(permissions);
        if (this.leadsCountByStatus?.totalLeads)
          this.totalCount =
            this.leadsCountByStatus?.totalLeads -
            this.leadsCountByStatus?.unassignedLeads -
            this.leadsCountByStatus?.deletedLeads;
        if (this.permissionsSet?.has('Permissions.Leads.ViewUnAssignedLead')) {
          if (this.leadsCountByStatus?.totalLeads)
            this.totalCount += this.leadsCountByStatus?.unassignedLeads;
        }
        if (this.permissionsSet?.has('Permissions.Leads.Delete')) {
          if (this.leadsCountByStatus?.totalLeads)
            this.totalCount += this.leadsCountByStatus?.deletedLeads;
        }

        if (
          this.permissionsSet?.has('Permissions.Dashboard.ViewTeam') &&
          !this.dashboardTypes.includes(DASHBOARD_VISIBILITY[1])
        ) {
          this.dashboardTypes?.push(DASHBOARD_VISIBILITY[1]);
        }
        if (
          this.permissionsSet?.has('Permissions.Dashboard.ViewOrg') &&
          !this.dashboardTypes.includes(DASHBOARD_VISIBILITY[2])
        ) {
          this.dashboardTypes?.push(DASHBOARD_VISIBILITY[2]);
        }
        if (this.permissionsSet?.has('Permissions.Leads.ViewLeadSource')) {
          this.store.dispatch(new FetchDashboardSource());
        }
      });

    this.store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a?.name?.localeCompare(b?.name));
      });

    this.store
      .select(getSourceV1)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.othersSource = data?.filter(
          (item: any) => item.type === 0 && ![8, 14].includes(item.leadSource)
        ); //Gharoffice, Portfolio
        this.socialProfileSource = data?.filter((item: any) => item.type === 1);
        this.thirdPartySource = data?.filter(
          (item: any) => item.type === 2 && ![18].includes(item.leadSource)
        ); //SquareYards
        this.updateDataFlags();
      });

    this.store
      .select(getCustomStatusCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {        
        this.customStatusCount = data;
        this.customStatusCount = Object.entries(data)
          .filter(([key, value]) => key !== '' && key !== 'All')
          .map(([key, value]) =>{
            return {
              key: this.globalsettings?.shouldRenameSiteVisitColumn
                ? {
                    SiteVisitDone: 'ReferralTaken',
                    SiteVisitNotDone: 'ReferralNotTaken',
                  }[key] || key
                : key,
              value,
            };
          });
        
        // .slice()
        // .sort((a: any, b: any) => a?.key.localeCompare(b?.key));
        this.allStatusCount = Object.entries(data)
          .filter(([key, value]) => key === 'All')
          .map(([key, value]) => value)[0];
      });

    this.store
      .select(getUsersByDesignation)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.designationList = Object.entries(data)
          .map(([key, value]: [string, any]) => ({
            id: value[0]?.designation?.id,
            name: key,
          }))
          .filter((item) => item.name !== 'No Designation')
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
        this.usersByDesignation = Object.values(data)
          ?.flat()
          ?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          })
          .slice()
          .sort((a: any, b: any) => a?.fullName.localeCompare(b?.fullName));
        this.filteredUsers = assignToSort(this.usersByDesignation, '');
      });

    this.globalForm.controls['designation'].valueChanges.subscribe(
      (selectedDesignations) => {
        if (selectedDesignations && selectedDesignations?.length > 0) {
          this.filteredUsers = this.usersByDesignation.filter((user: any) =>
            selectedDesignations.includes(user?.designation?.id)
          );
        } else {
          this.filteredUsers = assignToSort(this.usersByDesignation, '');
        }
      }
    );

    this.store
      .select(getGeneralManagerList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.generalManagerList = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.generalManagerList = assignToSort(this.generalManagerList, '');
      });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)
      });

    this.store.dispatch(new FetchFbAccount());
        this.store
          .select(getFbAccount)
          .pipe(takeUntil(this.stopper))
          .subscribe((response: any) => {
            this.accountDetails = response?.filter((account: any) => account?.ads?.length)
            .map((account: any) => ({
              accountId: account.accountId,
              facebookAccountName: account.facebookAccountName
            })) || [];
          });

    this.store
      .select(getFiltersPayloadV1)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: any) => {
        this.filtersPayload = { ...this.filtersPayload, ...filters.global };
        this.leadsFiltersPayload = filters.lead;
        this.dataFiltersPayload = filters.data;
        this.callFiltersPayload = filters.call;
        this.whatsappFiltersPayload = filters.whatsapp;
        this.cplFilterPayload = filters.cpl
      });
    this.globalForm.patchValue({
      dashboardVisibility: this.filtersPayload?.LeadVisibility,
      generalManager: this.filtersPayload?.GeneralManagerIds,
      withTeamGM: this.filtersPayload?.IsGenManagerWithTeam,
      designation: this.filtersPayload?.Designation,
      userIds: this.filtersPayload?.UserIds,
      withTeamUsers: this.filtersPayload?.IsWithTeam,
      projects: this.filtersPayload?.Projects,

      globalDType: LeadDateType[Number(this.filtersPayload?.DateType)] || 'All',
      globalDate: this.filtersPayload?.FromDate
        ? [patchTimeZoneDate(this.filtersPayload?.FromDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset), patchTimeZoneDate(this.filtersPayload?.ToDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)]
        : null,
    });
    this.loadersInfo();
    this.applyFilterFunc();
  }

  updateDataFlags() {
    // Recalculate flags for associated data
    this.hasOthersData = this.othersSource?.some(
      (profile: any) =>
        !this.isAssociatedData || (this.isAssociatedData && profile.count > 0)
    );
    this.hasSPData = this.socialProfileSource?.some(
      (profile: any) =>
        !this.isAssociatedData || (this.isAssociatedData && profile.count > 0)
    );
    this.hasTPData = this.thirdPartySource?.some(
      (profile: any) =>
        !this.isAssociatedData || (this.isAssociatedData && profile.count > 0)
    );

    this.hasAssociatedData =
      this.hasOthersData || this.hasSPData || this.hasTPData;
  }

  loadersInfo() {
    this.store
      .select(getCustomStatusCountIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isStatusCountLoading = isLoading;
      });

    this.store
      .select(getSourceV1IsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSourceLoading = isLoading;
      });
  }

  resetDateFilter() {
    this.globalForm.patchValue({
      globalDate: null,
    });
    this.applyFilterFunc();
  }

  onResetAllFilters() {
    const leadVisibilityValue = this.globalForm.value.dashboardVisibility;
    this.globalForm.reset();
    this.globalForm.patchValue({
      dashboardVisibility: leadVisibilityValue,
      globalDType: 'All',
    });
    this.applyFilterFunc();
  }

  applyFilterFunc() {
    this.showFilters = false;

    this.filtersPayload = {
      ...this.filtersPayload,
      LeadVisibility: this.globalForm.value.dashboardVisibility,
      GeneralManagerIds: this.globalForm.value.generalManager,
      IsGenManagerWithTeam: this.globalForm.value.withTeamGM,
      Designation: this.globalForm.value.designation,
      UserIds: this.globalForm.value.userIds,
      IsWithTeam: this.globalForm.value.withTeamUsers,
      Projects: this.globalForm.value.projects,
      DateType: LeadDateType[this.globalForm.value.globalDType],
      FromDate: setTimeZoneDate(this.globalForm.value.globalDate?.[0], this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
      ToDate: setTimeZoneDate(this.globalForm.value.globalDate?.[1], this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
    };

    if (
      this.filtersPayload?.GeneralManagerIds?.length ||
      this.filtersPayload?.Designation?.length ||
      this.filtersPayload?.UserIds?.length ||
      this.filtersPayload?.Projects?.length
    ) {
      this.showAppliedFilters = true;
    } else {
      this.showAppliedFilters = false;
    }
    this.store.dispatch(new UpdateGlobalFilterPayload(this.filtersPayload));
    this.store.dispatch(new FetchCustomStatusCount());
    if (this.permissionsSet?.has('Permissions.Leads.ViewLeadSource')) {
      this.store.dispatch(new FetchDashboardSource());
    }
    this.store.dispatch(
      new UpdateLeadStatusFilterPayload({
        ...this.leadsFiltersPayload,
        ...this.filtersPayload,

      })
    );
    this.store.dispatch(new FetchDashboardLeadStatus());
    this.store.dispatch(new FetchLeadStatusTotalCount());
    this.store.dispatch(
      new UpdateDataStatusFilterPayload({
        ...this.dataFiltersPayload,
        ...this.filtersPayload,
      })
    );
    this.store.dispatch(new FetchDashboardDataStatus());
    this.store.dispatch(
      new UpdateCallsFilterPayload({
        ...this.callFiltersPayload,
        ...this.filtersPayload,
      })
    );
    this.store.dispatch(new FetchDashboardCalls());
    this.store.dispatch(
      new UpdateWhatsappFilterPayload({
        ...this.whatsappFiltersPayload,
        ...this.filtersPayload,
      })
    );
    this.store.dispatch(new FetchDashboardWhatsapp());
    if (this.accountDetails?.length > 0) {
      this.store.dispatch(
        new UpdateCPLFilterPayload({
          ...this.cplFilterPayload,
          ...this.filtersPayload,
          acountId: this.accountDetails[0]?.accountId,
        })
      );
      this.store.dispatch(new FetchCPLTracking({
        ...this.cplFilterPayload,
        ...this.filtersPayload,
        acountId: this.accountDetails[0]?.accountId,
      }));
    }
  }

  getArrayOfFilters(key: string, values: string) {
    if (
      [
        'DateType',
        'FromDate',
        'ToDate',
        'LeadVisibility',
        'IsGenManagerWithTeam',
        'IsWithTeam',
        'PageSize',
        'PageNumber',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    return values?.toString()?.split(',');
  }

  getUserName(id: string) {
    let userName = '';
    this.usersByDesignation?.forEach((user: any) => {
      if (id === user.id) userName = `${user.firstName} ${user.lastName}`;
    });
    return userName;
  }

  getDesignation(id: string) {
    let designation = '';
    this.designationList?.forEach((item: any) => {
      if (item.id === id) designation = item.name;
    });
    return designation;
  }

  onRemoveFilter(key: string, value: string) {
    this.filtersPayload = { ...this.filtersPayload };
    this.filtersPayload[key] = [...this.filtersPayload[key]];
    this.filtersPayload[key] = this.filtersPayload[key]?.filter(
      (item: any) => item !== value
    );

    switch (key) {
      case 'GeneralManagerIds':
        this.globalForm.patchValue({
          generalManager: this.filtersPayload[key],
        });
        break;
      case 'Designation':
        this.globalForm.patchValue({ designation: this.filtersPayload[key] });
        break;
      case 'UserIds':
        this.globalForm.patchValue({ userIds: this.filtersPayload[key] });
        break;
      case 'Projects':
        this.globalForm.patchValue({ projects: this.filtersPayload[key] });
        break;
      default:
        break;
    }

    this.applyFilterFunc();
  }

  navigateStatus(status: any, source: any) {
    const visibility =
      LEAD_VISIBILITY_IMAGE[(this.globalForm.value.dashboardVisibility + 1) % 3]
        .name;
    const queryParams: any = {
      leadVisibility: visibility,
      customFirstLevelFilter: 'All Leads',
      StatusName: status,
      dtype: this.globalForm.value.globalDType,
      date: this.globalForm.value.globalDate?.[0]
        ? [setTimeZoneDate(this.globalForm.value.globalDate[0], this.userBasicDetails?.timeZoneInfo?.baseUTcOffset), setTimeZoneDate(this.globalForm.value.globalDate[1], this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)]
        : null,
      projects: this.globalForm.value.projects,
      assignTo: this.globalForm.value.userIds,
      withTeam: this.globalForm.value.withTeamUsers,
      designation: this.globalForm.value.designation,
      source: source || 0 ? [LeadSource[source]] : null,
    };

    switch (status) {
      case 'All':
        queryParams.customFirstLevelFilter = 'All Leads';
        break;
      case 'Active':
        queryParams.customFirstLevelFilter = 'Active Leads';
        break;
      case 'Overdue':
        queryParams.customFirstLevelFilter = 'Active Leads';
        break;
      case 'MeetingDone':
        queryParams.customFirstLevelFilter = 'All Leads';
        queryParams.doneStatus = ['Meeting Done'];
        break;
      case 'MeetingNotDone':
        queryParams.customFirstLevelFilter = 'All Leads';
        queryParams.doneStatus = ['Meeting Not Done'];
        break;
      case 'SiteVisitDone':
        queryParams.customFirstLevelFilter = 'All Leads';
        queryParams.doneStatus = ['Site Visit Done'];
        break;
      case 'SiteVisitNotDone':
        queryParams.customFirstLevelFilter = 'All Leads';
        queryParams.doneStatus = ['Site Visit Not Done'];
        break;
      default:
        break;
    }

    this.router.navigate(['leads/manage-leads'], {
      queryParams,
      state: { prevPage: this.router.url },
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}