import { Component, EventEmitter, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { DataDateType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { changeCalendar, getPages, setTimeZoneDate } from 'src/app/core/utils/common.util';
import {
  FetchDashboardDataStatus,
  UpdateDataStatusFilterPayload,
} from 'src/app/reducers/dashboard/dashboard.actions';
import {
  getDataStatus,
  getDataStatusIsLoading,
  getFiltersPayloadV1,
} from 'src/app/reducers/dashboard/dashboard.reducers';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';

@Component({
  selector: 'data-report',
  templateUrl: './data-report.component.html',
})
export class DataReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  dataStatusList: any;
  isDataStatusLoading: boolean;
  public PageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  offset: number = 0;
  gridOptions: any;
  gridApi: any;
  searchTerm: string;
  gridColumnApi: any;
  rowData: any = [];
  defaultColDef: any;
  totalCount: number;
  getPages = getPages;
  filtersPayload: any = {
    PageNumber: 1,
    PageSize: this.PageSize,
  };
  selectedPageSize: number;
  userData: any;
  currentDate: Date = new Date();
  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>,
    private router: Router
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
  }

  ngOnInit() {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });
    this.selectedPageSize = 10;

    this.store
      .select(getDataStatusIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isDataStatusLoading = isLoading;
      });

    this.store
      .select(getDataStatus)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.totalCount = data?.totalCount;
        this.rowData = data?.items?.map((row: any) => {
          let statuses: any[] = [];
          row?.data?.forEach((status: any) => {
            statuses.push({
              statusId: status.statusId,
              statusDisplayName: status.statusDisplayName,
              data: status.data || 0,
            });
          });
          return {
            ...row,
            data: statuses,
          };
        });

        let totalRow: any = {
          firstName: 'Total',
          lastName: '',
          data: [],
          all: 0,
          active: 0,
          convertedData: 0,
        };

        const statusCountMap: { [key: string]: number } = {};

        this.rowData?.forEach((row: any) => {
          row.data?.forEach((status: any) => {
            if (!statusCountMap[status.statusDisplayName]) {
              statusCountMap[status.statusDisplayName] = 0;
            }
            statusCountMap[status.statusDisplayName] += status.data;
          });

          totalRow.all += row.all || 0;
          totalRow.active += row.active || 0;
          totalRow.convertedData += row.convertedData || 0;
        });

        for (let key in statusCountMap) {
          totalRow.data.push({
            statusId: '00000000-0000-0000-0000-000000000000',
            statusDisplayName: key,
            data: statusCountMap[key],
          });
        }

        const originalDataLength = this.rowData?.length;
        if (originalDataLength === this.PageSize) {
          this.gridOptions.api?.setGridOption('paginationPageSize', this.PageSize + 1);
        }

        if (this.rowData?.length) {
          this.gridOptions.api?.setRowData(this.rowData);
          this.dataStatusList = [...data?.items];
          this.initializeGridSettings();
          this.generateDataStatus();
        }

        if (this.rowData?.length > 1) {
          this.rowData.push(totalRow);
          this.dataStatusList.push(totalRow);
          this.gridOptions.api?.setRowData(this.rowData);
        }
      });

    this.store
      .select(getFiltersPayloadV1)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: any) => {
        if (filters?.lead) {
          this.filtersPayload = filters.data;
          this.PageSize = this.filtersPayload.PageSize || PAGE_SIZE;
          this.selectedPageSize = this.PageSize;
          this.offset = Math.max((this.filtersPayload.PageNumber || 1) - 1, 0);

          if (this.gridOptions?.api) {
            this.gridOptions.api.paginationSetPageSize(this.PageSize + 1);
          }
        }
      });
  }

  generateDataStatus() {
    const statusSet = new Set<string>();
    this.rowData?.forEach((report: any) => {
      report.data?.forEach((status: any) => {
        if (status.statusDisplayName) {
          statusSet?.add(status.statusDisplayName);
        }
      });
    });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Agent Name',
        field: 'Agent Name',
        valueGetter: (params: any) => [
          `${params.data.firstName} ${params.data.lastName}`,
          `${params.data.designation ? params.data.designation : ''}`,
        ],
        cellRenderer: (
          params: any
        ) => `<p class="text-truncate-1 break-all fw-600">${params.value[0]}</p>
        <i class="text-truncate-1 break-all text-secondary text-xs mt-4">${params.value[1]}</i>`,
      },
      {
        headerName: 'All',
        field: 'All',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => {
          return [
            params.data?.all,
            params?.data?.userId,
          ];
        },
        cellRenderer: (params: any) => `<p>${params.value[0] || '--'}</p>`,
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey || event?.event?.metaKey;
          const params = { value: event?.value, data: event?.data };
          const filters: any = this.prepareManageDataFilterPayload(
            null,
            params?.value?.[1], false, 0
          );
          if (!event.data.userId) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab(filters);
              return;
            }
            const queryParams = {
              leadReportGetData: 'true',
              filtersPayload: encodeURIComponent(JSON.stringify(filters)),
            };

            this.router.navigate(['data/manage-data'], {
              queryParams: queryParams,
            });
          }
        },
      },
      // {
      //   headerName: 'Active',
      //   field: 'Active',
      //   cellClass: 'cursor-pointer',
      //   valueGetter: (params: any) => {
      //     return [
      //       params.data?.active,
      //       params?.data?.userId,
      //     ];
      //   },
      //   cellRenderer: (params: any) => `<p>${params.value[0] || '--'}</p>`,
      //   onCellClicked: (event: any) => {
      //     const isCtrlClick = event?.event?.ctrlKey || event?.event?.metaKey;
      //     const params = { value: event?.value, data: event?.data };
      //     const filters: any = this.prepareManageDataFilterPayload(
      //       null,
      //       params?.value?.[1], false, 0, 1
      //     );
      //     if (!event.data.userId) {
      //       return;
      //     } else if (event.value[0] != 0) {
      //       if (isCtrlClick) {
      //         this.getDataInNewTab(filters);
      //         return;
      //       }
      //       const queryParams = {
      //         leadReportGetData: 'true',
      //         filtersPayload: encodeURIComponent(JSON.stringify(filters)),
      //       };

      //       this.router.navigate(['data/manage-data'], {
      //         queryParams: queryParams,
      //       });
      //     }
      //   },
      // },
      {
        headerName: 'Converted',
        field: 'Converted',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => {
          return [
            params.data?.convertedData,
            params?.data?.userId,
          ];
        },
        cellRenderer: (params: any) => `<p>${params.value[0] || '--'}</p>`,
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey || event?.event?.metaKey;
          const params = { value: event?.value, data: event?.data };
          const filters: any = this.prepareManageDataFilterPayload(
            null,
            params?.value?.[1]
          );
          if (!event.data.userId) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab(filters);
              return;
            }
            const queryParams = {
              leadReportGetData: 'true',
              filtersPayload: encodeURIComponent(JSON.stringify(filters)),
            };

            this.router.navigate(['data/manage-data'], {
              queryParams: queryParams,
            });
          }
        },
      },
    ];

    const allStatuses: Set<string> = new Set();
    if (this.dataStatusList?.length) {
      this.dataStatusList?.forEach((agent: { data: any[] }) => {
        agent.data?.forEach((statusItem: { statusDisplayName: string }) => {
          allStatuses.add(statusItem.statusDisplayName);
        });
      });
    }

    allStatuses.forEach((statusDisplayName: string) => {
      let col = {
        headerName: statusDisplayName || 'Unknown Status',
        field: statusDisplayName || 'unknownStatus',
        filter: false,
        hide: false,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => {
          const statusItem = params.data.data?.find(
            (status: any) => status.statusDisplayName === statusDisplayName
          );
          return [statusItem, params?.data?.userId];
        },
        cellRenderer: (params: any) => `<p>${params.value[0]?.data || '--'}</p>`,
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey || event?.event?.metaKey;
          const params = { value: event?.value, data: event?.data };
          const filters: any = this.prepareManageDataFilterPayload(
            params.value[0]?.statusId,
            params?.value?.[1],
            statusDisplayName == 'Qualified' ? true : false
          );
          if (!event.data.userId) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab(filters);
              return;
            }
            const queryParams = {
              leadReportGetData: 'true',
              filtersPayload: encodeURIComponent(JSON.stringify(filters)),
            };

            this.router.navigate(['data/manage-data'], {
              queryParams: queryParams,
            });
          }
        },
      };
      this.gridOptions.columnDefs.push(col);
    });

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridOptions.api = params.api;
    params.api.sizeColumnsToFit();
    if (this.PageSize) {
      params.api.paginationSetPageSize(this.PageSize + 1);
    }
  }

  prepareManageDataFilterPayload(
    statusId: string,
    userId: string,
    isQualified: boolean = false,
    visibility?: number,
    firstFilter: number = 0
  ) {
    let dataFiltersPayload: any = {
      AssignTo: [userId],
      IsWithTeam: false,
      Projects: this.filtersPayload.Projects,
      DateType: DataDateType[this.filtersPayload.DateType],
      FilterType: 0,
      FirstLevelFilter: firstFilter,
      ProspectVisiblity: visibility == 0 ? visibility : statusId && !isQualified ? 0 : 3,
      ProspectSearch: null,
      isNavigatedFromDashboard: true
    };

    dataFiltersPayload.StatusIds = statusId ? [statusId] : null;

    if (this.filtersPayload.FromDate) {
      dataFiltersPayload.FromDate = setTimeZoneDate(this.filtersPayload.FromDate, this.userData?.timeZoneInfo?.baseUTcOffset);
      dataFiltersPayload.ToDate = setTimeZoneDate(this.filtersPayload.ToDate, this.userData?.timeZoneInfo?.baseUTcOffset);
    } else {
      dataFiltersPayload.FromDate = null;
      dataFiltersPayload.ToDate = null;
    }

    return dataFiltersPayload;
  }

  getDataInNewTab(filters: any) {
    window?.open(
      `data/manage-data?leadReportGetData=true&filtersPayload=${encodeURIComponent(
        JSON.stringify(filters)
      )}`,
      '_blank'
    );
  }

  getDataFromCell(operation: string, event: any) {
    this.router.navigate(['data/manage-data']);
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType =
      DataDateType[this.filtersPayload.DateType];
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) filters.IsWithTeam = true;
    this.gridOptionsService.payload = filters;
  }

  onPageChange(e: any) {
    this.offset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: e + 1,
    };
    this.store.dispatch(new UpdateDataStatusFilterPayload(this.filtersPayload));
    this.store.dispatch(new FetchDashboardDataStatus());
  }

  assignCount() {
    this.PageSize = this.selectedPageSize;

    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: 1,
    };
    this.store.dispatch(new UpdateDataStatusFilterPayload(this.filtersPayload));
    this.store.dispatch(new FetchDashboardDataStatus());
    this.gridOptions.paginationPageSize = this.PageSize;
    if (this.gridOptions?.api) {
      this.gridOptions.api.paginationSetPageSize(this.PageSize + 1);
    }
    // this.gridOptions.api.paginationSetPageSize(this.selectedPageSize);
    // this.gridApi.setRowData([]);
    // this.gridApi.applyTransaction({ add: this.rowData });
    this.offset = 0;
  }

  search(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      if (!this.searchTerm || this.searchTerm.trim() === '') {
        this.gridApi.setQuickFilter(null);
        return
      }
      this.filtersPayload = {
        ...this.filtersPayload,
        PageNumber: 1,
        SearchText: this.searchTerm,
      };
      this.store.dispatch(
        new UpdateDataStatusFilterPayload(this.filtersPayload)
      );
      this.store.dispatch(new FetchDashboardDataStatus());
      this.offset = 0;
    }
  }

  clearSearch() {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.gridApi.setQuickFilter(null);
      this.filtersPayload = {
        ...this.filtersPayload,
        SearchText: this.searchTerm,
      };
      this.store.dispatch(
        new UpdateDataStatusFilterPayload(this.filtersPayload)
      );
      this.store.dispatch(new FetchDashboardDataStatus());
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
