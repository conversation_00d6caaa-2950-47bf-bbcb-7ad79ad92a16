import { Component, EventEmitter, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { LeadDateType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getPages } from 'src/app/core/utils/common.util';
import {
  FetchDashboardLeadStatus,
  FetchLeadStatusTotalCount,
  UpdateLeadStatusFilterPayload,
} from 'src/app/reducers/dashboard/dashboard.actions';
import {
  getFiltersPayloadV1,
  getLeadsStatusIsLoading,
  getLeadStatus,
  getLeadStatusTotalCount,
} from 'src/app/reducers/dashboard/dashboard.reducers';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';

@Component({
  selector: 'leads-report',
  templateUrl: './leads-report.component.html',
})
export class LeadsReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  leadStatusList: any;
  isLeadStatusLoading: boolean;
  public PageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  offset: number = 0;
  gridOptions: any;
  gridApi: any;
  searchTerm: string;
  gridColumnApi: any;
  rowData: any = [];
  defaultColDef: any;
  totalCount: number;
  getPages = getPages;
  filtersPayload: any = {
    PageNumber: 1,
    PageSize: this.PageSize,
  };
  selectedPageSize: number;
  keysToDisplay: any = {
    MeetingDone: 'Meeting Done',
    MeetingNotDone: 'Meeting Not Done',
    SiteVisitDone: 'Site Visit Done',
    SiteVisitNotDone: 'Site Visit Not Done',
  };

  constructor(
    private gridOptionsService: GridOptionsService,
    private router: Router,
    private store: Store<AppState>
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
  }

  ngOnInit() {
    this.selectedPageSize = PAGE_SIZE;

    this.store
      .select(getLeadsStatusIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isLeadStatusLoading = isLoading;
      });

    this.store
      .select(getLeadStatusTotalCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.totalCount = data;
      });

    this.store
      .select(getLeadStatus)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!data?.length) {
          this.rowData = [];
          this.gridOptions.api?.setRowData([]);
          return;
        }

        this.rowData = data?.map((row: any) => {
          let statuses: any[] = [];
          row?.status?.forEach((status: any) => {
            statuses.push({
              statusId: status.statusId,
              baseStatusId: status.baseStatusId,
              statusDisplayName: status.statusDisplayName,
              baseStatusDisplayName: status.baseStatusDisplayName,
              count: status.count || '0',
              percentage: null as any,
              orderRank: null as any,
            });
          });
          return {
            ...row,
            userId: row.id,
            status: statuses,
          };
        });

        let totalRow: any = {
          firstName: 'Total',
          lastName: '',
          status: [],
          isTotal: true,
        };

        const statusCountMap: {
          [key: string]: { outside: number; inside: number };
        } = {};

        this.rowData?.forEach((row: any) => {
          row.status?.forEach((status: any) => {
            const statusDisplayName = status.statusDisplayName;
            let outsideCount = 0;
            let insideCount = 0;

            if (status.count.includes('(')) {
              const countParts = status.count.match(/^(\d+)\((\d+)\)$/);
              outsideCount = countParts ? parseInt(countParts[1], 10) : 0;
              insideCount = countParts ? parseInt(countParts[2], 10) : 0;
            } else {
              outsideCount = parseInt(status.count, 10) || 0;
            }

            if (!statusCountMap[statusDisplayName]) {
              statusCountMap[statusDisplayName] = { outside: 0, inside: 0 };
            }
            statusCountMap[statusDisplayName].outside += outsideCount;
            statusCountMap[statusDisplayName].inside += insideCount;
          });
        });

        for (let key in statusCountMap) {
          const totalOutside = statusCountMap[key].outside;
          const totalInside = statusCountMap[key].inside;
          const formattedCount =
            totalInside > 0
              ? `${totalOutside}(${totalInside})`
              : `${totalOutside}`;

          totalRow.status.push({
            statusId: '00000000-0000-0000-0000-000000000000',
            baseStatusId: null,
            statusDisplayName: key,
            baseStatusDisplayName: null,
            count: formattedCount,
            percentage: null as any,
            orderRank: null as any,
          });
        }

        const originalDataLength = this.rowData?.length;
        if (originalDataLength === this.PageSize) {
          this.gridOptions.api?.setGridOption('paginationPageSize', this.PageSize + 1);
        }

        if (this.rowData?.length > 1) {
          this.rowData.push(totalRow);
        }

        if (this.gridOptions.api) {
          this.gridOptions.api.setRowData(this.rowData);
        }

        this.leadStatusList = [...this.rowData];
        this.initializeGridSettings();
        this.generateLeadsStatus();
      });

    this.store
      .select(getFiltersPayloadV1)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: any) => {
        if (filters?.lead) {
          this.filtersPayload = filters.lead;
          this.PageSize = this.filtersPayload.PageSize || PAGE_SIZE;
          this.selectedPageSize = this.PageSize;
          this.offset = Math.max((this.filtersPayload.PageNumber || 1) - 1, 0);

          if (this.gridOptions?.api) {
            this.gridOptions.api.paginationSetPageSize(this.PageSize + 1);
          }
        }
      });

    this.gridOptions.isRowSelectable = (rowNode: any) => {
      return !rowNode.data.isTotal;
    };

    this.gridOptions.getRowStyle = (params: any) => {
      if (params.data?.isTotal) {
        return {
          'font-weight': 'bold',
          'background-color': '#f5f5f5'
        };
      }
      return null;
    };
  }

  generateLeadsStatus() {
    const statusSet = new Set<string>();
    this.rowData?.forEach((report: any) => {
      report.status?.forEach((status: any) => {
        if (status.statusDisplayName) {
          statusSet?.add(status.statusDisplayName);
        }
      });
    });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Agent Name',
        field: 'Agent Name',
        valueGetter: (params: any) => [
          `${params.data.firstName} ${params.data.lastName}`,
          `${params.data.designation ? params.data.designation : ''}`,
        ],
        cellRenderer: (params: any) => {
          if (params.data.isTotal) {
            return `<p class="text-truncate-1 break-all fw-600">${params.value[0]}</p>`;
          }
          return `<p class="text-truncate-1 break-all fw-600">${params.value[0]}</p>
          <i class="text-truncate-1 break-all text-secondary text-xs mt-4">${params.value[1]}</i>`;
        },
      },
    ];

    const allStatuses: Set<string> = new Set();

    if (this.leadStatusList?.length) {
      this.leadStatusList.forEach((agent: { status: any[] }) => {
        agent.status.forEach((statusItem: { statusDisplayName: string }) => {
          allStatuses.add(statusItem.statusDisplayName);
        });
      });
    }

    allStatuses.forEach((statusDisplayName: any) => {
      let col: any = {
        headerName: statusDisplayName,
        field: statusDisplayName,
        filter: false,
        hide: false,
        minWidth: 110,
        cellClass: (params: any) => {
          return params.data.isTotal ? '' : 'cursor-pointer';
        },
        valueGetter: (params: any) => {
          const statusItem = params.data.status?.find(
            (status: any) => status.statusDisplayName === statusDisplayName
          );
          return [
            statusItem ? statusItem.count : '--',
            params?.data?.firstName,
          ];
        },
        cellRenderer: (params: any) => {
          return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
        },
        onCellClicked: (event: any) => {
          if (event.data.isTotal) {
            return;
          }

          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };

          if (this.keysToDisplay.hasOwnProperty(statusDisplayName)) {
            const displayValue = this.keysToDisplay[statusDisplayName];
            if (event.value[0] != 0) {
              if (isCtrlClick) {
                this.getMeetingCountNewTab('All Leads', params, displayValue);
              } else {
                this.getMeetingCount('All Leads', event, displayValue);
              }
            }
          } else {
            if (event.value[0] != 0) {
              if (isCtrlClick) {
                this.getDataInNewTab(statusDisplayName, params);
              } else {
                this.getDataFromCell(statusDisplayName, event);
              }
            }
          }
        },
      };
      this.gridOptions?.columnDefs?.push(col);
    });

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridOptions.api = params.api;
    params.api.sizeColumnsToFit();
    if (this.PageSize) {
      params.api.paginationSetPageSize(this.PageSize + 1);
    }
  }

  getDataFromCell(operation: string, event: any) {
    if (operation === 'All') {
      operation = 'All Leads';
    } else if (operation === 'Active') {
      operation = 'Active Leads';
    }
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType =
      LeadDateType[this.filtersPayload.DateType];
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) filters.IsWithTeam = false;
    if (filters?.Designation) filters.Designation = null;
    this.gridOptionsService.payload = filters;
  }

  getDataInNewTab(operation: string, params: any) {
    if (operation === 'All') {
      operation = 'All Leads';
    } else if (operation === 'Active') {
      operation = 'Active Leads';
    }
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    window?.open(
      `leads/manage-leads?leadReportGetData=true&data=${encodeURIComponent(
        JSON.stringify(params?.data)
      )}&operation=${operation}&filtersPayload=${encodeURIComponent(
        JSON.stringify(filters)
      )}`,
      '_blank'
    );
  }

  getMeetingCount(operation: string, event: any, meetingStatus: string) {
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    this.router.navigate(['leads/manage-leads']);
    let visitMeeting: any = [];
    visitMeeting.push(meetingStatus);
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    this.gridOptionsService.payload = this.filtersPayload;
    this.gridOptionsService.meetingStatus = visitMeeting;
  }

  getMeetingCountNewTab(operation: string, params: any, meetingStatus: string) {
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    window?.open(
      `leads/manage-leads?leadReportGetMeetingCount=true&data=${encodeURIComponent(
        JSON.stringify(params?.data)
      )}&operation=${operation}&meetingStatus=${meetingStatus}&filtersPayload=${encodeURIComponent(
        JSON.stringify(filters)
      )}`,
      '_blank'
    );
  }

  onPageChange(e: any) {
    this.offset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: e + 1,
    };
    this.store.dispatch(new UpdateLeadStatusFilterPayload(this.filtersPayload));
    this.store.dispatch(new FetchDashboardLeadStatus());
  }

  assignCount() {
    this.PageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: 1,
    };
    this.store.dispatch(new UpdateLeadStatusFilterPayload(this.filtersPayload));
    this.store.dispatch(new FetchDashboardLeadStatus());
    if (this.gridOptions?.api) {
      this.gridOptions.api.paginationSetPageSize(this.PageSize + 1);
    }
    this.offset = 0;
  }

  search(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      if (!this.searchTerm || this.searchTerm.trim() === '') {
        this.gridApi.setQuickFilter(null);
        return
      }
      this.filtersPayload = {
        ...this.filtersPayload,
        PageNumber: 1,
        SearchText: this.searchTerm,
      };
      this.store.dispatch(new UpdateLeadStatusFilterPayload(this.filtersPayload));
      this.store.dispatch(new FetchDashboardLeadStatus());
      this.store.dispatch(new FetchLeadStatusTotalCount());
      this.offset = 0;
    }
  }

  clearSearch() {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.gridApi.setQuickFilter(null);
      this.filtersPayload = {
        ...this.filtersPayload,
        SearchText: this.searchTerm,
      };
      this.store.dispatch(new UpdateLeadStatusFilterPayload(this.filtersPayload));
      this.store.dispatch(new FetchDashboardLeadStatus());
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}