<div class="border-white mt-20 bg-white br-2 w-100 reports">
    <div class="p-10 flex-between border-bottom">
        <h5 class="text-black-200">WhatsApp Report</h5>
    </div>
    <div class="align-center bg-white w-100 px-10 no-validation border-bottom">
        <span class="search icon ic-search ic-sm ic-slate-90 mr-12"> </span>
        <input placeholder="type agent name" name="search" class="border-0 outline-0 w-100 py-12" autocomplete="off"
            [(ngModel)]="searchTerm" (keyup.enter)="search($event)" (input)="clearSearch()">
    </div>
    <ng-container *ngIf="!isWhatsappLoading else spinLoader">
        <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize"
            [gridOptions]="gridOptions" [rowData]="rowData" [alwaysShowHorizontalScroll]="true"
            [alwaysShowVerticalScroll]="true" [suppressPaginationPanel]="true"
            (gridReady)="onGridReady($event)"></ag-grid-angular>
        <div class="flex-between ip-col-reverse ip-flex-end p-16 ip-px-4"
            *ngIf="totalCount > 0 && filtersPayload?.LeadVisibility">
            <div class="mr-10 ip-mt-10">Showing {{(offset * pageSize) + 1}} to
                {{(offset * pageSize) + pageSize > totalCount ? totalCount : (offset * pageSize) + pageSize}} of
                {{totalCount}} entries</div>
            <div class="show-dropdown-white flex-center ph-flex-col ph-flex-end">
                <div class="flex-center">Entries per page
                    <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" [searchable]="false"
                        ResizableDropdown class="w-80" (change)="assignCount()" [(ngModel)]="selectedPageSize">
                        <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                            {{pageSize}}</ng-option>
                    </ng-select>
                </div>
                <div class="mx-8 my-4 border-right h-16 ph-d-none"></div>
                <pagination [offset]="offset" [limit]="1" [range]="1" [size]="getPages(totalCount,pageSize)"
                    (pageChange)="onPageChange($event)" [isV2Pagination]="true">
                </pagination>
            </div>
        </div>
    </ng-container>
</div>
<ng-template #spinLoader>
    <div class="spin-loader my-20"></div>
</ng-template>
<ng-template #noDataFound>
    <tr>
        <td class="h-100 header-4 text-secondary">No Data Available for Selected Criteria. Please try again with
            Different Filter Options.</td>
    </tr>
</ng-template>