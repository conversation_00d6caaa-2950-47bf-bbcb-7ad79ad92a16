import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { CanvasJSAngularChartsModule } from '@canvasjs/angular-charts';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { AgGridModule } from 'ag-grid-angular';
import { GoogleChartsModule } from 'angular-google-charts';
import { NgCircleProgressModule } from 'ng-circle-progress';
import { NgChartsModule } from 'ng2-charts';
import { CarouselModule } from 'ngx-bootstrap/carousel';
import { DragScrollModule } from 'ngx-drag-scroll';
import { LottieModule } from 'ngx-lottie';

import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import { BasicDetailsComponent } from 'src/app/features/profile/basic-details/basic-details.component';
import { SharedModule } from 'src/app/shared/shared.module';
import {
  DASHBOARD_DECLARATIONS,
  DashboardRoutingModule,
} from './dashboard-routing.module';

@NgModule({
  declarations: [...DASHBOARD_DECLARATIONS],
  imports: [
    CommonModule,
    MatProgressBarModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    CarouselModule,
    DashboardRoutingModule,
    NgChartsModule,
    GoogleChartsModule.forRoot(),
    NgCircleProgressModule,
    LottieModule.forRoot({ player: playerFactory }),
    SharedModule,
    ReactiveFormsModule,
    FormsModule,
    AgGridModule,
    DragScrollModule,
    CanvasJSAngularChartsModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      extend: true,
    }),
  ],
  exports: [DragScrollModule],
  providers: [TranslateService, BasicDetailsComponent],
})
export class DashboardModule { }
