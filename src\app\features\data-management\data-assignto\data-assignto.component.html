<div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
        <h3>Assign To </h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 scrollbar h-100-108">
        <div class="fw-600 text-coal text-large my-8">selected data
        </div>
        <div class="scrollbar table-scrollbar">
            <table class="table standard-table no-vertical-border">
                <thead>
                    <tr class="w-100 text-nowrap">
                        <th class="w-100px">
                            <span>{{'GLOBAL.name' | translate}}</span>
                        </th>
                        <th class="w-85">
                            <span>{{ 'LEADS.assign-to' | translate }}</span>
                        </th>
                        <th class="w-60">{{ 'GLOBAL.actions' | translate }}</th>
                    </tr>
                </thead>
                <tbody class="text-secondary fw-semi-bold scrollbar max-h-100-24">
                    <ng-container>
                        <tr>
                            <td class="w-100px">
                                <div class="text-truncate-1 break-all">
                                    {{data?.name}}
                                </div>
                            </td>
                            <td class="w-85">
                                <div class="text-truncate-1 break-all">
                                    {{assignTo}}
                                </div>
                            </td>
                            <td class="w-60">
                                <a (click)="openConfirmDeleteModal(data?.name)"
                                    class="bg-light-red icon-badge">
                                    <span class="icon ic-delete m-auto ic-xxs"></span></a>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
        <div class="field-label-req mt-16">{{ 'LEADS.assign-to' | translate }}</div>
        <div>
            <form [formGroup]="assignToUserForm">
                <form-errors-wrapper [control]="assignToUserForm.controls['assignedToUsers']"
                    label="{{'LEADS.assign-to' | translate}}">
                    <ng-select [virtualScroll]="true" placeholder="Select User" name="user" class="bg-white" formControlName="assignedToUsers" ResizableDropdown>
                        <ng-option *ngFor="let user of assignToUsersList" [value]="user.id">
                            <span class="text-truncate-1 break-all"> {{user.firstName}} {{user.lastName}}</span> <span
                                class="d-none">{{user.fullName}}</span></ng-option>
                        <ng-option *ngFor="let user of deactiveUsers" [value]="user.id" [disabled]="true">
                            <span class="text-truncate-1 break-all">  {{ user.firstName }} {{ user.lastName }} </span><span class="d-none">{{user.fullName}}</span><span
                                class="error-message-custom top-10" *ngIf="!user.isActive">
                                (Disabled)</span>
                        </ng-option>
                    </ng-select>
                </form-errors-wrapper>
            </form>
        </div>
    </div>
    <div class="flex-center mt-20">
        <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
        <button class="btn-coal" (click)="save()">{{ 'BUTTONS.save' | translate }}</button>
    </div>
</div>