import { Component, EventEmitter, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { combineLatest, takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAssignedToDetails,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { AssignData } from 'src/app/reducers/data/data-management.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'data-assignto',
  templateUrl: './data-assignto.component.html',
})
export class DataAssigntoComponent implements OnInit, OnDestroy {
  data: any;
  allUsers: any[] = [];
  users: any[] = [];
  assignToUsersList: any[] = [];
  deactiveUsers: any[] = [];
  assignToUserForm: FormGroup;
  stopper: EventEmitter<void> = new EventEmitter<void>();

  canEditData: boolean = false;
  canAssignData: boolean = false;
  canDeleteData: boolean = false;

  get assignTo(): string {
    return getAssignedToDetails(this.data?.assignTo, this.allUsers, true) || '';
  }

  constructor(
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private modalRef: BsModalRef,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.assignToUserForm = this.formBuilder.group({
      assignedToUsers: [null, Validators.required],
    });

    if(this.data && this.data?.assignTo){
      this.assignToUserForm?.get('assignedToUsers').setValue(this.data?.assignTo)
    }
    this._store.dispatch(new FetchAdminsAndReportees());
    this._store.dispatch(new FetchUsersListForReassignment());
    const adminsWithReportees$ = this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper));

    const allUsers$ = this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper));

    const permissions$ = this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper));

    combineLatest({
      adminsWithReportees: adminsWithReportees$,
      allUsers: allUsers$,
      permissions: permissions$,
    }).subscribe(({ adminsWithReportees, allUsers, permissions }) => {
      this.allUsers = allUsers?.map((user: any) => {
        user = {
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        };
        return user;
      });

      if (permissions?.includes('Permissions.Users.AssignToAny')) {
        this.users = allUsers;
      } else {
        this.users = adminsWithReportees;
      }

      this.deactiveUsers = this.users
        ?.filter((user: any) => !user.isActive)
        ?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });

      const activeUsers = this.users?.filter((user: any) => user.isActive);
      this.users = assignToSort(activeUsers, this.data?.assignTo);

      this.assignToUsersList = this.users.map((user: any) => {
        return {
          ...user,
          label: `${user.firstName} ${user.lastName}`,
          value: user.id,
        };
      });
    });
  }

  openConfirmDeleteModal(dataName: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: dataName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.modalService.hide();
        }
      });
    }
  }

  save() {
    if (!this.assignToUserForm.valid) {
      validateAllFormFields(this.assignToUserForm);
      return;
    }
    const formData = this.assignToUserForm.value;
    const payload = {
      ids: [this.data?.id],
      userIds: [formData.assignedToUsers],
    };

    this._store.dispatch(new AssignData(payload));
    this.modalService.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
