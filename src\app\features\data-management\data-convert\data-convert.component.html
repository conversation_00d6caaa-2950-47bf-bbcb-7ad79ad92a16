<div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
        <h3>Convert And Assign</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 scrollbar h-100-108">
        <div class="fw-600 text-coal text-large my-8">selected data
        </div>
        <div class="scrollbar max-h-100-240">
            <table class="table standard-table no-vertical-border">
                <thead>
                    <tr class="w-100">
                        <th class="w-100px">
                            <span>{{'GLOBAL.name' | translate}}</span>
                        </th>
                        <th class="w-100px">
                            <span>{{ 'LEADS.assign-to' | translate }}</span>
                        </th>
                        <!-- <th>{{ 'GLOBAL.actions' | translate }}</th> -->
                    </tr>
                </thead>
                <tbody class="text-secondary fw-semi-bold">
                    <ng-container>
                        <tr>
                            <td class="w-100px">
                                <div class="text-truncate-1 break-all">{{data?.name}}</div>
                            </td>
                            <td class="w-100px">
                                <div class="text-truncate-1 break-all">
                                    {{assignTo}}
                                </div>
                            </td>
                            <!-- <td>
                                <a class="bg-light-red icon-badge"
                                    (click)="openConfirmDeleteModal(data?.name)">
                                    <span class="icon ic-delete m-auto ic-xxs"></span></a>
                            </td> -->
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
        <form [formGroup]="assignToUserForm">
            <div class="mt-16 mb-4" *ngIf="conditionalStatus.length">
                <span [ngClass]="assignToUserForm.controls['status'].value ? 'field-label-req' : 'field-label'">{{ 'GLOBAL.update' |
                    translate }} {{ 'GLOBAL.status' | translate }}</span>
                <span class="text-gray text-xs"
                    [ngClass]="assignToUserForm.controls['status'].value ? 'ml-12' : 'ml-4'"> (If none selected, will be
                    added as new)</span>
            </div>
            <div class="d-flex flex-wrap">
                <ng-container *ngIf="!selectedStatus">
                    <ng-container *ngFor="let status of conditionalStatus; let i = index">
                        <input formControlName="status" type="radio" class="btn-check" [value]="status.id"
                            id="statusOption{{i}}" data-automate-id="statusOption{{i}}" autocomplete="off"
                            (change)="statusChanged(status)">
                        <label class="status-badge" for="statusOption{{i}}"
                            [class.active]="assignToUserForm.controls['status'].value === status.id">
                            {{ status.displayName | titlecase }}
                        </label>
                    </ng-container>
                </ng-container>
                <div *ngIf="selectedStatus">
                    <div class="align-center mr-4 mb-4 w-100">
                        <div class="m-0 status-badge bg-dark-700 text-white br-5">{{ selectedStatus.displayName |
                            titlecase }}
                        </div>
                        <a class="icon ic-close-secondary ic-light-pale ic-xxs ml-10" id="clkCancelSelectedBadge"
                            (click)="deselectStatus()" data-automate-id="clkCancelSelectedBadge"></a>
                    </div>
                    <div class="p-12 position-relative">
                        <ng-container *ngFor="let subStatus of selectedStatus?.childTypes; let i = index">
                            <input type="radio" class="btn-check" [value]="subStatus.id" id="option{{i}}"
                                data-automate-id="option{{i}}" autocomplete="off" formControlName="subStatus">
                            <label class="status-badge"
                                [class.active]="assignToUserForm.controls['subStatus'].value === subStatus.id"
                                for="option{{i}}">
                                {{ subStatus.displayName }}
                            </label>
                        </ng-container>
                        <ng-container>
                            <div class="error-message mb-12"
                                *ngIf="selectedStatus?.childTypes?.length && !assignToUserForm.controls['subStatus'].value">
                                Sub-Status is a required field.
                            </div>
                        </ng-container>
                    </div>
                </div>
            </div>

            <ng-container *ngIf="selectedStatus">
                <div class="field-label-req">{{'LEAD_FORM.schedule-date' | translate}}</div>
                <form-errors-wrapper [control]="assignToUserForm.controls['scheduledDate']"
                    label="{{'LEAD_FORM.schedule-date' | translate}}">
                    <input [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1" [min]="minDate" readonly id="inpAppDateTime"
                        data-automate-id="inpAppDateTime" formControlName="scheduledDate"
                        placeholder="ex. 19/06/2025, 12:00 pm">
                    <owl-date-time #dt1 [hour12Timer]="'true'" (afterPickerOpen)="onPickerOpened(currentDate)"
                        [startAt]="assignToUserForm.controls['scheduledDate'].value ? null: currentDate"></owl-date-time>
                </form-errors-wrapper>
            </ng-container>
            <div class="field-label-req mt-16">{{ 'LEADS.assign-to' | translate }}</div>
            <div [ngClass]="{'blinking pe-none': isAssignToLoading}">
                <form-errors-wrapper [control]="assignToUserForm.controls['assignedToUsers']"
                    label="{{'LEADS.assign-to' | translate}}">
                    <ng-select [virtualScroll]="true" formControlName="assignedToUsers" placeholder="Select User"
                        name="user" class="bg-white" ResizableDropdown [searchable]="true" [closeOnSelect]="true">
                        <ng-option *ngFor="let user of assignToUsersList" [value]="user.id">
                            <span class="text-truncate-1 break-all"> {{user.firstName}} {{user.lastName}} </span><span
                                class="d-none">{{user.fullName}}</span></ng-option>
                        <!-- <ng-option *ngFor="let user of deactiveUsers" [value]="user.id" [disabled]="true">
                            {{ user.firstName }} {{ user.lastName }} <span class="d-none">{{user.fullName}}</span><span
                                class="error-message-custom top-10" *ngIf="!user.isActive">
                                (Disabled)</span>
                        </ng-option> -->
                    </ng-select>
                </form-errors-wrapper>
            </div>
            <div class="field-label">{{'TASK.notes' | translate}}</div>
            <form-errors-wrapper [control]="assignToUserForm.controls['notes']" label="{{'TASK.notes' | translate}}">
                <textarea rows="2" id="txtUpdateStatusNotes" data-automate-id="txtUpdateStatusNotes"
                    formControlName="notes" placeholder="ex. I want to say ..."></textarea>
            </form-errors-wrapper>
            <!-- <div class="mt-20 d-flex">
                <h5 class="fw-600 mr-10">{{'GLOBAL.mark-as' | translate}}</h5>
                <label class="checkbox-container">
                    <input type="checkbox" [checked]="assignToUserForm.get('isQualified').value" formControlName="isQualified">
                    <span class="checkmark"></span>
                    <h5>{{'GLOBAL.qualified'
                        | translate}}</h5>
                </label>
            </div> -->
        </form>
    </div>
    <div class="flex-center mt-20">
        <div class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</div>
        <div class="btn-coal" (click)="save()" *ngIf="!isConvertLoading">Assign And Convert</div>
        <div class="btn-coal" *ngIf="isConvertLoading">
            <div class="container px-4">
                <ng-container *ngFor="let dot of [1,2,3]">
                    <div class="dot-falling dot-white"></div>
                </ng-container>
            </div>
        </div>
    </div>
</div>

<ng-template #convertPopUp>
    <div class="flex-center-col fw-semi-bold p-16 br-4">
        <div class="h-150">
            <ng-lottie [options]='sad'></ng-lottie>
        </div>
        <h1 class="text-accent-green">Uh-Oh!
        </h1>
        <h3> Data cannot be converted into lead as it is being assigned to the same user.</h3>
    </div>
</ng-template>