<div class="bg-white br-10 p-16 text-mud text-sm">
    <h5 class="text-accent-green fw-700">Contact Info</h5>
    <div class="align-center mt-4">
        <p class="w-50">{{ 'GLOBAL.name' | translate }}:</p>
        <p class="w-50 break-all text-truncate-1">{{liveFormValues?.name || "--"}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">{{'USER.phone-number' | translate}}: </p>
        <p class="w-50 break-all text-truncate-1">{{liveFormValues?.contactNo || "--"}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">{{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' |
            translate}}: </p>
        <p class="w-50 break-all text-truncate-1">{{liveFormValues?.alternateContactNo || "--"}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Assign To :</p>
        <p class="w-50 break-all text-truncate-1">{{ getAssignedToDetails(liveFormValues?.assignTo, allUsers, true) ||
            "--"}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Email Address: </p>
        <p class="w-50 break-all text-truncate-1">{{liveFormValues?.email || "--"}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Created By: </p>
        <p class="w-50 break-all text-truncate-1">{{getAssignedToDetails(dataInfo?.createdBy, allUsers, true) || '--'}}
        </p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Created Date: </p>
        <p class="w-50 break-all text-truncate-1">{{dataInfo?.createdOn ? getTimeZoneDate(dataInfo?.createdOn,
            userData?.timeZoneInfo?.baseUTcOffset, 'fullDateTime'): '--'}}</p>
    </div>
    <div class="align-center mt-4"
        *ngIf="userData?.shouldShowTimeZone && userData?.timeZoneInfo?.timeZoneName && dataInfo?.createdOn">
        <p class="w-50"></p>
        <p class="text-truncate-1 break-all text-sm">
            ({{userData?.timeZoneInfo?.timeZoneName }})</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Modified By: </p>
        <p class="w-50 break-all text-truncate-1">{{getAssignedToDetails(dataInfo?.lastModifiedBy, allUsers, true) ||
            '--'}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Modified Date: </p>
        <p class="w-50 break-all text-truncate-1">{{dataInfo?.lastModifiedOn ? getTimeZoneDate(dataInfo?.lastModifiedOn,
            userData?.timeZoneInfo?.baseUTcOffset, 'fullDateTime'): '--'}}</p>
    </div>
    <div class="align-center mt-4"
        *ngIf="userData?.shouldShowTimeZone && userData?.timeZoneInfo?.timeZoneName && dataInfo?.lastModifiedOn">
        <p class="w-50"></p>
        <p class="text-truncate-1 break-all text-sm">
            ({{userData?.timeZoneInfo?.timeZoneName }})</p>
    </div>
    <div class="my-12 border-bottom"></div>
    <h5 class="text-accent-green fw-700">Enquired Info</h5>
    <div class="align-center mt-4">
        <p class="w-50">Enquired For:</p>
        <p class="w-50 break-all text-truncate-1">{{enquiredFor}}
        </p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Property Type:</p>
        <p class="w-50 break-all text-truncate-1">{{liveFormValues?.propertyTypeId || "--"}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Property Sub Type:</p>
        <p class="w-50 break-all text-truncate-1">{{liveFormValues?.propSubType || "--"}}</p>
    </div>
    <div class="align-center mt-4"
        *ngIf="liveFormValues?.propertyTypeId == 'Residential' && liveFormValues?.['propSubType'] != 'Plot' && liveFormValues?.['propSubType']">
        <p class="w-50">BHK:</p>
        <p class="w-50 break-all text-truncate-1">{{bhkNo}}</p>
    </div>
    <div class="align-center mt-4"
        *ngIf="liveFormValues?.propertyTypeId == 'Residential' && liveFormValues?.['propSubType'] != 'Plot' && liveFormValues?.['propSubType']">
        <p class="w-50">BHK Type:</p>
        <p class="w-50 break-all text-truncate-1">{{bhkTypes}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Min. {{'LEAD_FORM.budget' | translate}}:</p>
        <p class="w-50 fw-semi-bold break-all text-truncate-1">{{liveFormValues?.lowerBudget ?
            liveFormValues?.lowerBudget : '--'}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Max. {{'LEAD_FORM.budget' | translate}}:</p>
        <p class="w-50 fw-semi-bold break-all text-truncate-1">{{liveFormValues?.upperBudget ?
            liveFormValues?.upperBudget : '--'}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Location: </p>
        <p class="w-50 break-all text-truncate-1">{{cities}}</p>
    </div>
    <div class="align-center mt-4">
        <p class="w-50">Source</p>
        <p class="w-50 break-all text-truncate-1">{{sourceListMap?.[liveFormValues?.dataSource] || "--"}}</p>
    </div>
</div>