import { Component, EventEmitter, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getAssignedToDetails, getBHKDisplayString, getLocationDetailsByObj, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import * as moment from 'moment';
import { getDataSourceList } from 'src/app/reducers/data/data-management.reducer';
import { EMPTY_GUID } from 'src/app/app.constants';

@Component({
  selector: 'data-information',
  templateUrl: './data-information.component.html',
})
export class DataInformationComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() liveFormValues: any;
  @Input() dataInfo: any;
  @Input() locality: any;
  @Input() manualLocationsList: any;
  allUsers: any[] = [];
  sourceListMap: any = {};
  getAssignedToDetails = getAssignedToDetails;
  getBHKDisplayString = getBHKDisplayString;
  getTimeZoneDate = getTimeZoneDate;
  moment = moment;
  userData: any;
  EMPTY_GUID = EMPTY_GUID;

  get cities() {

    return [
      ...(this.manualLocationsList?.map((address: any) => getLocationDetailsByObj(address)) || []),
      ...(this.liveFormValues?.locationId?.map((location: any) => location?.location) || []),
      ...(this.liveFormValues?.enquiredCity?.trim() || this.liveFormValues?.enquiredState?.trim() || this.liveFormValues?.enquiredLocality?.trim()
        ? [getLocationDetailsByObj({ subLocality: this.liveFormValues?.enquiredLocality, city: this.liveFormValues?.enquiredCity, state: this.liveFormValues?.enquiredState })]
        : [])
    ]
      .join(': ') || '--';
  }

  get enquiredFor() {
    return this.liveFormValues?.enquiryTypes?.join(', ') || '--';
  }

  get bhkNo() {
    return this.liveFormValues?.bhkNo?.map((number: string) => getBHKDisplayString(number))?.join(', ') || 'None';
  }

  get bhkTypes() {
    return this.liveFormValues?.bhkTypes?.join(', ') || '--';
  }


  constructor(private _store: Store<AppState>) { }

  ngOnInit(): void {
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUsers = data;
      });

    this._store
      .select(getDataSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((sources: any) => {
        sources.forEach((source: any) => {
          this.sourceListMap[source.id] = source.displayName;
        });
      });
      this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
