<div class="scrollbar h-100-309">
    <ng-container *ngIf="notesHistory?.length; else noNotes">
        <div *ngFor="let notesList of notesHistory" class="justify-center"
            [ngClass]="notesList?.data.length ? 'border-bottom-slate' : ''">
            <ng-container *ngIf="notesList?.data?.length">
                <h6 *ngIf="notesList?.data?.length" class="text-black-200 mt-16 text-nowrap">
                    {{ notesList?.date }}
                </h6>
                <div class="w-100">
                    <ng-container *ngFor="let note of notesList?.data">
                        <div class="card p-10 br-4 bg-white border-0 text-dark-gray">
                            <h6 [innerHTML]="convertUrlsToLinks(note?.newValue)" class="word-break line-break"></h6>
                            <div class="flex-end">
                                <h6>{{
                                    getTimeZoneDate(note?.modifiedOn,userData?.timeZoneInfo?.baseUTcOffset,'timeWithMeridiem')
                                    }}</h6>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </ng-container>
        </div>
    </ng-container>
</div>
<div class="box-shadow-20 bg-white p-16" [ngClass]="{'tb-mb-65': formDirty}" [formGroup]="noteForm">
    <form-errors-wrapper [control]="noteForm.controls['noteText']" label="Notes">
        <textarea rows="3" formControlName="noteText" class="non-resizable scrollbar"
            placeholder="ex. Type here to add note ........." (ngModelChange)="onNoteTextChange($event)"></textarea>
    </form-errors-wrapper>
</div>
<ng-template #noNotes>
    <div class="h-100-393 flex-center-col">
        <ng-lottie [options]='options'></ng-lottie>
        <div class="fw-600 header-4 text-light-slate">No Notes Found</div>
    </div>
</ng-template>