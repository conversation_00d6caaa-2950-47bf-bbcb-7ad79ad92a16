import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import {
  convertUrlsToLinks,
  getTimeZoneDate,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { UpdateDataNotes } from 'src/app/reducers/data/data-management.actions';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'data-notes',
  templateUrl: './data-notes.component.html',
})
export class DataNotesComponent implements OnInit, OnChanges {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  @Input() history: any;
  @Input() formDirty: boolean = false;
  @Input() dataId: string;
  @Output() updateFormDirty: EventEmitter<boolean> =
    new EventEmitter<boolean>();
  notesHistory: any[] = [];
  moment = moment;
  options: AnimationOptions = {
    path: 'assets/animations/empty-notes.json',
  };
  userData: any;
  getTimeZoneDate = getTimeZoneDate;
  convertUrlsToLinks = convertUrlsToLinks;
  noteForm: FormGroup;

  constructor(private _store: Store<AppState>, private fb: FormBuilder) {
    this.noteForm = this.fb.group({
      noteText: [null, ValidationUtil.cannotBeBlank],
    });
  }

  ngOnInit(): void {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes?.formDirty) {
      this.formDirty = changes?.formDirty?.currentValue;
    }
    if (changes?.dataId) {
      this.dataId = changes?.dataId?.currentValue;
    }
    if (changes?.history) {
      // this.history = this.transformData(this.history);
      this.notesHistory = [];
      if (changes?.history) {
        this.notesHistory = [];
        this.notesHistory = Object.entries(this.history).map((item: any) => {
          return {
            date: item[0],
            data: item[1]?.filter(
              (historyObj: any) => historyObj?.fieldName === 'Notes'
            ),
          };
        });
      }
    }
  }

  // transformData(inputData: any): any {
  //   const transformedData: any = {};

  //   for (const timestamp in inputData) {
  //     if (inputData.hasOwnProperty(timestamp)) {
  //       const date = new Date(new Date(timestamp).setHours(0,0,0,0)).toString()
  //       if (!transformedData?.[date]) {
  //         transformedData[date] = [];
  //       }

  //       for (const item of inputData[timestamp]) {
  //         transformedData[date].push(item);
  //       }
  //     }
  //   }

  //   return transformedData;
  // }

  save(isSaveAndClose: boolean = false, isSaveAndNext: boolean = false) {
    if (!this.noteForm.valid) {
      validateAllFormFields(this.noteForm);
      return;
    }
    if (this.noteForm.get('noteText').value) {
      const payload: any = {
        prospectId: this.dataId,
        notes: this.noteForm.get('noteText').value?.trim(),
      };
      this._store.dispatch(
        new UpdateDataNotes(payload, isSaveAndClose, isSaveAndNext)
      );
    }
    this.noteForm.reset();
    this.onNoteTextChange('');
  }

  onNoteTextChange(event: any) {
    this.formDirty = event ? true : false;
    this.updateFormDirty?.emit(this.formDirty);
  }
}
