import { Location } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { firstValueFrom, skipWhile, take, takeUntil } from 'rxjs';

import {
  EMPTY_GUID,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import {
  BHKType,
  EnquiryType,
  FurnishStatus,
  Gender,
  MaritalStatusType,
  OfferType,
  Profession,
  PurposeType,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  formatBudget,
  getBHKDisplayString,
  getBRDisplayString,
  getLocationDetailsByObj,
  getSystemTimeOffset,
  getTimeZoneDate,
  onPickerOpened,
  setTimeZoneDateWithTime,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  CommunicationDataCount,
  CommunicationDataMessage,
  FetchAllData,
  FetchDataById,
  FetchDataHistoryById,
  FetchDataStatus,
  UpdateDataFilterPayload,
  UpdateStatus,
} from 'src/app/reducers/data/data-management.actions';
import {
  DataManagementFilters,
  getActiveData,
  getActiveDataIsLoading,
  getAllData,
  getAllDataIsLoading,
  getCardData,
  getDataFiltersPayload,
  getDataHistory,
  getDataHistoryIsLoading,
  getDataStatusList,
  getDataStatusListIsLoading,
  getTotalDataCount,
} from 'src/app/reducers/data/data-management.reducer';
import { getGlobalAnonymousIsLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { CommonClickToCall, FetchVirtualNos, FetchVNAssignment } from 'src/app/reducers/Integration/integration.actions';
import { getVirtualNos, getVirtualNosIsLoading, getVNAssignment, getVNAssignmentIsLoading } from 'src/app/reducers/Integration/integration.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import {
  getAreaUnitIsLoading,
  getAreaUnits,
} from 'src/app/reducers/master-data/master-data.reducer';
import { NotificationCall } from 'src/app/reducers/notification-info/notification-info.action';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchIVRSettingListById } from 'src/app/reducers/teams/teams.actions';
import {
  getIVRSettingById,
  getIVRSettingByIdIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { DataManagementService } from 'src/app/services/controllers/data-management.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { LeadCallComponent } from '../../leads/lead-call/lead-call.component';
import { LeadsTemplateShareComponent } from '../../leads/leads-template-share/leads-template-share.component';
import { DataConvertComponent } from '../data-convert/data-convert.component';
import { DataNotesComponent } from '../data-notes/data-notes.component';

@Component({
  selector: 'data-preview',
  templateUrl: './data-preview.component.html',
})
export class DataPreviewComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('mobileNotification') mobileNotification!: TemplateRef<any>;

  showContact: boolean = true;
  showInfo: boolean = true;
  others: boolean = true;
  additionalInfo: boolean = true;
  locationInfo: boolean = true;
  selectedSection: string;
  selectedDataId: any;
  data: any;
  moment = moment;
  statusList: any[] = [];
  statusUpdateForm: FormGroup;
  selectedStatusList: any[] = [];
  history: any = {};
  statusModified: any;
  EnquiryType = EnquiryType;
  BHKType = BHKType;
  notesDirty: boolean = false;
  formatBudget = formatBudget;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild(DataNotesComponent) dataNotesComponent: DataNotesComponent;
  showLeftNav: boolean = true;
  areaSizeUnits: any = {};
  isAreaSizeUnitsLoading: boolean = true;
  canEditData: boolean = false;
  canViewData: boolean = false;
  isStatusListLoading: boolean = true;
  isDataLoading: boolean = true;
  isHistoryLoading: boolean = true;
  defaultCurrency: string = 'INR';
  userBasicDetails: any;
  getLocationDetailsByObj = getLocationDetailsByObj;
  getBHKDisplayString = getBHKDisplayString;
  getTimeZoneDate = getTimeZoneDate;
  getSystemTimeOffset = getSystemTimeOffset;
  onPickerOpened = onPickerOpened;
  contactForm: FormGroup;
  @ViewChild('contactNoInput') contactNoInput: any;
  duplicateLead: boolean;
  isApiCallInProgress: boolean = false;
  allData: any = [];
  filtersPayload: any = {};
  foundIndex: number = 0;
  allDataLoading: boolean = true;
  dataTotalCount: number = 0;
  OfferType = OfferType;
  FurnishStatus = FurnishStatus;

  allUsers: any;
  currentDate: Date = new Date();
  userData: any;
  canConvertToLead: boolean = false;
  globalSettingsDetails: any;
  users: any;
  userId: any;
  userCallType: any;
  VNAssignment: any;
  virtualNosList: any;
  selectedCallType: any;
  contactAdminType: string;
  clickedDataId: string;
  selectedVirtualNo: null;
  userPhoneNo: string;
  userPermissions: string;
  PurposeType = PurposeType;
  Gender = Gender;
  MaritalStatusType = MaritalStatusType;
  get minDate(): Date {
    const minDate = new Date(this.currentDate);
    minDate.setHours(0, 0, 0, 0);
    return minDate;
  }

  get enquiryTypes() {
    return (
      this.data?.enquiry?.enquiryTypes
        ?.map((type: any) => EnquiryType[type])
        .join(', ') || '--'
    );
  }

  get hasNonPlotPropertyTypes(): boolean {
    if (!this.data?.enquiry?.propertyTypes) {
      return false;
    }

    return this.data.enquiry.propertyTypes.some(
      (propertyType: any) => propertyType?.childType?.displayName !== 'Plot'
    );
  }

  get bhkNo(): string {
    return this.globalSettingsDetails?.isCustomLeadFormEnabled
      ? this.data?.enquiry?.bhKs
        ?.map((bhk: any) => getBRDisplayString(bhk))
        ?.join(', ')
      : this.data?.enquiry?.bhKs
        ?.map((bhk: any) => getBHKDisplayString(bhk))
        ?.join(', ');
  }

  get beds(): string {
    return Array.isArray(this.data.enquiry?.beds)
      ? this.data?.enquiry?.beds.map((bed: any) =>
        bed === 0 || bed === '0' ? 'Studio' : bed
      )
      : [];
  }

  get bhkTypes(): string {
    return (
      this.data?.enquiry?.bhkTypes
        ?.map((type: any) => BHKType[type])
        ?.join(', ') || '--'
    );
  }

  get isUnassignedData(): boolean {
    return this.data?.assignTo == EMPTY_GUID;
  }

  constructor(
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    public modalService: BsModalService,
    public modalRef: BsModalRef,
    public router: Router,
    private activatedRoute: ActivatedRoute,
    private _store: Store<AppState>,
    private formBuilder: FormBuilder,
    private cdr: ChangeDetectorRef,
    private shareDataService: ShareDataService,
    private DataManagementService: DataManagementService,
    private location: Location,
    public NotifyModalRef: BsModalRef,
    public trackingService: TrackingService
  ) {
    this.userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    this.userPermissions = localStorage.getItem('userPermissions');
    this.headerTitle.setLangTitle('SIDEBAR.data-preview');
    this.metaTitle.setTitle('CRM | Data Preview');
    this.contactForm = this.formBuilder.group({
      contactNo: [''],
    });
  }

  ngOnInit(): void {
    this.trackingService.trackFeature('Web.Data.Page.DataPreview.Visit');

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
        this.globalSettingsDetails = data;
      });
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canEditData = permissionsSet.has('Permissions.Prospects.Update');
        this.canViewData = permissionsSet.has('Permissions.Prospects.View');
        this.canConvertToLead = permissionsSet.has(
          'Permissions.Prospects.ConvertToLead'
        );
      });

    this._store.dispatch(new FetchAreaUnitList());

    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        if (units && Array.isArray(units)) {
          units.forEach((unitItem: any) => {
            if (unitItem?.id && unitItem?.unit) {
              this.areaSizeUnits[unitItem.id] = unitItem.unit;
            }
          });
        }
      });

    this._store
      .select(getAreaUnitIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAreaSizeUnitsLoading = isLoading;
      });

    this._store
      .select(getActiveData)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (this.selectedDataId === data?.id) {
          this.data = data;
        }
        this.allData = this.allData?.map((dataItem: any) => {
          if (dataItem?.id === data?.id) {
            return data;
          }
          return dataItem;
        });
        this.contactForm.get('contactNo').setValue(data?.contactNo);
      });

    this._store
      .select(getActiveDataIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isDataLoading = isLoading;
      });

    this._store
      .select(getAllDataIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.allDataLoading = isLoading;
      });

    this._store
      .select(getDataHistory)
      .pipe(takeUntil(this.stopper))
      .subscribe((history: any) => {
        const historyArray = Array.isArray(history) ? history : [];

        this.history = historyArray.reduce((acc: any, curr: any) => {
          const modifiedOn = getTimeZoneDate(
            curr.modifiedOn,
            this.userData?.timeZoneInfo?.baseUTcOffset,
            'dayMonthYear'
          );

          if (!acc[modifiedOn]) {
            acc[modifiedOn] = [];
          }
          acc[modifiedOn].push(curr);
          return acc;
        }, {});

        this.statusModified = historyArray.find(
          (curr: any) => curr?.fieldName === 'Status'
        );
      });

    this._store
      .select(getDataHistoryIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isHistoryLoading = isLoading;
      });

    this._store.dispatch(new FetchDataStatus());
    this._store
      .select(getDataStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((statuses: any) => {
        this.statusList = statuses;
        // statuses.forEach((status: any) => {
        //   this.statusIdMap[status.id] = status.displayName;
        // });
      });

    this._store
      .select(getDataStatusListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isStatusListLoading = isLoading;
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        );
      });

    this._store
      .select(getIVRSettingById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userCallType = data?.callThrough;
      });

    this._store
      .select(getVNAssignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.VNAssignment = item;
      });

    this.statusUpdateForm = this.formBuilder.group({
      statusId: ['', Validators.required],
      scheduleDate: null,
      scheduleTime: [null, this.validateScheduleTime(this.currentDate)],
      notes: null,
    });

    this.statusUpdateForm.controls['scheduleDate'].valueChanges.subscribe(
      () => {
        if (this.statusUpdateForm.controls.scheduleTime.value)
          this.statusUpdateForm.controls.scheduleTime.patchValue(
            this.statusUpdateForm.controls.scheduleTime.value
          );
      }
    );

    this.statusUpdateForm.get('statusId').valueChanges.subscribe((val: any) => {
      const makeRequired: any = {
        ScheduledDate: ['scheduleDate', 'scheduleTime'],
      };

      if (this.selectedStatusList?.length) {
        this.selectedStatusList.forEach((status: any) => {
          const controlsToMakeRequired = makeRequired[status.field.name];
          controlsToMakeRequired?.forEach((controlName: string) => {
            if (
              status.isRequired === true ||
              (status.validators && status.validators.includes('required'))
            ) {
              toggleValidation(
                VALIDATION_SET,
                this.statusUpdateForm,
                controlName,
                [Validators.required]
              );
            } else {
              toggleValidation(
                VALIDATION_CLEAR,
                this.statusUpdateForm,
                controlName
              );
            }
            this.statusUpdateForm.get(controlName)?.updateValueAndValidity();
          });
        });
      } else {
        (Object.values(makeRequired).flat() as string[]).forEach(
          (controlName: string) => {
            toggleValidation(
              VALIDATION_CLEAR,
              this.statusUpdateForm,
              controlName
            );
            this.statusUpdateForm.get(controlName)?.updateValueAndValidity();
          }
        );
        this.removeScheduler();
      }
      this.statusUpdateForm
        .get('scheduleDate')
        ?.valueChanges.subscribe((dateValue: any) => {
          if (dateValue) {
            toggleValidation(
              VALIDATION_SET,
              this.statusUpdateForm,
              'scheduleTime',
              [Validators.required, this.validateScheduleTime(this.currentDate)]
            );
          } else {
            toggleValidation(
              VALIDATION_CLEAR,
              this.statusUpdateForm,
              'scheduleTime'
            );
          }
          this.statusUpdateForm.get('scheduleTime')?.updateValueAndValidity();
        });
    });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
  }

  async ngAfterViewInit() {
    await this.initializeQueryParams();
    await this.initializeParams();
    await this.initializeFilters();
    this.subscribeToDataChanges();
    this.swipeEventListener();
    this.cdr.detectChanges();
  }

  swipeEventListener() {
    const swipeArea = document.getElementById('swipeArea');
    let startX: any, startY: any;

    swipeArea?.addEventListener('touchstart', (event) => {
      startX = event.touches[0].clientX;
      startY = event.touches[0].clientY;
    });

    swipeArea?.addEventListener('touchend', (event) => {
      const endX = event.changedTouches[0].clientX;
      const endY = event.changedTouches[0].clientY;

      const deltaX = endX - startX;
      const deltaY = endY - startY;
      if (deltaX > 100 && Math.abs(deltaY) < 50) {
        this.prevData();
      } else if (deltaX < -100 && Math.abs(deltaY) < 50) {
        this.nextData();
      }
    });
  }

  private async initializeQueryParams() {
    const queryParams = await firstValueFrom(this.activatedRoute.queryParams);
    this.selectedSection = queryParams?.notes ? 'Notes' : 'History';
  }

  private async initializeParams() {
    const params = await firstValueFrom(this.activatedRoute.params);
    if (params?.id) {
      this.selectedDataId = params.id;
      this.fetchDataById(this.selectedDataId);
    }
  }

  private async initializeFilters() {
    const filters: DataManagementFilters = await firstValueFrom(
      this._store.select(getDataFiltersPayload).pipe(takeUntil(this.stopper))
    );

    const isBudgetNotPresent = (!filters?.FromMaxBudget || !filters?.ToMaxBudget) && (!filters?.FromMinBudget || !filters?.ToMinBudget);
    const currency = isBudgetNotPresent ? null : filters?.Currency;

    this.filtersPayload = {
      ...this.filtersPayload,
      ...filters,
      ProspectSearch: filters?.ProspectSearch || null,
      PageNumber: filters?.PageNumber || 1,
      PageSize: filters?.PageSize || 10,
      DateType: filters?.DateType || 0,
      FromDate: filters?.FromDate,
      ToDate: filters?.ToDate,
      FilterType: filters?.FilterType || 0,
      Currency: currency,
    };
  }

  private subscribeToDataChanges() {
    this._store
      .select(getCardData)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.length) {
          this.allData = data;
          this.allDataLoading = false;
        } else {
          this._store.dispatch(new FetchAllData(this.filtersPayload));
          this._store
            .select(getAllData)
            .pipe(
              skipWhile(() => this.allDataLoading),
              take(1)
            )
            .subscribe((data: any) => {
              if (data?.length) {
                this.allData = data;
              }
            });
        }
      });
    this._store
      .select(getTotalDataCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((count: any) => {
        this.dataTotalCount = count || 0;
      });
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUsers = data?.map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));
      });
  }

  private fetchDataById(dataId: string) {
    this._store.dispatch(new FetchDataById(dataId));
    this._store.dispatch(new FetchDataHistoryById(dataId));
  }

  nextData() {
    if (this.isDataLoading || this.isHistoryLoading || this.allDataLoading) {
      return;
    }
    this.foundIndex = this.allData.findIndex(
      (data: any) => data.id === this.selectedDataId
    );

    const currentIndex =
      (this.filtersPayload.PageNumber - 1) * this.filtersPayload.PageSize +
      this.foundIndex +
      1;
    if (currentIndex >= this.dataTotalCount) {
      return;
    }

    if (this.foundIndex === -1) {
      this.filtersPayload = {
        ...this.filtersPayload,
        PageNumber: 1,
      };
      this.loadDataPage();
    } else if (this.foundIndex === this.allData.length - 1) {
      this.filtersPayload = {
        ...this.filtersPayload,
        PageNumber: this.filtersPayload?.PageNumber + 1,
      };
      this.loadDataPage();
    } else {
      this.updateSelectedData(this.foundIndex + 1);
    }
  }

  prevData() {
    if (this.isDataLoading || this.isHistoryLoading || this.allDataLoading) {
      return;
    }

    this.foundIndex = this.allData.findIndex(
      (data: any) => data.id === this.selectedDataId
    );

    if (this.foundIndex === -1) {
      this.filtersPayload = {
        ...this.filtersPayload,
        PageNumber: 1,
      };
      this.loadDataPage(true);
    } else if (this.foundIndex === 0 && this.filtersPayload.PageNumber > 1) {
      this.filtersPayload = {
        ...this.filtersPayload,
        PageNumber: this.filtersPayload?.PageNumber - 1,
      };
      this.loadDataPage(true);
    } else {
      this.updateSelectedData(this.foundIndex - 1);
    }
  }

  private loadDataPage(loadLastItem = false) {
    this._store.dispatch(new UpdateDataFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchAllData(this.filtersPayload));
    this.allDataLoading = true;
    this._store
      .select(getAllData)
      .pipe(
        skipWhile(() => this.allDataLoading),
        take(1)
      )
      .subscribe((data: any) => {
        if (data?.length) {
          this.allData = data;
          const index = loadLastItem ? data.length - 1 : 0;
          this.updateSelectedData(index);
        }
      });
  }

  private updateSelectedData(index: number) {
    if (index >= 0 && index < this.allData.length) {
      this.data = this.allData[index];
      this.selectedDataId = this.data?.id;
      this.statusUpdateForm?.reset();
      this.location.replaceState(`/data/data-preview/${this.selectedDataId}`);
      this._store.dispatch(new FetchDataHistoryById(this.selectedDataId));
    }
  }

  hexToRgba(hex: string, opacity: number) {
    hex = hex.replace('#', '');
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    const validOpacity = Math.min(1, Math.max(0, opacity));
    return `rgba(${r}, ${g}, ${b}, ${validOpacity})`;
  }

  validateScheduleTime(currDateOnTZ: any): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const currentTime = new Date(currDateOnTZ);
      const selectedTime = new Date(control?.value);

      const selectedHours = selectedTime?.getHours();
      const selectedMinutes = selectedTime?.getMinutes();
      const currentDate = new Date(currDateOnTZ);
      const scheduleDate = new Date(this.statusUpdateForm?.value?.scheduleDate);

      currentDate.setHours(0, 0, 0, 0);
      scheduleDate.setHours(0, 0, 0, 0);

      if (!control?.value) {
        return { required: true };
      }

      if (currentDate.getTime() === scheduleDate.getTime()) {
        const timeDifference = selectedTime.getTime() - currentTime.getTime();

        if (
          selectedHours < currentTime.getHours() ||
          (selectedHours === currentTime.getHours() &&
            selectedMinutes <= currentTime.getMinutes()) ||
          timeDifference < 1 * 60 * 1000
        ) {
          return { invalidTime: true };
        }
      }

      return null;
    };
  }

  handleUpdateFormDirty(isDirty: boolean) {
    this.notesDirty = isDirty;
  }

  getAbbreviation(name: string) {
    if (!name) return '--';
    const words = name.split(' ');
    let abbreviation = '';

    for (const word of words) {
      if (word.length > 0) {
        abbreviation += word[0].toUpperCase();
      }
      if (abbreviation.length === 2) {
        break;
      }
    }

    return abbreviation;
  }

  openConvertedModal(convertToLeadModal: any) {
    if (this.isApiCallInProgress) {
      return;
    }
    if (this.modalRef) {
      this.modalRef.hide();
    }

    if (this.duplicateLead === undefined || this.duplicateLead === null) {
      this.isApiCallInProgress = true;

      this.DataManagementService.convertToLeadBulk([
        {
          prospectId: this.data?.id,
          primaryContactNo: this.data?.contactNo,
          alternateContactNo: this.data?.alternateContactNo,
        },
      ]).subscribe(
        (resp: any) => {
          this.isApiCallInProgress = false;

          this.duplicateLead = resp?.data.includes(this.data?.id);

          if (!this.duplicateLead) {
            this.openConvertLeadModal();
          } else {
            this.modalRef = this.modalService.show(convertToLeadModal, {
              class: 'modal-400 top-modal ph-modal-unset',
            });
          }
        },
        () => {
          this.isApiCallInProgress = false;
        }
      );

      this._store.dispatch(new LoaderHide());
    } else {
      if (!this.duplicateLead) {
        this.openConvertLeadModal();
      } else {
        this.modalRef = this.modalService.show(convertToLeadModal, {
          class: 'modal-400 top-modal ph-modal-unset',
        });
      }
    }
  }

  openConvertLeadModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState = {
      data: this.data,
    };
    this.modalRef = this.modalService.show(DataConvertComponent, {
      class: 'modal-350 right-modal ip-modal-unset',
      initialState,
    });
  }

  openEditData() {
    this.router.navigate([`data/edit-data/${this.data?.id}`]);
  }

  save(isSaveAndClose: boolean = false, isSaveAndNext: boolean = false) {
    Object.keys(this.statusUpdateForm.controls).forEach((field) => {
      const control = this.statusUpdateForm.get(field);
      if (control && control.invalid) {
        console.log(`Invalid field: ${field}`);
      }
    });

    if (this.statusUpdateForm?.touched) {
      if (!this.statusUpdateForm.valid) {
        validateAllFormFields(this.statusUpdateForm);
        return;
      }

      const formData = this.statusUpdateForm.value;
      let payload = {
        id: this.data?.id,
        statusId: formData?.statusId,
        scheduleDate: setTimeZoneDateWithTime(
          this.mergeDateTime(formData?.scheduleDate, formData?.scheduleTime),
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        ),
        notes: formData?.notes?.trim() ? formData?.notes?.trim() : null,
      };

      this._store.dispatch(
        new UpdateStatus(payload, isSaveAndClose, isSaveAndNext)
      );
      this.statusUpdateForm?.reset();
    }
    !this.statusUpdateForm?.touched &&
      this.dataNotesComponent?.save(isSaveAndClose, isSaveAndNext);
    if (isSaveAndNext) {
      this.nextData();
    }
    this.selectedStatusList = [];
  }

  mergeDateTime(date: any, time: any) {
    if (!date || !time) return null;
    const date1 = new Date(time);
    const date2 = new Date(date);

    const newDateString = new Date(
      date2.getFullYear(),
      date2.getMonth(),
      date2.getDate(),
      date1.getHours(),
      date1.getMinutes(),
      date1.getSeconds(),
      date1.getMilliseconds()
    );

    return newDateString;
  }
  async checkToCall(
    event: any,
    virtualNo: TemplateRef<any>,
    askBefore: TemplateRef<any>,
    contactAdmin: TemplateRef<any>,
    chooseToCallType: TemplateRef<any>,
    dataId: string
  ) {
    this.clickedDataId = dataId;
    event.stopPropagation();
    await this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    if (this.globalSettingsDetails.callSettings.callType === 2) {
      this._store.dispatch(new FetchIVRSettingListById(this.userId));
      await this._store
        .select(getIVRSettingByIdIsLoading)
        .pipe(
          skipWhile((isLoading: boolean) => {
            return isLoading;
          }),
          take(1)
        )
        .toPromise();
      switch (this.userCallType) {
        case 0:
          this.openAskBeforeCall(askBefore);
          break;
        case 1:
          this.openIVROnly(virtualNo, contactAdmin, chooseToCallType);
          break;
        case 2:
          this.initCall();
          break;
      }
    } else {
      this.initCall();
    }
  }

  openAskBeforeCall(askBefore: TemplateRef<any>) {
    this.modalRef = this.modalService.show(askBefore, {
      class: 'modal-350 top-modal ip-modal-unset',
    });
  }

  async openIVROnly(
    virtualNo: TemplateRef<any>,
    contactAdmin: TemplateRef<any>,
    chooseToCallType: TemplateRef<any>
  ) {
    if (this.userPermissions?.includes('IVRCall')) {
      if (this.globalSettingsDetails?.isIVROutboundEnabled) {
        if (this.globalSettingsDetails?.isVirtualNumberRequiredForOutbound) {
          this._store.dispatch(new FetchVNAssignment(this.clickedDataId));
          await this._store
            .select(getVNAssignmentIsLoading)
            .pipe(
              skipWhile((isLoading: boolean) => {
                return isLoading;
              }),
              take(1)
            )
            .toPromise();
          if (this.VNAssignment?.isVirtualNumberAssigned) {
            this.ivrClickToCheck(
              this.VNAssignment?.virtualNumber,
              chooseToCallType
            );
          } else if (this.VNAssignment?.shouldFetchVirtualNumbers) {
            this._store.dispatch(new FetchVirtualNos());
            await this._store
              .select(getVirtualNosIsLoading)
              .pipe(
                skipWhile((isLoading: boolean) => {
                  return isLoading;
                }),
                take(1)
              )
              .toPromise();
            this.checkVirtualNumber(virtualNo);
          }
        } else {
          this.ivrClickToCheck(null, chooseToCallType);
        }
      } else {
        this.openContactAdmin(contactAdmin, 'NoPrimaryOutBound');
      }
    } else {
      this.openContactAdmin(contactAdmin, 'UserPermission');
    }
  }

  initCall() {
    if (this.data?.alternateContactNo) {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      let initialState: any = {
        data: { ...this.data, isData: true },
      };
      this.modalRef = this.modalService.show(
        LeadCallComponent,
        Object.assign(
          {},
          {
            class: 'modal-400 top-modal ph-modal-unset',
            initialState,
          }
        )
      );
    } else if (this.globalSettingsDetails.isMobileCallEnabled) {
      let payload = {
        contactNo: this.data?.contactNo,
        userId: this.userId,
        name: this.data?.name,
      };
      this._store.dispatch(new NotificationCall(payload));
      this.mobileNotifyInfo();
      this.onInitiateDataCall(1);
      this.modalRef.hide();
    } else {
      location.href = 'tel:' + this.data?.contactNo;
      this.onInitiateDataCall(1);
    }
  }

  mobileNotifyInfo() {
    let initialState: any = {
      class: 'modal-400 top-modal ph-modal-unset',
    };
    this.NotifyModalRef = this.modalService.show(
      this.mobileNotification,
      initialState
    );
    const intervalId = setInterval(() => {
      this.NotifyModalRef.hide();
      clearInterval(intervalId);
    }, 5000);
  }

  openDialerPad() {
    location.href = 'tel:' + this.data.contactNo;
    this.modalRef.hide();
  }

  openContactAdmin(contactAdmin: TemplateRef<any>, type: string) {
    switch (type) {
      case 'UserPermission':
        this.contactAdminType =
          "You don't have permission to call through IVR, Please contact your admin";
        break;
      case 'NoPrimaryOutBound':
        this.contactAdminType =
          'No primary IVR account found in Integration, please integrate an "Outbound" account with valid token and make it primary';
        break;
      case 'NoAgents':
        this.contactAdminType =
          'No Agents are registered with your IVR Service Provider. Please contact your Admin.';
        break;
    }

    this.modalRef = this.modalService.show(contactAdmin, {
      class: 'modal-400 top-modal ph-modal-unset',
    });
  }

  checkVirtualNumber(virtualNo: any) {
    this._store
      .select(getVirtualNos)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.virtualNosList = item;
        this.modalRef = this.modalService.show(virtualNo, {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
        });
      });
  }

  ivrClickToCheck(virtualNo: string, chooseToCallType: TemplateRef<any>) {
    if (this.data.alternateContactNo) {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      this.selectedCallType = this.data.contactNo;
      this.modalRef = this.modalService.show(chooseToCallType, {
        class: 'modal-400 top-modal ph-modal-unset',
      });
    } else {
      this.ivrClickToCall(virtualNo, false);
    }
  }

  ivrClickToCall(virtualNo: string, alternateContactNo: boolean = false) {
    this.onInitiateDataCall(1);
    let payload = {
      destinationNumber: !alternateContactNo
        ? this.data.contactNo
        : this.selectedCallType,
      agentNumber: this.userPhoneNo,
      callerIdOrVirtualNumber: virtualNo,
      prospectId: this.data.id,
      userId: this.userId,
    };
    this._store.dispatch(new CommonClickToCall(payload));
    this._store.dispatch(new LoaderHide());
    this.modalRef.hide();
  }

  onInitiateDataCall(contactType: number) {
    const payload: any = {
      prospectId: this.data?.id,
      contactType: contactType,
    };
    let payloadCount = {
      id: this.data?.id,
      contactType: contactType,
    };
    this._store.dispatch(
      new CommunicationDataCount(payloadCount.id, payloadCount)
    );
    this._store.dispatch(new CommunicationDataMessage(payload));
    this._store.dispatch(new LoaderHide());
  }

  closeModal() {
    this.modalRef.hide();
    this.selectedVirtualNo = null;
  }


  openTemplateModal(event: any, shareType: string) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      data: { ...this.data, isData: true, shareType: shareType },
    };
    this.modalRef = this.modalService.show(
      LeadsTemplateShareComponent,
      Object.assign(
        {},
        {
          class: 'modal-300 right-modal',
          initialState,
        }
      )
    );
  }

  updateStatus(status: any) {
    this.selectedStatusList = status?.customFields;
  }

  getProfession(profession: Profession): string {
    return Profession[profession];
  }

  validateButton(buttonType: string) {
    const foundIndex = this.allData.findIndex(
      (data: any) => data.id === this.selectedDataId
    );

    if (buttonType === 'prev') {
      return (
        (this.filtersPayload.PageNumber - 1) * this.filtersPayload.PageSize +
        foundIndex ===
        0
      );
    } else {
      return (
        (this.filtersPayload.PageNumber - 1) * this.filtersPayload.PageSize +
        foundIndex +
        1 ===
        this.dataTotalCount
      );
    }
  }

  getName(id: string) {
    let name = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) name = `${user.fullName}`;
    });
    return name ? name : '--';
  }

  close() {
    this.router.navigate([`data/manage-data`]);
  }

  removeScheduler(datetime: any = false) {
    if (datetime === 'date') {
      this.statusUpdateForm.get('scheduleDate').reset();
      this.statusUpdateForm.get('scheduleTime').reset();
    }
    if (datetime == 'time') {
      this.statusUpdateForm.get('scheduleTime').reset();
    }
    if (datetime == false) {
      this.statusUpdateForm.get('scheduleDate').reset();
      this.statusUpdateForm.get('scheduleTime').reset();
    }
  }


  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
