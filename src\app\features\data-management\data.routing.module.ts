import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BulkUploadComponent } from 'src/app/shared/components/bulk-upload/bulk-upload.component';
import { ConvertConfirmationComponent } from './convert-confirmation/convert-confirmation.component';
import { DataActionsComponent } from './data-actions/data-actions.component';
import { DataAssigntoComponent } from './data-assignto/data-assignto.component';
import { DataBulkUpdateComponent } from './data-bulk-update/data-bulk-update.component';
import { DataConvertComponent } from './data-convert/data-convert.component';
import { DataHistoryComponent } from './data-history/data-history.component';
import { DataInformationComponent } from './data-information/data-information.component';
import { DataNotesComponent } from './data-notes/data-notes.component';
import { DataPreviewComponent } from './data-preview/data-preview.component';
import { dataRootLayoutComponent } from './data-root-component';
import { ManageDataAdvanceFiltersComponent } from './manage-data/manage-data-advance-filters/manage-data-advance-filters.component';
import { ManageDataCardComponent } from './manage-data/manage-data-card/manage-data-card.component';
import { ManageDataComponent } from './manage-data/manage-data.component';
import { CustomizationAddDataComponent } from './customization-add-data/customization-add-data.component';

export const routes: Routes = [
  { path: '', redirectTo: 'manage-data', pathMatch: 'full' },
  { path: 'manage-data', component: ManageDataComponent },
  { path: 'add-data', component: CustomizationAddDataComponent },
  { path: 'edit-data/:id', component: CustomizationAddDataComponent },
  { path: 'data-preview/:id', component: DataPreviewComponent },
  { path: 'bulk-upload', component: BulkUploadComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class dataRoutingModule { }

export const DATA_DECLARATIONS = [
  dataRootLayoutComponent,
  ManageDataComponent,
  DataPreviewComponent,
  DataHistoryComponent,
  DataNotesComponent,
  DataInformationComponent,
  DataBulkUpdateComponent,
  DataActionsComponent,
  DataAssigntoComponent,
  DataConvertComponent,
  ManageDataAdvanceFiltersComponent,
  ManageDataCardComponent,
  ConvertConfirmationComponent,
  CustomizationAddDataComponent
];
