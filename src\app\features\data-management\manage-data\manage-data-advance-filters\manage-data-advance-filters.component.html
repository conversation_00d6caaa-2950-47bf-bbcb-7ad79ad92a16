<div class="lead-adv-filter px-30 bg-white brbl-15 brbr-15">
  <div class="h-100-100 scrollbar position-relative">
    <div class="adv-filter">
      <div class="flex-between ip-flex-start ip-flex-col ip-col-reverse">
        <div>
          <div class="field-label">{{'LEADS.filter-by' | translate}}</div>
          <ul class="d-flex flex-wrap">
            <li *ngFor="let state of topFilters">
              <div name="state" class="px-20 py-6 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer"
                id="clkLeadsCurrent{{state?.displayName}}" data-automate-id="clkLeadsCurrent{{state?.displayName}}"
                [ngClass]="state.enumValue == getFormValue('ProspectVisiblity') ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'"
                (click)="onDataFilterChange(state);trackerFeatures(state.displayName)">{{state?.displayName}}</div>
            </li>
          </ul>
        </div>
        <div class="filters-grid d-flex pl-0">
          <div class="dropdown-date-picker d-flex rounded">
            <div class="bg-white rounded-start manage-select datefilter-scroll">
              <ng-select [virtualScroll]="true" placeholder="{{'GLOBAL.all'| translate}}" [searchable]="false"
                ResizableDropdown class="lead-date ip-max-w-80px min-w-60" [(ngModel)]="dateType"
                (change)="dateChange()">
                <ng-option name="dateType" ngDefaultControl *ngFor="let dType of dateTypeList"
                  [value]="dType">{{dType}}</ng-option>
              </ng-select>
            </div>
            <div class="date-picker align-center py-4 rounded-end" id="leadsAppointmentDate"
              data-automate-id="leadsAppointmentDate">
              <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt1"></span>
              <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
                class="pl-20 ph-pl-12 text-large ph-w-150px" placeholder="ex. 5-03-2025 - 14-03-2025"
                (ngModelChange)="filterDate = $event; dateChange()" [ngModel]="filterDate" />
              <owl-date-time [pickerType]="'calendar'" #dt1
                (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
            </div>
          </div>
          <div *ngIf="filterDate[0]" class="bg-coal align-center cursor-pointer px-8 br-6 ml-10"
            (click)="onResetDateFilter()">
            <span class="ic-refresh ic-white"></span>
          </div>
        </div>
      </div>
    </div>
    <form [formGroup]="advanceFilterForm">
      <div class="d-flex flex-wrap ng-select-sm">
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">{{'GLOBAL.status' | translate}}</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="statusList" [multiple]="true" [closeOnSelect]="false"
              ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id"
              formControlName="StatusIds">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected">
                  <span class="checkmark"></span><span class="text-truncate-1 break-all">{{item.displayName}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">{{'INTEGRATION.agency-name' | translate}}</div>
          <div class="mr-20">
            <ng-select [ngClass]="{ 'pe-none blinking': isAgencyNameListLoading }" [virtualScroll]="true"
              ResizableDropdown [items]="agencyNameList" [multiple]="true" [closeOnSelect]="false"
              placeholder="{{'GLOBAL.select' | translate}}" bindLabel="item" bindValue="item"
              formControlName="AgencyNames">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Channel Partner Name</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" *ngIf="!isChannelPartnerListLoading else fieldLoader" ResizableDropdown
              [items]="channelPartnerList" [multiple]="true" [closeOnSelect]="false"
              placeholder="{{'GLOBAL.select' | translate}}" bindLabel="item" bindValue="item"
              formControlName="ChannelPartnerNames">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Campaign Name</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" *ngIf="!isCampaignListLoading else fieldLoader" ResizableDropdown
              [items]="campaignList" [multiple]="true" [closeOnSelect]="false"
              placeholder="{{'GLOBAL.select' | translate}}" bindLabel="item" bindValue="item"
              formControlName="CampaignNames">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">{{'LEADS.profession'| translate}}</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="professions" [multiple]="true" [closeOnSelect]="false"
              ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" formControlName="Profession"
              bindValue="value" bindLabel="label">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container">
                  <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                    [checked]="item$.selected">
                  <span class="checkmark"></span><span class="text-truncate-1 break-all">{{item.label}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Nationality</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" *ngIf="!nationalitiesIsLoading else fieldLoader" [items]="nationalities"
                ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" formControlName="Nationality">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
        </ng-container>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Excel Sheet</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [multiple]="false"
              [ngClass]="{ 'pe-none blinking': isUploadTypeNameListIsLoading }" [closeOnSelect]="true" ResizableDropdown
              placeholder="{{'GLOBAL.select' | translate}}" formControlName="UploadTypeName">
              <ng-option *ngFor="let file of uploadTypeList" [value]="file">{{file}}</ng-option>
            </ng-select>
          </div>
        </div>

        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Possession</div>
          <div class="mr-20">
            <app-possession-filter #possessionFilter [initialPossessionType]="getFormValue('PossesionType')"
              [initialFromPossessionDate]="getFormValue('FromPossesionDate')"
              [initialToPossessionDate]="getFormValue('ToPossesionDate')"
              [userTimeZoneOffset]="userData?.timeZoneInfo?.baseUTcOffset"
              (possessionFilterChange)="onPossessionFilterChange($event)">
            </app-possession-filter>
          </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Gender</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [multiple]="true" [closeOnSelect]="false" ResizableDropdown
              placeholder="Select Gender" [items]="genders" bindLabel="displayName" bindValue="id"
              formControlName="GenderTypes">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item.displayName}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Date of Birth</div>
          <div class="mr-20">
            <div class="form-group">
              <input type="text" class="h-32" formControlName="DateOfBirth" placeholder="23/04/2002"
                [owlDateTimeTrigger]="dt2" [owlDateTime]="dt2" [max]="maxDate" />
              <owl-date-time #dt2 (afterPickerOpen)="onPickerOpened(currentDate)"
                [pickerType]="'calendar'"></owl-date-time>
            </div>
          </div>
        </div>


        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Marital Status</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [multiple]="true" [closeOnSelect]="false" ResizableDropdown
              placeholder="Select Marital Status" [items]="maritalStatus" bindLabel="name" bindValue="value"
              formControlName="MaritalStatuses">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item.name}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>



      </div>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Assign & Source:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="justify-between align-end mr-20">
              <div class="field-label">{{'GLOBAL.assigned-to'| translate}}</div>
              <label class="checkbox-container mb-4">
                <input type="checkbox" formControlName="IsWithTeam">
                <span class="checkmark"></span>{{'DASHBOARD.with-team' | translate}}
              </label>
            </div>
            <div class="mr-20 position-relative">
              <ng-select [virtualScroll]="true" [items]="canViewAllUsers ? allUsers : reportees" [multiple]="true"
                [closeOnSelect]="false" ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                bindLabel="fullName" bindValue="id"
                [ngClass]="{'pe-none blinking': reporteesIsLoading || isReassignmentIsLoading}"
                formControlName="AssignTo">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.firstName}}
                        {{item.lastName}} </span></div><span class="text-disabled" *ngIf="!item.isActive">( Disabled
                      )</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.assigned' | translate}} {{'GLOBAL.from' | translate}}</div>
            <div class="mr-20 position-relative">
              <ng-select [virtualScroll]="true" [clearSearchOnAdd]="true" [items]="allUsers" [multiple]="true"
                ResizableDropdown [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                bindLabel="fullName" bindValue="id" formControlName="AssignedFromIds">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.firstName}}
                        {{item.lastName}} </span></div><span class="text-disabled" *ngIf="!item.isActive">( Disabled
                      )</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.source' | translate}}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="sourceList" [multiple]="true" [closeOnSelect]="false"
                ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id"
                formControlName="SourceIds" (change)="updateSubSource()">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected">
                    <span class="checkmark"></span><span class="text-truncate-1 break-all">{{item.displayName}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.sub-source' | translate}}</div>
            <div class="mr-20">
              <ng-select [ngClass]="{ 'pe-none blinking': isSubSourceListLoading }" [virtualScroll]="true"
                ResizableDropdown [items]="subSourceList" [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" formControlName="SubSources">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container" title="{{item}}"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
        </div>
      </fieldset>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Budget & Area:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'GLOBAL.min' | translate}} {{'LABEL.budget' | translate}}</div>
            <div class="w-100 align-center">
              <div class="w-70 position-relative no-input-validation input-sm">
                <form-errors-wrapper>
                  <div class="w-100 d-flex">
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbers($event)" (input)="minBudgetCheck()"
                        formControlName="FromMinBudget" min="0" id="inpFromMinBudget"
                        data-automate-id="inpFromMinBudget" placeholder="ex. 123">
                    </div>
                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbers($event)" (input)="minBudgetCheck()"
                        formControlName="ToMinBudget" id="inpToMinBudget" min="0" data-automate-id="inpToMinBudget"
                        placeholder="ex. 123">
                    </div>
                  </div>
                </form-errors-wrapper>
                <div *ngIf="getFormValue('FromMinBudget')" class="position-absolute left-10 top-32">
                  <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                    formatBudget(getFormValue('FromMinBudget'),getFormValue('Currency') ||
                    defaultCurrency)}}</span>
                </div>
                <div *ngIf="getFormValue('ToMinBudget')" class="position-absolute right-10 top-32">
                  <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                    formatBudget(getFormValue('ToMinBudget'),getFormValue('Currency') ||
                    defaultCurrency)}}</span>
                </div>
              </div>
              <div class="text-xs mt-60 text-red fw-semi-bold position-absolute"
                *ngIf="( getFormValue('FromMinBudget') && getFormValue('ToMinBudget')) && !minBudgetValidation">
                {{'LEADS.budget-validation' | translate}}</div>
              <div class="w-30 ml-8 mr-20">
                <form-errors-wrapper label="Currency">
                  <ng-container *ngIf="dataCurrencyList?.length > 1; else showCurrencySymbol">
                    <ng-select [virtualScroll]="true" formControlName="Currency" placeholder="ex.INR"
                      [items]="dataCurrencyList" class="manage-dropdown" ResizableDropdown></ng-select>
                  </ng-container>
                  <ng-template #showCurrencySymbol>
                    <h5 class="rupees px-12 py-4 fw-600 m-4">{{ defaultCurrency }}</h5>
                  </ng-template>
                </form-errors-wrapper>
              </div>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'GLOBAL.max' | translate}} {{'LABEL.budget' | translate}}</div>
            <div class="w-100 align-center">
              <div class="w-70 position-relative no-input-validation input-sm">
                <form-errors-wrapper>
                  <div class="w-100 d-flex">
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbers($event)" (input)="maxBudgetCheck()"
                        formControlName="FromMaxBudget" min="0" id="inpFromMaxBudget"
                        data-automate-id="inpFromMaxBudget" placeholder="ex. 123">
                    </div>
                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbers($event)" (input)="maxBudgetCheck()"
                        formControlName="ToMaxBudget" id="inpToMaxBudget" min="0" data-automate-id="inpToMaxBudget"
                        placeholder="ex. 123">
                    </div>
                  </div>
                </form-errors-wrapper>
                <div *ngIf="getFormValue('FromMaxBudget')" class="position-absolute left-10 top-32">
                  <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                    formatBudget(getFormValue('FromMaxBudget'),getFormValue('Currency') ||
                    defaultCurrency)}}</span>
                </div>
                <div *ngIf="getFormValue('ToMaxBudget')" class="position-absolute right-10 top-32">
                  <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                    formatBudget(getFormValue('ToMaxBudget'),getFormValue('Currency') ||
                    defaultCurrency)}}</span>
                </div>
              </div>
              <div class="text-xs mt-60 text-red fw-semi-bold position-absolute"
                *ngIf="( getFormValue('FromMaxBudget') && getFormValue('ToMaxBudget')) && !maxBudgetValidation">
                {{'LEADS.budget-validation' | translate}}</div>
              <div class="w-30 ml-8 mr-20">
                <form-errors-wrapper label="Currency">
                  <ng-container *ngIf="dataCurrencyList?.length > 1; else showCurrencySymbol">
                    <ng-select [virtualScroll]="true" formControlName="Currency" placeholder="ex.INR"
                      [items]="dataCurrencyList" class="manage-dropdown" ResizableDropdown></ng-select>
                  </ng-container>
                  <ng-template #showCurrencySymbol>
                    <h5 class="rupees px-12 py-4 fw-600 m-4">{{ defaultCurrency }}</h5>
                  </ng-template>
                </form-errors-wrapper>
              </div>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Carpet Area</div>
            <div class="w-100 align-center">
              <div class="w-60pr no-input-validation input-sm">
                <form-errors-wrapper>
                  <div class="w-100 d-flex">
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                        (input)="validateCarpetArea()" formControlName="MinCarpetArea" min="0" id="inpMinCarpetArea"
                        data-automate-id="inpMinCarpetArea" placeholder="ex. 123">
                    </div>
                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                        (input)="validateCarpetArea()" formControlName="MaxCarpetArea" min="0" id="inpMaxCarpetArea"
                        data-automate-id="inpMaxCarpetArea" placeholder="ex. 123">
                    </div>
                  </div>
                </form-errors-wrapper>
              </div>
              <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                *ngIf="( getFormValue('MinCarpetArea') && getFormValue('MaxCarpetArea')) && !carpetAreaValidations ">
                {{'PROPERTY.area-validation' | translate}}</div>
              <div class="w-40pr ml-8 mr-20">
                <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                  <ng-select [virtualScroll]="true" [items]="areaSizeUnits" ResizableDropdown
                    formControlName="CarpetAreaUnitId" placeholder="ex. sq.feet." bindLabel="unit" bindValue="id"
                    [items]="areaSizeUnits"></ng-select>
                </form-errors-wrapper>
              </div>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Built-Up Area</div>
            <div class="w-100 align-center">
              <div class="w-60pr no-input-validation input-sm">
                <form-errors-wrapper>
                  <div class="w-100 d-flex">
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                        (input)="validateBuildUpArea()" formControlName="MinBuiltUpArea" min="0" id="inpMinBuitUpArea"
                        data-automate-id="inpPropSize" placeholder="ex. 123">
                    </div>
                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                        (input)="validateBuildUpArea()" formControlName="MaxBuiltUpArea" min="0" id="inpPropSize"
                        data-automate-id="inpPropSize" placeholder="ex. 123">
                    </div>
                  </div>
                </form-errors-wrapper>
              </div>
              <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                *ngIf="( getFormValue('MinBuiltUpArea') && getFormValue('MaxBuiltUpArea')) && !buildUpAreaValidations ">
                {{'PROPERTY.area-validation' | translate}}</div>
              <div class="w-40pr ml-8 mr-20">
                <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                  <ng-select [virtualScroll]="true" formControlName="BuiltUpAreaUnitId" tabindex="4"
                    placeholder="ex. sq. feet." [items]="areaSizeUnits"
                    [ngClass]="{'pe-none blinking': isAreaSizeUnitsLoading}" bindValue="id" bindLabel="unit"
                    ResizableDropdown></ng-select>
                </form-errors-wrapper>
              </div>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Saleable Area</div>
            <div class="w-100 align-center">
              <div class="w-60pr no-input-validation input-sm">
                <form-errors-wrapper>
                  <div class="w-100 d-flex">
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                        (input)="saleableAreaValidation()" [max]="getFormValue('MaxSaleableArea')" min="0"
                        formControlName="MinSaleableArea" id="inpMinSaleableArea" data-automate-id="inpMinSaleableArea"
                        placeholder="ex. 123">
                    </div>
                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                        (input)="saleableAreaValidation()" formControlName="MaxSaleableArea" min="0"
                        id="inpMaxSaleableArea" data-automate-id="inpMaxSaleableArea" placeholder="ex. 123">
                    </div>
                  </div>
                </form-errors-wrapper>
              </div>
              <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                *ngIf="( getFormValue('MinSaleableArea') && getFormValue('MaxSaleableArea')) && !saleableValidation">
                {{'PROPERTY.area-validation' | translate}}</div>
              <div class="w-40pr ml-8 mr-20">
                <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                  <ng-select [virtualScroll]="true" formControlName="SaleableAreaUnitId" tabindex="4"
                    placeholder="ex. sq. feet." [items]="areaSizeUnits"
                    [ngClass]="{'pe-none blinking': isAreaSizeUnitsLoading}" bindValue="id" bindLabel="unit"
                    ResizableDropdown></ng-select>
                </form-errors-wrapper>
              </div>
            </div>
          </div>
          <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
            <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Property Area</div>
              <div class="w-100 align-center">
                <div class="w-60pr no-input-validation input-sm">
                  <form-errors-wrapper>
                    <div class="w-100 d-flex">
                      <div class="w-50">
                        <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                          (input)="propertyAreaValidation()" [max]="getFormValue('MaxPropertyArea')" min="0"
                          formControlName="MinPropertyArea" id="inpMinPropertyArea"
                          data-automate-id="inpMinPropertyArea" placeholder="ex. 123">
                      </div>
                      <h6 class="text-sm text-mud align-center m-4">To</h6>
                      <div class="w-50">
                        <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                          (input)="propertyAreaValidation()" formControlName="MaxPropertyArea" min="0"
                          id="inpMaxPropertyArea" data-automate-id="inpMaxPropertyArea" placeholder="ex. 123">
                      </div>
                    </div>
                  </form-errors-wrapper>
                </div>
                <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                  *ngIf="( getFormValue('MinPropertyArea') && getFormValue('MaxPropertyArea')) && !areaValidation">
                  {{'PROPERTY.area-validation' | translate}}</div>
                <div class="w-40pr ml-8 mr-20">
                  <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                    <ng-select [virtualScroll]="true" formControlName="PropertyAreaUnitId" tabindex="4"
                      placeholder="ex. sq. feet." [items]="areaSizeUnits"
                      [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id" bindLabel="unit"
                      ResizableDropdown></ng-select>
                  </form-errors-wrapper>
                </div>
              </div>
            </div>
            <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Net Area</div>
              <div class="w-100 align-center">
                <div class="w-60pr no-input-validation input-sm">
                  <form-errors-wrapper>
                    <div class="w-100 d-flex">
                      <div class="w-50">
                        <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                          (input)="netAreaValidation()" [max]="getFormValue('MaxNetArea')" min="0"
                          formControlName="MinNetArea" id="inpMinNetArea" data-automate-id="inpMinNetArea"
                          placeholder="ex. 123">
                      </div>
                      <h6 class="text-sm text-mud align-center m-4">To</h6>
                      <div class="w-50">
                        <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                          (input)="netAreaValidation()" formControlName="MaxNetArea" min="0" id="inpMaxNetArea"
                          data-automate-id="inpMaxNetArea" placeholder="ex. 123">
                      </div>
                    </div>
                  </form-errors-wrapper>
                </div>
                <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                  *ngIf="( getFormValue('MinNetArea') && getFormValue('MaxNetArea')) && !netValidation">
                  {{'PROPERTY.area-validation' | translate}}</div>
                <div class="w-40pr ml-8 mr-20">
                  <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                    <ng-select [virtualScroll]="true" formControlName="NetAreaUnitId" tabindex="4"
                      placeholder="ex. sq. feet." [items]="areaSizeUnits"
                      [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id" bindLabel="unit"
                      ResizableDropdown></ng-select>
                  </form-errors-wrapper>
                </div>
              </div>
            </div>
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Unit Number/Name</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="unitNames"
                  [ngClass]="{ 'pe-none blinking': isDataUnitNameLoading }" ResizableDropdown [multiple]="true"
                  [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" formControlName="UnitNames">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
          </ng-container>
        </div>
      </fieldset>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Location:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LOCATION.location'| translate}}</div>
            <div class="mr-20">
              <ng-select [ngClass]="{ 'pe-none blinking': isLocationLoading }" [virtualScroll]="true"
                [items]="locations" ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" formControlName="Locations">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Locality</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" *ngIf="!localitesIsLoading else fieldLoader" [items]="localities"
                ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" formControlName="Localities">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Sub-Community</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" *ngIf="!subCommunitiesIsLoading else fieldLoader"
                  [items]="subCommunities" ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                  placeholder="{{'GLOBAL.select' | translate}}" formControlName="SubCommunities">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Community</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" *ngIf="!communitiesIsLoading else fieldLoader" [items]="communities"
                  ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                  placeholder="{{'GLOBAL.select' | translate}}" formControlName="Communities">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Tower Name</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" *ngIf="!towerNamesIsLoading else fieldLoader" [items]="towerNames"
                  ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                  placeholder="{{'GLOBAL.select' | translate}}" formControlName="TowerNames">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
          </ng-container>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">City</div>
            <div class="mr-20">
              <ng-select [ngClass]="{ 'pe-none blinking': isCitiesLoading }" [virtualScroll]="true" [items]="cities"
                ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" formControlName="Cities">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">State</div>
            <div class="mr-20">
              <ng-select [ngClass]="{ 'pe-none blinking': statesIsLoading }" [virtualScroll]="true" [items]="states"
                [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                placeholder="{{'GLOBAL.select' | translate}}" formControlName="States">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Country</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" *ngIf="!countriesIsLoading else fieldLoader" [items]="countries"
                ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" formControlName="Countries">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <!-- <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Postal Code</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" *ngIf="!postalCodeIsLoading else fieldLoader" [items]="postalCodeList"
                ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" formControlName="PostalCodes">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div> -->
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Country Code</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="countryCodes"
                [ngClass]="{ 'pe-none blinking': isDataCountryCodeLoading }" ResizableDropdown [multiple]="true"
                [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" formControlName="CountryCode">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Alt Country Code</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="altCountryCodes"
                [ngClass]="{ 'pe-none blinking': isDataAltCountryCodeLoading }" ResizableDropdown [multiple]="true"
                [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" formControlName="AltCountryCode">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
        </div>
      </fieldset>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Enquiry:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEAD_FORM.enquired-for'| translate}}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="enquiryType" [multiple]="true" [closeOnSelect]="false"
                [bindLabel]="'type'" [bindValue]="'type'" ResizableDropdown
                placeholder="{{'GLOBAL.select' | translate}}" formControlName="EnquiryTypes">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container">
                    <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                      [checked]="item$.selected">
                    <span class="checkmark"></span>
                    <span class="text-truncate-1 break-all">{{item.type}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Purpose</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="purposeList" [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" formControlName="Purposes" bindLabel="displayName"
                bindValue="displayName" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item.displayName}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'SIDEBAR.project'| translate}}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="projectList" [multiple]="true" [closeOnSelect]="false"
                ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="id"
                formControlName="Projects">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LABEL.property'| translate}}</div>
            <div class="mr-20">
              <ng-select [ngClass]="{ 'pe-none blinking': isPropertyListLoading }" [virtualScroll]="true"
                ResizableDropdown [items]="propertyList" [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" bindLabel="id" formControlName="Properties">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LABEL.property'| translate}} {{'LABEL.type'| translate}}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="propertyType" [multiple]="true" [closeOnSelect]="false"
                ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindValue="id" bindLabel="displayName"
                formControlName="PropertyType">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item?.displayName}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LABEL.property'| translate}} {{'LABEL.sub-type'| translate}}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="propertySubTypes" [multiple]="true" [closeOnSelect]="false"
                ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id"
                formControlName="PropertySubType">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item.displayName}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <ng-container *ngIf="!globalSettingsData?.isCustomLeadFormEnabled">
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">{{'PROPERTY.bhk'| translate}}</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="noOfBhk" [multiple]="true" [closeOnSelect]="false"
                  ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" formControlName="NoOfBHKs">
                  <ng-template ng-label-tmp let-item="item" let-clear="clear">
                    <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                    <span class="ng-value-label">{{getBHKDisplayString(item)}}</span>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span>{{getBHKDisplayString(item)}}
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">{{'PROPERTY.bhk' | translate}} {{'LABEL.type'| translate}}</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="bhkType" [multiple]="true" [closeOnSelect]="false"
                  ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" formControlName="BHKTypes">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
            <!-- <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">BR</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="numbers10" [multiple]="true" [closeOnSelect]="false"
                  ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="display" bindValue="value"
                  formControlName="NoOfBHKs">
                  <ng-template ng-label-tmp let-item="item" let-clear="clear">
                    <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                    <span class="ng-value-label">{{getBRDisplayString(item?.display)}}</span>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span>{{getBRDisplayString(item?.display)}}
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div> -->
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Baths</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="numbers10" [multiple]="true" [closeOnSelect]="false"
                  ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" formControlName="Baths"
                  bindLabel="display" bindValue="value">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span>{{item?.display}}
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Beds</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="numbers" [multiple]="true" [closeOnSelect]="false"
                  ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" formControlName="Beds"
                  bindLabel="display" bindValue="value">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span>{{item?.display}}
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Furnish Status</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="furnishStatus" [multiple]="true" [closeOnSelect]="false"
                  placeholder="{{'GLOBAL.select' | translate}}" formControlName="Furnished" bindLabel="value"
                  bindValue="dispName" ResizableDropdown>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.dispName}}</span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Preferred Floor</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="floorOptions" [multiple]="true" [closeOnSelect]="false"
                  placeholder="{{'GLOBAL.select' | translate}}" formControlName="Floors" ResizableDropdown>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Offering Type</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="offerType" [multiple]="true" [closeOnSelect]="false"
                  placeholder="{{'GLOBAL.select' | translate}}" formControlName="OfferTypes" bindLabel="displayName"
                  bindValue="displayName" ResizableDropdown>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.displayName}}</span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
              <div class="field-label">Cluster Name</div>
              <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="clusterNames"
                  [ngClass]="{ 'pe-none blinking': isDataClusterLoading }" ResizableDropdown [multiple]="true"
                  [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" formControlName="ClusterName">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
          </ng-container>
        </div>
      </fieldset>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Referral:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEAD_FORM.referral-name' | translate}}</div>
            <div class="mr-20 input-sm">
              <form-errors-wrapper>
                <input type="text" class="w-100" placeholder="enter referral name"
                  formControlName="ReferralName"></form-errors-wrapper>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">
              {{'LEAD_FORM.referral-phone-no' | translate}}</div>
            <div class="mr-20 input-sm"><form-errors-wrapper>
                <input type="number" class="w-100" placeholder="enter referral phone no"
                  formControlName="ReferralContactNo"></form-errors-wrapper>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Referral Email</div>
            <div class="mr-20 input-sm">
              <form-errors-wrapper>
                <input type="text" class="w-100" placeholder="enter referral email"
                  formControlName="ReferralEmail"></form-errors-wrapper>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Landline Number</div>
            <div class="mr-20 input-sm">
              <form-errors-wrapper>
                <ng-select [virtualScroll]="true" *ngIf="!landLineIsLoading else fieldLoader" [items]="landLineList"
                  [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                  placeholder="{{'GLOBAL.select' | translate}}" formControlName="LandLine">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
              </form-errors-wrapper>
            </div>
          </div>

        </div>
      </fieldset>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">User:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.sourcing-manager' | translate}}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" *ngIf="!isReassignmentIsLoading else fieldLoader" ResizableDropdown
                [items]="allUsers" [multiple]="true" [closeOnSelect]="false" [dropdownPosition]="'top'"
                placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id"
                formControlName="SourcingManagers">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.firstName}}
                        {{item.lastName}}</span></div>
                    <span class="text-disabled" *ngIf="!item.isActive">( Disabled)</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.closing-manager' | translate}}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" *ngIf="!isReassignmentIsLoading else fieldLoader" ResizableDropdown
                [items]="allUsers" [multiple]="true" [closeOnSelect]="false" [dropdownPosition]="'top'"
                placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id"
                formControlName="ClosingManagers">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' +
                    item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.firstName}}
                        {{item.lastName}}</span></div>
                    <span class="text-disabled" *ngIf="!item.isActive">( Disabled)</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.created-by' | translate}}</div>
            <div class="mr-20 position-relative">
              <ng-select [virtualScroll]="true" [clearSearchOnAdd]="true" [items]="allUsers" [multiple]="true"
                ResizableDropdown [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                bindLabel="fullName" bindValue="id" formControlName="CreatedByIds">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.firstName}}
                        {{item.lastName}} </span>
                    </div><span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.qualified-by' | translate}}</div>
            <div class="mr-20 position-relative">
              <ng-select [virtualScroll]="true" [clearSearchOnAdd]="true" [items]="allUsers" [multiple]="true"
                ResizableDropdown [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                bindLabel="fullName" bindValue="id" formControlName="QualifiedByIds">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.firstName}}
                        {{item.lastName}} </span>
                    </div><span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.last-modified-by' | translate}}</div>
            <div class="mr-20 position-relative">
              <ng-select [virtualScroll]="true" [clearSearchOnAdd]="true" [items]="allUsers" [multiple]="true"
                ResizableDropdown [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                bindLabel="fullName" bindValue="id" formControlName="LastModifiedByIds">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.firstName}}
                        {{item.lastName}} </span>
                    </div><span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.converted-by' | translate}}</div>
            <div class="mr-20 position-relative">
              <ng-select [virtualScroll]="true" [clearSearchOnAdd]="true" [items]="allUsers" [multiple]="true"
                ResizableDropdown [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                bindLabel="fullName" bindValue="id" formControlName="ConvertedByIds">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.firstName}}
                        {{item.lastName}} </span>
                    </div><span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'DASHBOARD.deleted' | translate}} {{'GLOBAL.by' | translate}}</div>
            <div class="mr-20 position-relative">
              <ng-select [virtualScroll]="true" [clearSearchOnAdd]="true" [items]="allUsers" [multiple]="true"
                ResizableDropdown [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                bindLabel="fullName" bindValue="id" formControlName="DeletedByIds">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.firstName}}
                        {{item.lastName}} </span>
                    </div><span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.restored-by' | translate}}</div>
            <div class="mr-20 position-relative">
              <ng-select [virtualScroll]="true" [clearSearchOnAdd]="true" [items]="allUsers" [multiple]="true"
                ResizableDropdown [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                bindLabel="fullName" bindValue="id" formControlName="RestoredByIds">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item.firstName}}
                        {{item.lastName}} </span>
                    </div><span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
        </div>
      </fieldset>
    </form>
  </div>
  <div class="flex-end py-20">
    <u class="mr-20 fw-semi-bold text-mud cursor-pointer"
      (click)="modalService.hide();trackingService.trackFeature('Web.Data.Filter.Cancel.Click')">{{'BUTTONS.cancel' |
      translate }}</u>
    <div class="btn-gray mr-20"
      (click)="onClearAllFilters();trackingService.trackFeature('Web.Data.Filter.ResetFilter.Click')">{{ 'GLOBAL.reset'
      | translate
      }}</div>
    <div class="btn-coal" (click)="applyAdvancedFilter()">{{ 'GLOBAL.search' | translate
      }}</div>
  </div>
</div>

<ng-template #fieldLoader>
  <ng-select [virtualScroll]="true" class="pe-none blinking"></ng-select>
</ng-template>