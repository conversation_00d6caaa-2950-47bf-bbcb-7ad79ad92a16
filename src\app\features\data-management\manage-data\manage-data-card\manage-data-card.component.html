<div class="w-100 bg-brick-pattern" *ngIf="isMobileView && showWebUI"
  [ngClass]="showWebUI ? 'hidden-phone' : 'hidden-web'">
  <div class="pt-10 px-24 pr-10">
    <div class="align-center">
      <div class="align-center flex-grow-1 no-validation p-8 bg-white border-gray br-8">
        <input type="text" (keydown.enter)="emitEvent(updateSearchTerm, searchTerm);" placeholder="type to search"
          [(ngModel)]="searchTerm" class="border-0 outline-0 w-100" />
        <span class="icon ic-search ic-large ic-slate-90 cursor-pointer"
          (click)="emitEvent(updateSearchTerm, searchTerm);"></span>
      </div>
      <div class="border-gray br-8 p-8 bg-white mx-10 cursor-pointer" (click)="emitEvent(openAdvFiltersModal)">
        <span class="icon ic-right-left ic-sm ic-slate-90"></span>
      </div>
    </div>
    <div class="bg-white p-4 tb-w-100-40 w-100-190 ph-w-100-50 mt-10" *ngIf="showFilters">
      <div class="bg-secondary flex-between">
        <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
          <div class="d-flex" *ngFor="let filter of filtersPayload | keyvalue">
            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
              *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
              {{
                ((dataFiltersKeyLabel[filter.key]
                == 'NoOfBHKs' || filter.key == 'NoOfBHKs') &&
                globalSettingsData?.isCustomLeadFormEnabled) ? 'BR' :
                dataFiltersKeyLabel[filter.key] || filter.key
                }}: {{ filter.key === 'NoOfBHKs' && !globalSettingsData?.isCustomLeadFormEnabled ?
                getBHKDisplayString(value) :
                filter.key === 'NoOfBHKs' && globalSettingsData?.isCustomLeadFormEnabled ? getBRDisplayString(value) :
                filter.key === 'Beds'? getBedsDisplay(value) :
                filter.key === 'AssignTo' ? getUserName(value) :
                filter.key === 'AssignedFromIds' ? getUserName(value) :
                filter.key === 'LastModifiedByIds' ? getUserName(value) :
                filter.key === 'ConvertedByIds' ? getUserName(value) :
                filter.key === 'QualifiedByIds' ? getUserName(value) :
                filter.key === 'CreatedByIds' ? getUserName(value) :
                filter.key === 'DeletedByIds' ? getUserName(value) :
                filter.key === 'RestoredByIds' ? getUserName(value) :
                filter.key === 'SourceIds' ? sourceIdMap?.[value] :
                filter.key === 'StatusIds' ? statusIdMap?.[value] :
                filter.key === 'PropertyType' ? getPropertyTypeName(value):
                filter.key === 'PropertySubType' ? getPropertySubTypeName(value):
                filter.key === 'SourcingManager' ? getUserName(value) :
                filter.key === 'ClosingManager' ? getUserName(value) :
                filter.key === 'CarpetArea' ? getArea(value,filtersPayload?.CarpetAreaUnitId) :
                filter.key === 'BuiltUpArea' ? getArea(value,filtersPayload?.BuiltUpAreaUnitId) :
                filter.key === 'SaleableArea' ? getArea(value,filtersPayload?.SaleableAreaUnitId) :
                filter.key === 'Profession' ? getProfession(value):
                filter.key === 'Minbudget' ? value?.split(" ")?.[1] :
                filter.key === 'Maxbudget' ? value?.split(" ")?.[1] :
                filter.key === 'Currency' ? value?.split(" ")?.[1] :
                value }}
              <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                (click)="onRemoveFilter(filter.key, value)"></span>
            </div>
          </div>
        </drag-scroll>
        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
          (click)="onClearAllFilters()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}</div>
      </div>
    </div>
  </div>
  <ng-container *ngIf="(cardData?.length && isMobileView && showWebUI) else phLoader">
    <ng-container *ngFor="let data of cardData">
      <div class="bg-white br-4 p-10 mx-24 my-10 fw-semi-bold text-dark-gray" (click)="openPreview(data)">
        <div class="flex-between">
          <div class="align-center"><span *ngIf="data?.enquiry?.prospectSource?.imageURL"><img alt="source"
                [type]="'leadrat'" [appImage]="s3BucketUrl + data?.enquiry?.prospectSource?.imageURL" [height]="15" class="mr-4">
            </span><span class="text-xxs">{{data?.enquiry?.prospectSource?.displayName}}</span></div>
          <span class="status-label-badge" [style.color]="data?.status?.color || '#4B4B4B'"
            [style.backgroundColor]="hexToRgba(data?.status?.color || '#4B4B4B', 0.08)">
            <span class="dot dot-xs mr-6" [style.backgroundColor]="data?.status?.color || '#4B4B4B'"></span>
            {{ data?.status?.displayName }}</span>
        </div>
        <div class="mt-6"><i class="header-6 text-coal text-truncate-1 break-all">{{data?.name}}</i></div>
        <div class="d-flex mt-4">
          <div *ngIf="data?.contactNo"><span class="icon ic-Call ic-xxxs ic-gray mr-4"></span><span
              class="text-sm">{{data.contactNo}}</span></div>
          <div class="align-center" *ngIf="data?.email"><span
              class="icon ic-envelope ic-xxxs ic-gray mr-4 ml-8"></span><span class="text-sm">{{data.email}}</span>
          </div>
        </div>
        <div class="mt-6" *ngIf="data?.assignTo"><span class="text-xs">assign to:</span><i
            class="text-accent-green text-xs">{{ getAssignedToDetails(data?.assignTo, allUsers, true) || ''}}</i></div>
        <div class="my-10 border-bottom"></div>
        <div class="align-center w-100">
          <div class="align-center w-25 text-xs"><span class="icon ic-buliding ic-x-xs ic-gray mr-4"></span><span
              class="text-truncate-1 break-all">
              {{ data?.projects?.length > 0
              ? data?.projects[0]?.name
              : '--'
              }}
            </span>
            <span *ngIf="data.projects.length > 1"> {{ (data.projects.length > 1 ? ' +' +
              (data.projects.length - 1) : '') }}</span>
          </div>
          <div class="border-right h-10 mx-6"></div>
          <div class="align-center w-25 text-xs"><span class="icon ic-cube ic-x-xs ic-gray mr-4"></span> <span
              class="text-truncate-1 break-all">{{ (data?.enquiry?.bhKs !== null) && (data?.enquiry?.bhKs !== 0 ) &&
              (data?.enquiry?.bhKs.length > 0)
              ? getBHKDisplayString(data?.enquiry?.bhKs)
              : '--'
              }}</span></div>
          <div class="border-right h-10 mx-6"></div>
          <div class="text-truncate-1 align-center w-25 text-xs"> <span
              class="icon ic-cash ic-x-xs ic-gray mr-4"></span><span class="text-nowrap"> {{ (data?.enquiry?.upperBudget
              !== null) && (data?.enquiry?.upperBudget !== 0)
              ? formatBudget(data?.enquiry?.upperBudget, data?.enquiry?.currency || defaultCurrency)
              : '--' }}
            </span></div>
          <div class="mx-6 border-right h-10"></div>
          <div class="align-center w-25 text-xs">
            <span class="icon ic-location-solid ic-x-xs ic-gray mr-4"></span>
            <span class="text-truncate-1 break-all">
              {{ data?.enquiry?.addresses?.length > 0
              ? data?.enquiry?.addresses[0]?.subLocality

              : '--'
              }}
            </span>
            <span *ngIf="data?.enquiry?.addresses?.length > 1"> {{ (data?.enquiry?.addresses?.length > 1 ? ' +' +
              (data?.enquiry?.addresses?.length - 1) : '') }}</span>
          </div>

        </div>
        <div class="my-10 border-bottom"></div>
        <data-actions [data]="data"></data-actions>
      </div>
    </ng-container>
    <div class="h-20px" inView (inView)="onInView($event)"></div>
  </ng-container>
</div>

<ng-template #phLoader>
  <div class="flex-center-col h-100-250">
    <img src="assets/images/layered-cards.svg" alt="No Data found">
    <div class="header-3 fw-600 text-center">No Data Found</div>
  </div>
</ng-template>