import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';

import { DATA_FILTERS_KEY_LABEL } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { MasterAreaUnitType } from 'src/app/core/interfaces/master-data.interface';
import {
  formatBudget,
  getAssignedToDetails,
  getBHKDisplayString,
  hexToRgba,
} from 'src/app/core/utils/common.util';
import {
  FetchAllData,
  UpdateDataFilterPayload,
} from 'src/app/reducers/data/data-management.actions';
import {
  DataManagementFilters,
  getAllData,
} from 'src/app/reducers/data/data-management.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'manage-data-card',
  templateUrl: './manage-data-card.component.html',
})
export class ManageDataCardComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() showWebUI: boolean;
  @Input() searchTermSubject: any;
  @Input() searchTerm: string;
  @Input() showFilters: boolean;
  @Input() filtersPayload: DataManagementFilters;
  @Input() allUsers: any;
  @Input() sourceIdMap: any;
  @Input() propertyType: Array<string>;
  @Input() areaSizeUnits: MasterAreaUnitType[];
  @Input() dataTotalCount: number;
  @Input() getArrayOfFilters: Function;
  @Input() getUserName: Function;
  @Input() getPropertyTypeName: Function;
  @Input() getPropertySubTypeName: Function;
  @Input() getCarpetArea: Function;
  @Input() getProfession: Function;
  @Input() onRemoveFilter: Function;
  @Input() onClearAllFilters: Function;
  globalSettingsData: any;
  defaultCurrency: string = '';
  @Input()
  get isMobileView(): boolean {
    return this._isMobileView;
  }
  set isMobileView(isMobileView: boolean) {
    this.filtersPayload = {
      ...this.filtersPayload,
      PageNumber: 1,
      PageSize: 10,
    };
    this.filterFunction();
    this._isMobileView = isMobileView;
  }
  private _isMobileView: boolean;
  s3BucketUrl: string = env.s3ImageBucketURL;

  @Output() openAdvFiltersModal: EventEmitter<any> =
    new EventEmitter();
  @Output() updateSearchTerm: EventEmitter<string> =
    new EventEmitter();

  hexToRgba: Function = hexToRgba;
  getAssignedToDetails: Function = getAssignedToDetails;
  cardDataPageNumber: number = 1;
  cardData: any[] = [];
  getBHKDisplayString = getBHKDisplayString;
  formatBudget = formatBudget;

  dataFiltersKeyLabel = DATA_FILTERS_KEY_LABEL;

  constructor(private _store: Store<AppState>) { }

  ngOnInit(): void {
    this._store
      .select(getAllData)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.cardData.push(...data);
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
      });
  }

  emitEvent(event: EventEmitter<any>, args?: any) {
    if (event === this.updateSearchTerm) this.cleanCardsData();
    event.emit(args);
  }

  onInView(isVisible: boolean) {
    if (isVisible && this.cardData.length < this.dataTotalCount) {
      this.loadMore();
    }
  }

  loadMore() {
    this.cardDataPageNumber += 1;

    {
      this.filtersPayload = {
        ...this.filtersPayload,
        PageNumber: this.cardDataPageNumber,
      };
    }
    this.filterFunction(false);
  }

  filterFunction(shouldCleanCardData: boolean = true): void {
    if (shouldCleanCardData) this.cleanCardsData();
    this._store.dispatch(new UpdateDataFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchAllData(this.filtersPayload));
  }

  cleanCardsData() {
    this.cardData = [];
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
