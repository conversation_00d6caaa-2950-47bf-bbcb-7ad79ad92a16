<!-- <div class="flex-center h-100-46 bg-white overflow-hidden">
    <div class="position-relative w-100 h-100">
        <ng-lottie [options]="rotate" class="position-absolute w-100"></ng-lottie>
        <div *ngFor="let circle of circles; let i = index" class="circle position-absolute top-50 left-50 br-50"
            [ngStyle]="circle.style">
            <img *ngFor="let img of circle.images" [src]="img.src" alt="" [ngStyle]="img.style">
        </div>
        <div class="flex-center-col h-100 w-100 position-absolute">
            <img src="../../../../assets/images/engage-to-logo.svg" alt="" width="240" />
            <div class="text-largest fw-700 mt-24">Engage</div>
            <div class="text-largest fw-700">Clients Smarter</div>
            <h4 class="text-dark-gray mt-12">Bring the power of next gen WhatsApp</h4>
            <h4 class="text-dark-gray">Business API to {{getAppName()}} CRM</h4>
            <div class="bg-coal br-60 cursor-pointer flex-center text-white px-20 py-12 mt-30" (click)="JoinEngageTo()">
                <h3>Join EngageTo now</h3>
                <span class="icon ic-arrow-left ic-xxs rotate-135 mt-2 ml-4"></span>
            </div>
        </div>
    </div>
</div> -->
<!-- <span class="text-capitalize">{{getAppName()}}</span> -->
<iframe #iframeElement [src]="safeUrl" class="w-100 h-100" (load)="onIframeLoad()">
</iframe>