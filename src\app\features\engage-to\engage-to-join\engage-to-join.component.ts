import {
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  DomSanitizer,
  SafeResourceUrl,
  Title,
} from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getAppName, getTenantName } from 'src/app/core/utils/common.util';
import { getUserProfile } from 'src/app/reducers/teams/teams.reducer';
import { SendEmailForm } from 'src/app/reducers/whatsapp-cloud/whatsapp-cloud.actions';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'engage-to-join',
  templateUrl: './engage-to-join.component.html',
})
export class EngageToJoinComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  getAppName = getAppName;
  rotate: AnimationOptions = {
    path: 'assets/animations/rotate.json',
  };

  circles = [
    {
      style: { width: '450px', height: '450px' },
      images: [
        {
          src: '../../../../assets/images/engageto/like.svg',
          style: { top: '0', left: '50%' },
        },
        {
          src: '../../../../assets/images/engageto/message-32.svg',
          style: { top: '35%', left: '100%' },
        },
        {
          src: '../../../../assets/images/engageto/like-count.svg',
          style: { top: '100%', left: '50%' },
        },
      ],
    },
    {
      style: { width: '750px', height: '750px' },
      images: [
        {
          src: '../../../../assets/images/engageto/smile.svg',
          style: { top: '6%', left: '74%' },
        },
        {
          src: '../../../../assets/images/engageto/rupees.svg',
          style: { top: '50%', left: '100%' },
        },
        {
          src: '../../../../assets/images/engageto/light.svg',
          style: { top: '94%', left: '73%' },
        },
        {
          src: '../../../../assets/images/engageto/bag.svg',
          style: { top: '80%', left: '10%' },
        },
        {
          src: '../../../../assets/images/engageto/message-10.svg',
          style: { top: '55%', left: '1%' },
        },
        {
          src: '../../../../assets/images/engageto/pie-chart.svg',
          style: { top: '22%', left: '9%' },
        },
      ],
    },
    {
      style: { width: '1000px', height: '1000px' },
      images: [
        {
          src: '../../../../assets/images/engageto/whatsapp.svg',
          style: { top: '20%', left: '90%' },
        },
        {
          src: '../../../../assets/images/engageto/check-circle.svg',
          style: { top: '44%', left: '100%' },
        },
        {
          src: '../../../../assets/images/engageto/like.svg',
          style: { top: '79%', left: '91%' },
        },
        {
          src: '../../../../assets/images/engageto/check-circle.svg',
          style: { top: '90%', left: '80%' },
        },
        {
          src: '../../../../assets/images/engageto/building.svg',
          style: { top: '87%', left: '16%' },
        },
        {
          src: '../../../../assets/images/engageto/heart.svg',
          style: { top: '57%', left: '0%' },
        },
        {
          src: '../../../../assets/images/engageto/star-count.svg',
          style: { top: '25%', left: '7%' },
        },
      ],
    },
    {
      style: { width: '1250px', height: '1250px' },
      images: [
        {
          src: '../../../../assets/images/engageto/sparkle.svg',
          style: { top: '21%', left: '91%' },
        },
        {
          src: '../../../../assets/images/engageto/bag.svg',
          style: { top: '50%', left: '100%' },
        },
        {
          src: '../../../../assets/images/engageto/pie-chart.svg',
          style: { top: '69%', left: '96%' },
        },
        {
          src: '../../../../assets/images/engageto/rupees.svg',
          style: { top: '82%', left: '12%' },
        },
        {
          src: '../../../../assets/images/engageto/smile.svg',
          style: { top: '67%', left: '3%' },
        },
        {
          src: '../../../../assets/images/engageto/check-circle.svg',
          style: { top: '33%', left: '3%' },
        },
      ],
    },
    {
      style: { width: '1500px', height: '1500px' },
      images: [
        {
          src: '../../../../assets/images/engageto/heart.svg',
          style: { top: '50%', left: '100%' },
        },
        {
          src: '../../../../assets/images/engageto/bag.svg',
          style: { top: '50%', left: '0%' },
        },
      ],
    },
    {
      style: { width: '1750px', height: '1750px' },
      images: [
        {
          src: '../../../../assets/images/engageto/whatsapp-icon.svg',
          style: { top: '40%', left: '99%' },
        },
        {
          src: '../../../../assets/images/engageto/star-count.svg',
          style: { top: '70%', left: '96%' },
        },
        {
          src: '../../../../assets/images/engageto/sparkle.svg',
          style: { top: '67%', left: '3%' },
        },
        {
          src: '../../../../assets/images/engageto/heart.svg',
          style: { top: '30%', left: '4%' },
        },
      ],
    },
  ];
  userDetails: any;
  safeUrl: SafeResourceUrl;
  engageToUrl: string = environment.engageToURL;
  @ViewChild('iframeElement') iframeElement: ElementRef<HTMLIFrameElement>;

  constructor(
    private store: Store<AppState>,
    private sanitizer: DomSanitizer,
    public metaTitle: Title,
    private headerTitle: HeaderTitleService
  ) { }

  ngOnInit(): void {
    this.metaTitle.setTitle('CRM | EngageTo');
    this.headerTitle.setLangTitle('EngageTo');
    this.store
      .select(getUserProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.userDetails = item;
      });
    this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
      this.engageToUrl
    );
  }

  JoinEngageTo() {
    let payload: any = {
      Sender: '<EMAIL>',
      Subject: 'New Enquiry for EngageTo',
      ContentBody: this.getContent(),
      ToRecipients: [
        // '<EMAIL>',
        // '<EMAIL>',
        // '<EMAIL>',
        // '<EMAIL>',
        // '<EMAIL>',
        // '<EMAIL>',
        '<EMAIL>',
        // '<EMAIL>',
      ],
    };
    this.store.dispatch(new SendEmailForm(payload));
  }

  getContent() {
    const roles = this.userDetails?.userRoles
      ?.filter((item: any) => item.name != 'Default')
      ?.map((role: any) => role.name)
      ?.join(', ');

    const userDetails = JSON.parse(localStorage.getItem('userDetails') || '{}');
    const { given_name, family_name, preferred_username } = userDetails;
    const content = `A Client is interested in "EngageTo" Business API  
  Tenant Name: ${getTenantName()},
  Environment: ${environment.envt} ,
  User Name: ${given_name} ${family_name},
  User ID: ${preferred_username},
  Phone Number: ${this.userDetails?.phoneNumber},
  Role(s): ${roles},
`;
    return content;
  }

  ngAfterViewInit() {
    this.initIframe();
  }

  onIframeLoad() {
    if (this.iframeElement?.nativeElement) {
      const iframe = this.iframeElement.nativeElement;
      iframe.contentWindow?.postMessage(
        {
          tenantId: getTenantName(),
          emailContent: this.getContent(),
        },
        '*'
      );
    }
  }

  initIframe() {
    const iframe = this.iframeElement?.nativeElement;
    if (iframe) {
      iframe.onload = () => {
        setTimeout(() => this.onIframeLoad(), 1000);
      };
      iframe.setAttribute('src', iframe.getAttribute('src')); // Refresh iframe
    }
  }
}

