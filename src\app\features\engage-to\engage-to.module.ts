import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { RouterModule } from '@angular/router';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { DragScrollModule } from 'ngx-drag-scroll';
import { LottieModule } from 'ngx-lottie';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';

import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import {
  routes
} from 'src/app/features/no-auth/no-auth-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { EngageToJoinComponent } from './engage-to-join/engage-to-join.component';
import { EngageToRoutingModule } from './engage-to-routing.module';

@NgModule({
  declarations: [
    EngageToJoinComponent,
    // InViewDirective
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    EngageToRoutingModule,
    SharedModule,
    GoogleMapsModule,
    NgxMatIntlTelInputComponent,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    LottieModule.forRoot({ player: playerFactory }),
    FormsModule,
    ReactiveFormsModule,
    DragScrollModule,
  ],
  exports: [DragScrollModule],
})
export class EngageToModule { }
