<div class="p-16  min-w-350 max-w-350 h-100-60 scrollbar">
    <img [type]="'leadrat'" [appImage]="image" alt="img" />
    <div class="align-center mt-30">
        <span class="icon ic-chevron-left ic-xxs ic-black cursor-pointer mr-10"
            (click)="hideCountryCodePopup();isBulkAssignModel=true"></span>
        <h3 class="fw-700">{{ 'LEADS.assignment' | translate }}</h3>
    </div>
    <div class="bg-light-pearl mt-20 br-6 flex-between break-all bg-profile">
        <div class="flex-column pt-20 pl-10 pb-20">
            <div *ngIf="selectedIntegrations?.length > 0 && !isBulkAssignModel" class="fw-semi-bold fv-sm-caps">
                Account Name(s)
            </div>
            <div *ngIf="isBulkAssignModel" class="fw-semi-bold fv-sm-caps">
                Account Name
            </div>
            <div *ngIf="selectedIntegrations?.length > 0 && !isBulkAssignModel">
                <span class="fw-700 text-small" *ngFor="let name of selectedIntegrations; let last = last">
                    {{ name.accountName }}{{ !last ? ', ' : ' ' }}
                </span>
            </div>
            <div class="fw-700 text-small" *ngIf="isBulkAssignModel">{{ selectedAccountName
                }}</div>
        </div>
    </div>
    <div class="field-label fw-semi-bold"> Country Code
    </div>
    <div class="form-group">
        <ngx-mat-intl-tel-input #contactNoInput [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
            [enableSearch]="true" [formControl]="countryCode" class="no-validation contactNoInput no-number-input"
            placeholder="9133XXXXXX">
        </ngx-mat-intl-tel-input>
    </div>
</div>
<div class="flex-end p-16 box-shadow-20">
    <button class="btn-gray mr-20" (click)="hideCountryCodePopup(); isBulkAssignModel=true">
        {{ 'BUTTONS.cancel' | translate }}</button>
    <button class="btn-coal" (click)="updateCountryCode()">
        {{ 'BUTTONS.save' | translate }}</button>
</div>