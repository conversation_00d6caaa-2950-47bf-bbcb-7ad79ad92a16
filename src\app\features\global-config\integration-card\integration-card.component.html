<div *ngIf="!isSkeleton">
    <div class="border banner-hover py-12 br-10 mr-20 ph-mr-10">
        <div class="px-12">
            <img [type]="'leadrat'" [appImage]="integration?.logo" alt="logo" height="28px" width="28px" class="mr-12">
            <div class="pt-10 text-black-10 fw-600 clear-margin text-truncate">
                {{integration?.displayName}}</div>
            <div (click)="openIntegration(integration?.image, integration?.displayName, integration?.name)"
                class="flex-between connect-hover align-center mt-10 border w-99 py-6 br-6 ph-mr-0 cursor-pointer">
                <div class="text-black-10 fw-semi-bold clear-margin pl-10 ">Connect Now</div>
                <span class="mr-8 icon rotate-90 ic-coal ic-triangle-up ic-xx-xs"></span>
            </div>
        </div>
    </div>
</div>

<div *ngIf="isSkeleton">
    <div class="border banner-hover py-12 br-10 mr-20 ph-mr-10 pe-none blinking">
        <div class="px-12">
            <img [type]="'leadrat'" [appImage]="" height="28px" width="28px" class="bg-grey mr-12">
            <div class="pt-10 text-black-10 fw-600 clear-margin text-truncate">
                <span class="bg-grey">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            </div>
            <div
                class="flex-between connect-hover align-center mt-10 border w-99 py-6 br-6 ph-mr-0 cursor-pointer">
                <div class="text-black-10 fw-semi-bold clear-margin pl-10 bg-grey">
                    <span class="bg-grey">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                </div>
                <span class="mr-8 icon rotate-90 ic-coal ic-triangle-up ic-xx-xs"></span>
            </div>
        </div>
    </div>
</div>