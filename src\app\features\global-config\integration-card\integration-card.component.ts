import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ThirdPartyIntegrationComponent } from '../integration/third-party-integration/third-party-integration.component';
import { BsModalService } from 'ngx-bootstrap/modal';
import { CommonFloorComponent } from '../integration/common-floor/common-floor.component';
import { Router } from '@angular/router';

@Component({
  selector: 'integration-card',
  templateUrl: './integration-card.component.html'
})
export class IntegrationCardComponent {
  @Input() isSkeleton: boolean = false;
  @Input() integration: {
    displayName: string;
    name: string;
    image: string;
    logo: string;
    description: string;
  };
  @Input() functionType: string;
  @Output() connectNowClicked: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private modalService: BsModalService,
    private router: Router,

  ) { }


  // openIntegration(image: string, displayName: string, name: string) {  
  //   if (this.functionType) {
  //     this.connectNowClicked?.emit();
  //     return;
  //   }
  
  //   this.router.navigate([`/global-config/${displayName}`], {
  //     state: {
  //       image: image,
  //       displayName: displayName,
  //       name: name
  //     }
  //   });
    
  // }
  openIntegration(image: string, displayName: string, name: string) {
    if (this.functionType) {
      this.connectNowClicked?.emit();
      return;
    }

    localStorage.setItem('integrationData', JSON.stringify({ image, displayName, name }));

    if (name === 'GoogleCampaign' || displayName === 'Google Campaign') {
      this.router.navigate(['/global-config/google-campaign']);
    } else {
      this.router.navigate(['/global-config/integration']);
    }
  }

  // openCommonFloorIntegration(
  //   image: string,
  //   displayName: string,
  //   name: string,
  //   count: string
  // ) {  
  //   if (this.functionType) {
  //     this.connectNowClicked?.emit();
  //     return;
  //   }
  
  //   localStorage.setItem('integrationData', JSON.stringify({ image, displayName, name, count }));
  
  //   this.router.navigate([`/global-config/integrations`]);
  // }

}
