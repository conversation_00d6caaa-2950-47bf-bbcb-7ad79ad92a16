<div class="p-20 border">
    <div class="align-center">
        <div class="form-check form-check-inline" (click)="selectedFetchType = 'all'">
            <input type="radio" id="inpFetchAll" data-automate-id="inpFetchAll" class="radio-check-input"
                name="fetchType" checked>
            <label class="fw-600 text-secondary cursor-pointer text-large" for="inpFetchAll">
                Fetch all your facebook leads till today</label>
        </div>
    </div>
    <div class="align-center">
        <div class="form-check form-check-inline" (click)="selectedFetchType = 'custom'">
            <input type="radio" id="inpFetchCustom" data-automate-id="inpFetchCustom" class="radio-check-input"
                name="fetchType">
            <label class="fw-600 text-secondary cursor-pointer text-large" for="inpFetchCustom">
                Select date range to fetch facebook leads</label>
        </div>
    </div>
    <div class="form-group ml-30" *ngIf="selectedFetchType == 'custom'">
        <span class="icon ic-calendar ic-sm ic-coal position-absolute right-20 top-12 cursor-pointer"
            [owlDateTimeTrigger]="dt1"></span>
        <input type="text" readonly [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1" [selectMode]="'range'" bsDatepicker
            placeholder="ex. 19-06-2025, 11:49 am - 29-06-2025, 03:49 pm" (ngModelChange)="selectedDate = $event"
            [ngModel]="selectedDate">
        <owl-date-time [hour12Timer]="'true'" #dt1 (afterPickerOpen)="onPickerOpened(currentDate)" [startAt]="currentDate"></owl-date-time>
    </div>
    <div class="flex-end mt-30">
        <button class="btn-gray" id="clkFetchFbCancel" data-automate-id="clkFetchFbCancel" (click)="modalRef.hide()">
            {{ 'BUTTONS.cancel' | translate }}</button>
        <button class="btn-green ml-20" id="clkFetchFb" data-automate-id="clkFetchFb" (click)="fetchFbBulkLeads()">
            {{ 'BUTTONS.fetch' | translate }}</button>
    </div>
</div>