import { Component, EventEmitter } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  isEmptyObject,
  onPickerOpened,
  setTimeZoneDateWithTime,
} from 'src/app/core/utils/common.util';
import { FetchBulkLeadsFromFb } from 'src/app/reducers/Integration/integration.actions';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'bulk-fetch-fb-leads',
  templateUrl: './bulk-fetch-fb-leads.component.html',
})
export class BulkFetchFBLeads {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  selectedFetchType: string = 'all';
  selectedDate: any;
  userData: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
  ) {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        if (!isEmptyObject(data)) {
          this.currentDate = changeCalendar(
            this.userData?.timeZoneInfo?.baseUTcOffset
          );
        }
      });
  }

  fetchFbBulkLeads() {
    this.modalRef.hide();
    let payload: any = {
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset:
        this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      FromDate: setTimeZoneDateWithTime(
        this.selectedDate?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ToDate: setTimeZoneDateWithTime(
        this.selectedDate?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
    };
    this.store.dispatch(new FetchBulkLeadsFromFb(payload));
  }
}
