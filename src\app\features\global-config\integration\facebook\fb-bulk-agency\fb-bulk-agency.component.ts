import { Component, EventEmitter, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { GridApi } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { AgencyName, FetchAgencyNameList } from 'src/app/reducers/Integration/integration.actions';
import { getAgencyNameList } from 'src/app/reducers/Integration/integration.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'fb-bulk-agency',
  templateUrl: './fb-bulk-agency.component.html',
})
export class FbBulkAgencyComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  closeModal: Function;
  gridApi: GridApi;
  isForm: boolean = false;
  fbAccountName: string;
  agencyName: FormControl = new FormControl(null);
  agencyNameList: any;
  agencySource: any;
  Integartionsource: any;

  constructor(
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private modalRef: BsModalRef,
  ) { }

  ngOnInit() {
    const selectAndPipe = (selector: any) =>
      this._store.select(selector).pipe(takeUntil(this.stopper));

    this._store.dispatch(new FetchAgencyNameList());
    selectAndPipe(getAgencyNameList).subscribe((item: any) => {
      this.agencyNameList = item
        .filter((data: any) => data)
        .slice()
        .sort((a: any, b: any) => a.localeCompare(b));
    });
  }

  openConfirmDeleteModal(dataName: string, id: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: dataName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeSelection(id);
        }
      });
    }
  }

  removeSelection(id: string) {
    const node = this.gridApi?.getSelectedNodes()?.filter(
      (dataNodes: any) => dataNodes?.data?.id === id
    );
    this.gridApi?.deselectNode(node?.[0]);
    if (this.gridApi?.getSelectedNodes()?.length <= 0) this.modalService.hide();
  }

  updateBulkAgency(): void {
    let payload: any = {
      accountIds: this.gridApi?.getSelectedNodes()?.map((dataNodes: any) => dataNodes?.data?.id),
      agencyName: this.agencyName.value,
      source: this.Integartionsource,
    };

    this._store.dispatch(new AgencyName(payload));
    this.closeModal();
    this.gridApi?.deselectAll();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
