<form class="h-100vh">
    <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
        <h3 class="fw-semi-bold">Assignment</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="closeModal()"></div>
    </div>
    <img src="../../../../assets/images/integration/facebook.svg" alt="img" class="px-16 pt-16 mb-8" />
    <div class="px-16 pb-16 scrollbar h-100-160">
        <div class="scrollbar table-scrollbar scrollbar ip-w-100-40">
            <table class="table standard-table no-vertical-border">
                <thead>
                    <tr class="w-100 text-nowrap">
                        <th class="w-100px">
                            <span>{{'INTEGRATION.account-name' | translate}}</span>
                        </th>
                        <th class="w-70px">
                            <span>{{ (isForm ? 'INTEGRATION.lead-form' : 'INTEGRATION.ad-name') | translate
                                }}</span>
                        </th>
                        <th class="w-50px">{{ 'GLOBAL.actions' | translate }}</th>
                    </tr>
                </thead>
                <tbody class="text-secondary fw-semi-bold max-h-100-282">
                    <ng-container *ngFor="let dataNode of gridApi?.getSelectedNodes()">
                        <tr>
                            <td class="w-100px">
                                <div class="text-truncate-1">
                                    {{fbAccountName}}
                                </div>
                            </td>
                            <td class="w-70px">
                                <div class="text-truncate-1">
                                    {{(isForm ? dataNode?.data?.name : dataNode?.data?.adName)}}
                                </div>
                            </td>
                            <td class="w-50px">
                                <a (click)="openConfirmDeleteModal(dataNode?.data?.adName, dataNode?.data?.id)"
                                    class="bg-light-red icon-badge">
                                    <span class="icon ic-delete m-auto ic-xxs"></span></a>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
        <div class="p-16">
            <integration-assignment [image]="image" [isBulkAssignModel]="false"
                [selectedAccountName]="selectedAccountName" [selectedIntegrations]="selectedIntegrations"
                [canAllowSecondaryUsers]="canAllowSecondaryUsers" [sameAsPrimaryUsers]="sameAsPrimaryUsers"
                [assignedSecondaryUsers]="assignedSecondaryUsers" [assignedDuplicateUser]="assignedDuplicateUser"
                [assignedPrimaryUsers]="assignedPrimaryUsers" [sameAsSelectedUsers]="sameAsSelectedUsers"
                [assignedUser]="assignedUser" [assignedUserDetails]="assignedUserDetails"
                [updatedIntegrationList]="updatedIntegrationList" [moduleId]="moduleId"
                [canAssignToAny]="canAssignToAny" [allActiveUsers]="allActiveUsers" [activeUsers]="activeUsers"
                [selectedAccountId]="selectedAccountId" [selectedCount]="selectedCount"
                [canEnableAllowDuplicates]="canEnableAllowDuplicates"
                [canEnableAllowSecondaryUsers]="canEnableAllowSecondaryUsers" [allUserList]="allUserList"
                [userList]="userList" [isFbComponent]="false" [isBulkFb]="true" [isAdAccount]="isAdAccount"
                [isFormAccount]="isFormAccount" [selectedAdName]="selectedAdName"
                (isShowAssignModalChanged)="closeModal()" [gridApi]="gridApi"></integration-assignment>
        </div>
        <!-- <div class="flex-end mt-20">
            <button class="btn-gray mr-20" (click)="closeModal()">
                {{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal" (click)="bulkAssignAccount()">
                {{ 'BUTTONS.save' | translate }}</button>
        </div> -->
        <div class="mt-10" *ngIf="assignedUser?.value?.length">
            <div class="d-flex">
                <div class="field-label-underline">{{'GLOBAL.assigned-to'| translate}}
                </div>
            </div>
            <div class="mt-12">
                <div class="flex-between mb-12" *ngFor="let user of assignedUser.value; let i = index">
                    <div class="align-center">
                        <div class="dot dot-xl bg-pearl-90 mr-6">
                            <span class="fw-semi-bold text-normal text-white text-uppercase">{{
                                user ?
                                getAssignedToDetails(user, canAssignToAny ? allUserList
                                :userList)?.firstName[0]
                                +
                                getAssignedToDetails(user, canAssignToAny ? allUserList
                                :userList)?.lastName[0]
                                :
                                '--'}}</span>
                        </div>
                        <div class="fw-semi-bold text-large text-coal">{{getAssignedToDetails(user, canAssignToAny ?
                            allUserList : userList, true) || '--'}}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>