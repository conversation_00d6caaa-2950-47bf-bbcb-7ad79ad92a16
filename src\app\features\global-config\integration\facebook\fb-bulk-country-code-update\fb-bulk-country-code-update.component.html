<form class="h-100vh">
    <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
        <h3 class="fw-semi-bold">Assignment</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="closeModal()"></div>
    </div>
    <img src="../../../../assets/images/integration/facebook.svg" alt="img" class="px-16 pt-16 mb-8" />
    <form autocomplete="off" class="px-16 pb-16 scrollbar h-100-100">
        <div class="scrollbar table-scrollbar">
            <table class="table standard-table no-vertical-border">
                <thead>
                    <tr class="w-100 text-nowrap">
                        <th class="w-100px">
                            <span>{{'INTEGRATION.account-name' | translate}}</span>
                        </th>
                        <th class="w-70px">
                            <span>{{ (isForm ? 'INTEGRATION.lead-form' : 'INTEGRATION.ad-name') | translate }}</span>
                        </th>
                        <th class="w-50px">{{ 'GLOBAL.actions' | translate }}</th>
                    </tr>
                </thead>
                <tbody class="text-secondary fw-semi-bold max-h-100-290">
                    <ng-container *ngFor="let dataNode of gridApi?.getSelectedNodes()">
                        <tr>
                            <td class="w-100px">
                                <div class="text-truncate-1">
                                    {{fbAccountName}}
                                </div>
                            </td>
                            <td class="w-70px">
                                <div class="text-truncate-1">
                                    {{(isForm ? dataNode?.data?.name : dataNode?.data?.adName)}}
                                </div>
                            </td>
                            <td class="w-50px">
                                <a (click)="openConfirmDeleteModal(dataNode?.data?.adName, dataNode?.data?.id)"
                                    class="bg-light-red icon-badge">
                                    <span class="icon ic-delete m-auto ic-xxs"></span></a>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
        <div class="field-label">Country Code</div>
        <div class="form-group">
            <ngx-mat-intl-tel-input #contactNoInput [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                [enableSearch]="true" class="no-validation contactNoInput no-number-input" placeholder="9133XXXXXX">
            </ngx-mat-intl-tel-input>
        </div>
        <div class="flex-end mt-20">
            <button class="btn-gray mr-20" (click)="closeModal()">
                {{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal" (click)="updateBulk()">
                {{ 'BUTTONS.save' | translate }}</button>
        </div>
    </form>
</form>