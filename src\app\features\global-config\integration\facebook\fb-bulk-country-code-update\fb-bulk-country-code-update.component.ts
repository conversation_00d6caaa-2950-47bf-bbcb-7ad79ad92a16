import {
  Component,
  EventEmitter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { GridApi } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';
import { takeUntil } from 'rxjs';

import { LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  updateCountryCodeAssignment
} from 'src/app/reducers/automation/automation.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'fb-bulk-country-code-update',
  templateUrl: './fb-bulk-country-code-update.component.html',
})
export class FbBulkCountryCodeUpdateComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  closeModal: Function;
  gridApi: GridApi;
  isForm: boolean = false;
  fbAccountName: string;
  preferredCountries: any[];
  @ViewChild('contactNoInput') contactNoInput!: NgxMatIntlTelInputComponent;

  constructor(
    public modalService: BsModalService,
    private store: Store<AppState>,
    private modalRef: BsModalRef
  ) { }

  ngOnInit(): void {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.preferredCountries = data.countries.length
          ? [data.countries[0].code.toLowerCase()]
          : ['in'];
      });
  }

  openConfirmDeleteModal(dataName: string, id: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: dataName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeSelection(id);
        }
      });
    }
  }

  removeSelection(id: string) {
    const node = this.gridApi
      ?.getSelectedNodes()
      ?.filter((dataNodes: any) => dataNodes?.data?.id === id);
    this.gridApi?.deselectNode(node?.[0]);
    if (this.gridApi?.getSelectedNodes()?.length <= 0) this.modalService.hide();
  }

  updateBulk(): void {
    let payload: any = {
      ids: this.gridApi
        ?.getSelectedNodes()
        ?.map((dataNodes: any) => dataNodes?.data?.id),
      countryCode: this.contactNoInput?.selectedCountry?.dialCode
        ? '+' + this.contactNoInput.selectedCountry.dialCode
        : null,
      source: LeadSource['Facebook'],
    };
    this.store.dispatch(new updateCountryCodeAssignment(payload));
    this.closeModal();
    this.gridApi?.deselectAll();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
