import {
  Component,
  EventEmitter,
  On<PERSON><PERSON>roy,
  OnInit
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { GridApi } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { updateProjectAssignment } from 'src/app/reducers/automation/automation.actions';
import { FetchProjectIdWithName } from 'src/app/reducers/project/project.action';
import { getProjectsIDWithName } from 'src/app/reducers/project/project.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'fb-bulk-project-update',
  templateUrl: './fb-bulk-project-update.component.html',
})
export class FbBulkProjectUpdateComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  closeModal: Function;
  gridApi: GridApi;
  allProjectList: any;
  isForm: boolean = false;
  fbAccountName: string;

  project: FormControl = new FormControl(null);

  constructor(
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private modalRef: BsModalRef,
  ) { }

  ngOnInit(): void {
    const selectAndPipe = (selector: any) =>
      this._store.select(selector).pipe(takeUntil(this.stopper));

    this._store.dispatch(new FetchProjectIdWithName());
    selectAndPipe(getProjectsIDWithName).subscribe((data: any) => {
      this.allProjectList = data
        ?.filter((data: any) => data.name)
        .slice()
        .sort((a: any, b: any) => a.name.localeCompare(b.name));
    });
  }

  openConfirmDeleteModal(dataName: string, id: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: dataName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeSelection(id);
        }
      });
    }
  }

  removeSelection(id: string) {
    const node = this.gridApi
      ?.getSelectedNodes()
      ?.filter((dataNodes: any) => dataNodes?.data?.id === id);
    this.gridApi?.deselectNode(node?.[0]);
    if (this.gridApi?.getSelectedNodes()?.length <= 0) this.modalService.hide();
  }

  updateBulk(): void {
    let payload: any = {
      ids: this.gridApi
        ?.getSelectedNodes()
        ?.map((dataNodes: any) => dataNodes?.data?.id),
      projectId: this.project.value,
      source: LeadSource['Facebook'],
    };

    this._store.dispatch(new updateProjectAssignment(payload));
    this.closeModal();
    this.gridApi?.deselectAll();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
