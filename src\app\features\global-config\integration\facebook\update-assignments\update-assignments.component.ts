import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { updateIntegrationAssignment } from 'src/app/reducers/automation/automation.actions';
import { getIntegrationAssignment } from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchProjectIdWithName } from 'src/app/reducers/project/project.action';
import {
  getProjectsIDWithName,
  getProjectsIDWithNameIsLoading,
} from 'src/app/reducers/project/project.reducer';
import { FetchAllLocations } from 'src/app/reducers/site/site.actions';
import {
  getAllLocations,
  getAllLocationsIsLoading,
} from 'src/app/reducers/site/site.reducer';

@Component({
  selector: 'update-assignments',
  templateUrl: './update-assignments.component.html',
})
export class UpdateAssignmentsComponent implements OnInit {
  placesList: any;
  allLocationsIsLoading: boolean;
  allProjectList: any;
  projectListIsLoading: boolean;
  preferredCountries: any[] = [];
  updateForm: FormGroup;
  globalSettingsData: any;
  selectedAccountId: any;
  source: any;
  isAdAccount: any;
  selectedAccountName: any;
  selectedAdName: string;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('contactNoInput') contactNoInput!: NgxMatIntlTelInputComponent;

  constructor(
    private store: Store<AppState>,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private cdr: ChangeDetectorRef,
    private fb: FormBuilder
  ) {
    this.updateForm = this.fb.group({
      project: [null],
      location: [null],
      countryCode: [null],
    });
  }
  ngOnInit() {
    this.store.dispatch(new FetchProjectIdWithName());
    this.store.dispatch(new FetchAllLocations());

    const selectAndPipe = (selector: any) =>
      this.store.select(selector).pipe(takeUntil(this.stopper));

    selectAndPipe(getProjectsIDWithName).subscribe((data: any) => {
      this.allProjectList = data
        ?.filter((data: any) => data.name)
        .slice()
        .sort((a: any, b: any) => a.name.localeCompare(b.name));
    });

    this.store
      .select(getProjectsIDWithNameIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.projectListIsLoading = data;
      });

    selectAndPipe(getAllLocations).subscribe((data: any) => {
      this.placesList = data?.items
        ?.slice()
        .sort((a: any, b: any) => a.location.localeCompare(b.location));
    });

    this.store
      .select(getAllLocationsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.allLocationsIsLoading = data;
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
      });

    this.store
      .select(getIntegrationAssignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (this.contactNoInput) {
          this.updateForm?.patchValue({
            project: (!data?.project?.isDeleted && !data?.project?.isArchived)
              ? data?.project?.id
              : null,
            location: data?.location?.id,
          });
          this.updatePreferredCountry(data?.countryCode);
        }
      });
  }

  private updatePreferredCountry(countryCode: string | null) {
    if (!this.contactNoInput) {
      return;
    }

    let matchingCountry: any = null;

    if (countryCode) {
      const countryCodeWithoutPlus = countryCode.replace('+', '');
      matchingCountry = this.contactNoInput?.allCountries?.find(
        (country: any) => country.dialCode === countryCodeWithoutPlus
      );
    }

    if (matchingCountry) {
      this.preferredCountries = [matchingCountry.iso2.toLowerCase()];
    } else if (this.globalSettingsData?.countries?.length) {
      const fallbackCountryCode =
        this.globalSettingsData.countries[0].code.toLowerCase();
      matchingCountry = this.contactNoInput.allCountries.find(
        (country: any) => country.iso2.toLowerCase() === fallbackCountryCode
      );
      this.preferredCountries = [fallbackCountryCode];
    } else {
      this.preferredCountries = ['in'];
      matchingCountry = this.contactNoInput.allCountries.find(
        (country: any) => country.iso2.toLowerCase() === 'in'
      );
    }

    this.contactNoInput.selectedCountry = matchingCountry;
    this.cdr.detectChanges();
  }

  updateProjectAndLocation() {
    let payload: any = {
      id: this.selectedAccountId,
      source: this.source,
      projectId: this.updateForm.value.project,
      locationId: this.updateForm.value.location,
      countryCode: this.contactNoInput?.selectedCountry?.dialCode
        ? this.contactNoInput.selectedCountry.dialCode
        : null,
    };
    this.store.dispatch(new updateIntegrationAssignment(payload));
    this.closeModal();
  }

  closeModal() {
    this.modalService.hide();
    this.isAdAccount = false;
  }
}
