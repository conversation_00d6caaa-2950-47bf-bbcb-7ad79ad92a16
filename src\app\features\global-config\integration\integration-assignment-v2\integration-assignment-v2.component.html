<div>
    <div class="flex-between fw-400 bg-coal px-24 py-12 text-white">
        <h3 class="fw-300">Lead Assignment</h3>
        <div class="icon ic-close  ic-sm cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-16 py-8 bg-light-pearl h-100-114 scrollbar">
        <div *ngIf="isBulkFb">
            <div class="scrollbar table-scrollbar scrollbar ip-w-100-40">
                <table class="table standard-table no-vertical-border">
                    <thead>
                        <tr class="w-100 text-nowrap">
                            <th class="w-100px">
                                <span>Account Name</span>
                            </th>
                            <th class="w-70px">
                                <span>{{ (isForm ? 'INTEGRATION.lead-form' : 'INTEGRATION.ad-name') | translate
                                    }}</span>
                            </th>
                            <th class="w-50px">{{ 'GLOBAL.actions' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary fw-semi-bold max-h-100-282">
                        <ng-container *ngFor="let dataNode of gridApi?.getSelectedNodes()">
                            <tr>
                                <td class="w-100px">
                                    <div class="text-truncate-1">
                                        {{fbAccountName}}
                                    </div>
                                </td>
                                <td class="w-70px">
                                    <div class="text-truncate-1">
                                        {{(isForm ? dataNode?.data?.name : dataNode?.data?.adName)}}
                                    </div>
                                </td>
                                <td class="w-50px">
                                    <a (click)="openConfirmDeleteModal(dataNode?.data?.adName, dataNode?.data?.id)"
                                        class="bg-light-red icon-badge">
                                        <span class="icon ic-delete m-auto ic-xxs"></span></a>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
        </div>
        <div *ngIf="isBulkAssignModel" class="scrollbar table-scrollbar scrollbar ip-w-100-40">
            <table class="table standard-table no-vertical-border">
                <thead>
                    <tr class="w-100 text-nowrap">
                        <th class="w-100px">
                            <span>Account Name</span>
                        </th>

                        <th class="w-50px">{{ 'GLOBAL.actions' | translate }}</th>
                    </tr>
                </thead>
                <tbody class="text-secondary fw-semi-bold max-h-100-433">
                    <ng-container *ngFor="let name of selectedIntegrations;">
                        <tr>
                            <td class="w-100px">
                                <div class="text-truncate-1">
                                    {{ name.accountName }}
                                </div>
                            </td>
                            <td class="w-50px">
                                <a (click)="openConfirmDeleteModal(name?.accountName, name?.accountId)"
                                    class="bg-light-red icon-badge">
                                    <span class="icon ic-delete m-auto ic-xxs"></span></a>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
        <div *ngIf="!isFbComponent && !isBulkAssignModel">
            <div class="br-6 bg-white flex-between break-all bg-profile">
                <div class="flex-column pt-20 pl-10 pb-20">
                    <div class="fw-semi-bold fv-sm-caps">
                        Account Name
                    </div>
                    <div class="fw-700 text-small text-truncate-1 break-all">{{
                        integration?.accountName }}</div>
                </div>
            </div>
        </div>
        <div *ngIf="isFbComponent && !isBulkFb" class="bg-white mt-20 br-6 align-end justify-content-between">
            <div class="flex-column pt-10 pl-10 pb-20">
                <div class="fw-semi-bold fv-sm-caps">Account Name</div>
                <div class="fw-700 text-large">{{integration?.facebookAccountName}}</div>
                <div *ngIf="isAdAccount">
                    <div class="fw-semi-bold fv-sm-caps mt-10">{{'INTEGRATION.ad-name' | translate}}</div>
                </div>
                <div *ngIf="isFormAccount">
                    <div class="fw-semi-bold fv-sm-caps mt-10">{{'INTEGRATION.lead-form' | translate}}</div>
                </div>
                <div class="fw-700 text-large">{{selectedAdName}}</div>
            </div>
            <div><img src="../../../../assets/images/profile.svg" class="mt-8" /></div>
        </div>
        <div class="border br-20 bg-white align-center user w-max-content mt-10">
            <ng-container *ngFor="let option of leadAssignmentOptions">
                <div class="activation" [ngClass]="{'active' : selectedSectionLeadAssignment == option?.value}"
                    (click)="selectedSectionLeadAssignment = option?.value; setListSelection();">
                    <span>{{option?.name}}</span>
                </div>
            </ng-container>
        </div>
        <div *ngIf="selectedSectionLeadAssignment === 'AdditionalConfig'" class="bg-white mt-12 px-8 pb-8">
            <ng-container *ngIf="selectedSectionLeadAssignment === 'AdditionalConfig'">
                <h5 class="fw-400 field-label pt-12 text-black-100">Additional Assignments</h5>
                <div class="mt-8 d-flex flex-wrap">
                    <div class="flex-wrap mr-10 mb-12 bg-light-pearl br-4 p-10 d-flex align-center"
                        title="This feature will assign a duplicate of the Incoming Lead to a block of selected user(s) at once."
                        (click)="canEnableAllowDuplicates ? handleDuplicatesToggle() : openConfirmModal(changePopup, 'allowDuplicateLeads')">
                        <label class="checkbox-container d-flex align-center gap-2 mb-0"
                            [ngClass]="{'pe-none': !canEnableAllowDuplicates}">
                            <input type="checkbox" [(ngModel)]="canAllowDuplicates"
                                [ngModelOptions]="{standalone: true}" (change)="setListSelection()"
                                [disabled]="!canEnableAllowDuplicates" />
                            <span class="checkmark"></span>
                            <h5 class="fw-semi-bold cursor-pointer" [ngClass]="{'text-coal': canEnableAllowDuplicates}">
                                Real Time Duplicates
                            </h5>
                        </label>
                    </div>
                    <div class="flex-wrap align-center mr-10 mb-12 bg-light-pearl br-4 p-10"
                        title="This feature will assign a secondary Owner to an Incoming Lead to a block of selected user(s) in a Sequential Manner."
                        (click)="canEnableAllowSecondaryUsers ? handleSecondaryUsersToggle() : openConfirmModal(changePopup, 'allowSecondaryUsers')">
                        <label class="checkbox-container d-flex align-center gap-2 mb-0"
                            [ngClass]="{'pe-none': !canEnableAllowSecondaryUsers}">
                            <input type="checkbox" [(ngModel)]="canAllowSecondaryUsers"
                                [ngModelOptions]="{standalone: true}" (change)="setListSelection()"
                                [disabled]="!canEnableAllowSecondaryUsers" />
                            <span class="checkmark"></span>
                            <h5 class="fw-semi-bold cursor-pointer"
                                [ngClass]="{'text-coal': canEnableAllowSecondaryUsers}">
                                Secondary Owners
                            </h5>
                        </label>
                    </div>
                </div>
                <form [formGroup]="integrationDuplicateForm" class="prevent-text-select" autocomplete="off">
                    <ng-container *ngIf="canAllowDuplicates && canEnableAllowDuplicates && !canAllowSecondaryUsers">
                        <div class="w-50">
                            <div class="label-req">Duplicate Owner(s)
                            </div>
                            <form-errors-wrapper [control]="integrationDuplicateForm.controls['assignedDuplicateUser']"
                                label="Select Duplicate Owner(s)">
                                <ng-select [required]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers"
                                    ResizableDropdown bindLabel="fullName" bindValue="id" [multiple]="true"
                                    groupBy="selectedAllGroup" [selectableGroup]="true" [closeOnSelect]="false"
                                    [clearSearchOnAdd]="true" (change)="sameAsSelectedUsers = false; handleSelectAll();"
                                    name="assignedDuplicateUser" placeholder="ex. Mounika Pampana" class="bg-white"
                                    [(ngModel)]="assignedDuplicateUser" formControlName="assignedDuplicateUser">
                                    <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span>Select All</div>
                                    </ng-template>
                                    <ng-template ng-label-tmp let-item="item">
                                        {{(item?.firstName || item?.lastName) ? (item.firstName+ " " +item.lastName) :
                                        "All"}}
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container" (click)="lastClickedOption = item">
                                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                                [checked]="item$.selected">
                                            <span class="checkmark"></span><span class="text-truncate-1 break-all">
                                                {{item.firstName}}
                                                {{item.lastName}}</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </form-errors-wrapper>
                        </div>
                        <div class="d-flex">
                            <label class="checkbox-container mt-10" (click)="sameAsSelectedUsersClicked()">
                                <input type="checkbox" [(ngModel)]="sameAsSelectedUsers"
                                    [ngModelOptions]="{standalone: true}" />
                                <span class="checkmark"></span>
                                <span class="line-break text-sm ip-pl-10">Same as <span
                                        class="fw-600 text-coal">Selected
                                        User(s)</span></span>
                            </label>
                        </div>
                        <div class="d-flex">
                            <label class="checkbox-container mt-10">
                                <input type="checkbox" [(ngModel)]="canAssignSequentially"
                                    [ngModelOptions]="{standalone: true}" />
                                <span class="checkmark"></span>
                                <span class="line-break text-sm ip-pl-10">Assign duplicate leads sequentially</span>
                            </label>
                        </div>
                    </ng-container>
                </form>
                <ng-container *ngIf="canAllowSecondaryUsers && canEnableAllowSecondaryUsers">
                    <form [formGroup]="integrationDualOwnerForm" class="prevent-text-select" autocomplete="off">
                        <div class="w-100 gap-2 d-flex">
                            <div class="w-50">
                                <div class="flex-between">
                                    <div class="label-req">Secondary Owner(s)
                                    </div>
                                    <label class="mt-10 checkbox-container" (click)="sameAsSelectedUsersClicked(true)">
                                        <input type="checkbox" [(ngModel)]="sameAsPrimaryUsers"
                                            [ngModelOptions]="{standalone: true}" />
                                        <span class="checkmark"></span>
                                        <span class="line-break text-sm ip-pl-10">Same as <span
                                                class="fw-600 text-coal">Primary
                                                User(s)</span></span>
                                    </label>
                                </div>
                                <form-errors-wrapper
                                    [control]="integrationDualOwnerForm?.controls?.['assignedSecondaryUsers']"
                                    label="Select Secondary Owner(s)">
                                    <ng-select [required]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers"
                                        ResizableDropdown bindLabel="fullName" bindValue="id" [multiple]="true"
                                        groupBy="selectedAllGroup" [selectableGroup]="true" [closeOnSelect]="false"
                                        [clearSearchOnAdd]="true" name="assignedSecondaryUsers"
                                        formControlName="assignedSecondaryUsers" placeholder="ex. Mounika Pampana"
                                        class="bg-white" [(ngModel)]="assignedSecondaryUsers"
                                        (change)="sameAsPrimaryUsers = false; handleSelectAll()">
                                        <ng-template ng-optgroup-tmp let-item="item" let-item$="item$"
                                            let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span>Select All</div>

                                        </ng-template>
                                        <ng-template ng-label-tmp let-item="item">
                                            {{(item?.firstName || item?.lastName) ? (item.firstName+ " " +item.lastName)
                                            :
                                            "All"}}
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container" (click)="lastClickedOption = item">
                                                <input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected">
                                                <span class="checkmark"></span>{{item.firstName}} {{item.lastName}}
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                </form-errors-wrapper>
                            </div>
                            <ng-container *ngIf="canAllowDuplicates && canEnableAllowDuplicates">
                                <div class="w-50">
                                    <div class="flex-column">
                                        <div class="flex-between">
                                            <div class="label-req">Duplicate Owner(s)
                                            </div>
                                            <label class="checkbox-container mt-10">
                                                <input type="checkbox" [(ngModel)]="canAssignSequentially"
                                                    [ngModelOptions]="{standalone: true}" />
                                                <span class="checkmark"></span>
                                                <span class="line-break text-sm ip-pl-10">Assign sequentially</span>
                                            </label>
                                        </div>
                                        <form-errors-wrapper
                                            [control]="integrationDualOwnerForm?.controls?.['assignedDuplicateUser']"
                                            label="Select Duplicate Owner(s)">
                                            <ng-select [required]="true"
                                                [items]="canAssignToAny ? allActiveUsers : activeUsers"
                                                ResizableDropdown bindLabel="fullName" bindValue="id" [multiple]="true"
                                                groupBy="selectedAllGroup" [selectableGroup]="true"
                                                [closeOnSelect]="false" [clearSearchOnAdd]="true"
                                                name="assignedDuplicateUser" placeholder="ex. Mounika Pampana"
                                                class="bg-white" formControlName="assignedDuplicateUser"
                                                [(ngModel)]="assignedDuplicateUser"
                                                (change)="sameAsAbove = false; handleSelectAll()">
                                                <ng-template ng-optgroup-tmp let-item="item" let-item$="item$"
                                                    let-index="index">
                                                    <div class="checkbox-container"><input type="checkbox"
                                                            id="item-{{index}}" data-automate-id="item-{{index}}"
                                                            [checked]="item$.selected"><span
                                                            class="checkmark"></span>Select
                                                        All</div>

                                                </ng-template>
                                                <ng-template ng-label-tmp let-item="item">
                                                    {{(item?.firstName || item?.lastName) ? (item.firstName+ " "
                                                    +item.lastName)
                                                    :
                                                    "All"}}
                                                </ng-template>
                                                <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                    let-index="index">
                                                    <div class="checkbox-container" (click)="lastClickedOption = item">
                                                        <input type="checkbox" id="item-{{index}}"
                                                            data-automate-id="item-{{index}}"
                                                            [checked]="item$.selected">
                                                        <span class="checkmark"></span>{{item.firstName}}
                                                        {{item.lastName}}
                                                    </div>
                                                </ng-template>
                                            </ng-select>
                                        </form-errors-wrapper>
                                        <div class="d-flex mt-12 align-center">
                                            <label class="checkbox-container"
                                                (click)="sameAsPrimarySecondaryUsersClicked()">
                                                <input type="checkbox" [(ngModel)]="sameAsAbove"
                                                    [ngModelOptions]="{standalone: true}" />
                                                <span class="checkmark"></span>
                                                <span class="line-break text-sm ip-pl-10">Same as</span>
                                            </label>
                                            <div class="ml-8"
                                                *ngFor="let userType of ['Primary User(s)', 'Secondary User(s)']; let i = index">
                                                <div class="form-check form-check-inline align-center p-0 mr-0">
                                                    <input type="radio" id="userType{{ i }}" data-automate-id="userType"
                                                        name="userType" [checked]="userType === selectedUserType"
                                                        [value]="userType" (change)="sameAsAbove = false"
                                                        [(ngModel)]="selectedUserType"
                                                        [ngModelOptions]="{standalone: true}"
                                                        class="radio-check-input" />
                                                    <label class="fw-600 text-secondary cursor-pointer text-xs"
                                                        for="userType{{ i }}">
                                                        {{ userType }}</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                        </div>
                    </form>
                </ng-container>
            </ng-container>
            <!-- <ng-template #selectedUsers> -->
            <div class="mt-10" *ngIf="assignedUser?.length && !canAllowDuplicates && !canAllowSecondaryUsers">
                <div class="d-flex align-center">
                    <h5 class="text-dark-gray mt-12 mr-12">Selected :- </h5>
                    <div class="bg-light-pearl br-4 d-flex px-16 py-8 cursor-pointer mr-20 mt-12 border-bottom-green"
                        *ngIf="assignedUser?.length">Primary
                        <div class="px-8 br-4 ml-4 bg-accent-green">
                            <span class="text-white">{{assignedUser?.length}}</span>
                        </div>
                    </div>
                </div>
                <div class="mt-12 align-center gap-2 flex-wrap">
                    <div class="flex-between mb-12" *ngFor="let user of assignedUser; let i = index; let last = last">
                        <div class="align-center">
                            <div class="dot dot-xl mr-6" [ngClass]="isUserActive(user) ? 'bg-pearl-90' : 'bg-light-gray'">
                                <span class="fw-semi-bold text-normal text-white text-uppercase">{{user ?
                                    getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.firstName[0] +
                                    getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.lastName[0] :
                                    '--'}}</span>
                            </div>
                            <div class="fw-semi-bold w-140 text-truncate-1 break-all text-large"
                                 [ngClass]="isUserActive(user) ? 'text-coal' : 'text-light-gray'"
                                 [title]="!isUserActive(user) ? 'This user is inactive' : ''">
                                {{getAssignedToDetails(user, canAssignToAny ?
                                allUserList : userList, true) || '--'}}
                                <span *ngIf="!isUserActive(user)" class="text-danger text-xs ml-2">(Inactive)</span>
                            </div>
                        </div>
                        <div>
                            <span (click)="removeUserFromSelection(user)" class="bg-light-red icon-badge">
                                <span class="icon ic-delete m-auto ic-xxs"></span></span>
                        </div>
                        <div [ngClass]=" { 'border-right h-16 mx-4' : !last && (i + 1) % 3 !==0 }"></div>
                    </div>
                </div>
            </div>
            <div *ngIf="canAllowDuplicates && !canAllowSecondaryUsers">
                <div class="d-flex align-center">
                    <h5 class="text-dark-gray mt-12 mr-12">Selected :- </h5>
                    <div class="bg-light-pearl br-4 d-flex px-16 py-8 cursor-pointer mr-20 mt-12"
                        *ngIf="assignedUser?.length" (click)="originalDuplicateListToggle('original')"
                        [ngClass]="{'border-bottom-green field-label-left-underline-green': listSelection == 'original'}">
                        Primary
                        <div class="px-8 br-4 ml-4 bg-accent-green">
                            <span class="text-white">{{assignedUser?.length}}</span>
                        </div>
                    </div>
                    <div class="bg-light-pearl br-4 d-flex px-16 py-8 cursor-pointer mr-20 mt-12"
                        *ngIf="assignedDuplicateUser?.length" (click)="originalDuplicateListToggle('duplicate')"
                        [ngClass]="{'border-bottom-green field-label-left-underline-green': listSelection == 'duplicate'}">
                        Duplicate User(s)
                        <div class="px-8 br-4 ml-4 bg-accent-green">
                            <span class="text-white">{{assignedDuplicateUser?.length}}</span>
                        </div>
                    </div>
                </div>
                <div class="mt-12 flex-wrap gap-2 d-flex">
                    <div class="flex-between mb-12" *ngFor="let user of filteredUsers; let i = index">
                        <div class="align-center">
                            <div class="dot dot-xl mr-6" [ngClass]="isUserActive(user) ? 'bg-pearl-90' : 'bg-light-gray'">
                                <span class="fw-semi-bold text-normal text-white text-uppercase">{{
                                    user ?
                                    getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.firstName[0] +
                                    getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.lastName[0] :
                                    '--'}}</span>
                            </div>
                            <div class="fw-semi-bold w-130 text-truncate-1 break-all text-large"
                                 [ngClass]="isUserActive(user) ? 'text-coal' : 'text-light-gray'"
                                 [title]="!isUserActive(user) ? 'This user is inactive' : ''">
                                {{getAssignedToDetails(user, canAssignToAny
                                ?
                                allUserList : userList, true) || '--'}}
                                <span *ngIf="!isUserActive(user)" class="text-danger text-xs ml-2">(Inactive)</span>
                            </div>
                        </div>
                        <div>
                            <span (click)="removeUserFromSelection(user)" class="bg-light-red icon-badge ms-2">
                                <span class="icon ic-delete m-auto ic-xxs"></span></span>
                        </div>
                        <div [ngClass]=" { 'border-right h-16 mx-4' : !last && (i + 1) % 3 !==0 }"></div>
                    </div>
                </div>
            </div>
            <div *ngIf="primarySeondaryUsers?.length && canAllowSecondaryUsers">
                <div class="d-flex align-center">
                    <h5 class="text-dark-gray mt-12 mr-12">Selected :- </h5>
                    <div class="bg-light-pearl br-4 d-flex px-16 py-8 cursor-pointer mr-20 mt-12"
                        *ngIf="assignedUser?.length" (click)="originalDuplicateListToggle('primary')"
                        [ngClass]="{'border-bottom-green field-label-left-underline-green': listSelection == 'primary'}">
                        Primary
                        <div class="px-8 br-4 ml-4 bg-accent-green">
                            <span class="text-white">{{assignedUser?.length}}</span>
                        </div>
                    </div>
                    <div class="bg-light-pearl br-4 d-flex px-16 py-8 cursor-pointer mr-20 mt-12"
                        *ngIf="assignedSecondaryUsers?.length" (click)="originalDuplicateListToggle('secondary')"
                        [ngClass]="{'border-bottom-green field-label-left-underline-green': listSelection == 'secondary'}">
                        Secondary
                        <div class="px-8 br-4 ml-4 bg-accent-green">
                            <span class="text-white">{{assignedSecondaryUsers?.length}}</span>
                        </div>
                    </div>
                    <div class="bg-light-pearl br-4 d-flex px-16 py-8 cursor-pointer mr-20 mt-12"
                        *ngIf="canAllowDuplicates && assignedDuplicateUser?.length"
                        (click)="originalDuplicateListToggle('duplicate')"
                        [ngClass]="{'border-bottom-green field-label-left-underline-green': listSelection == 'duplicate'}">
                        Duplicate
                        <div class="px-8 br-4 ml-4 bg-accent-green">
                            <span class="text-white">{{assignedDuplicateUser?.length}}</span>
                        </div>
                    </div>
                </div>
                <div class="mt-12 flex-wrap gap-2 d-flex">
                    <div class="flex-between mb-12" *ngFor="let user of primarySeondaryUsers; let i = index">
                        <div class="align-center">
                            <div class="dot dot-xl mr-6" [ngClass]="isUserActive(user) ? 'bg-pearl-90' : 'bg-light-gray'">
                                <span class="fw-semi-bold text-normal text-white text-uppercase">{{
                                    user ?
                                    getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.firstName[0] +
                                    getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.lastName[0] :
                                    '--'}}</span>
                            </div>
                            <div class="fw-semi-bold w-140 text-truncate-1 break-all text-large"
                                 [ngClass]="isUserActive(user) ? 'text-coal' : 'text-light-gray'"
                                 [title]="!isUserActive(user) ? 'This user is inactive' : ''">
                                {{getAssignedToDetails(user, canAssignToAny ?
                                allUserList : userList, true) || '--'}}
                                <span *ngIf="!isUserActive(user)" class="text-danger text-xs ml-2">(Inactive)</span>
                            </div>
                        </div>
                        <div>
                            <span (click)="removeUserFromSelection(user)" class="bg-light-red icon-badge">
                                <span class="icon ic-delete m-auto ic-xxs"></span></span>
                        </div>
                        <div [ngClass]=" { 'border-right h-16 mx-4' : !last && (i + 1) % 3 !==0 }"></div>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="selectedSectionLeadAssignment === 'Configuration'" class="p-8">
            <div class="bg-white p-12">
                <h5 class="fw-400 field-label-clear-m-req">Assignment Base</h5>
                <div class="d-flex mt-8" [formGroup]="configForm">
                    <div class="flex-wrap ph-mr-4 mr-10 px-16 br-20 align-center text-nowrap bg-slate py-10">
                        <input type="radio" [value]="false" formControlName="categoryType" id="sequential"
                            class="cursor-pointer" (change)="onSave()" style="display: none;">
                        <label for="sequential" class="cursor-pointer d-flex align-center">
                            <div [class.checked]="!configForm.get('categoryType').value"
                                class="radio-button cursor-pointer">
                            </div>
                            <span [ngClass]="!configForm.get('categoryType').value ? 'text-black' : 'text-light-gray'"
                                class="ml-6 align-center fw-semi-bold">Sequential&nbsp;<span
                                    class="ph-d-none">Based</span></span>
                        </label>
                    </div>
                    <div class="flex-wrap ph-mr-4 mr-10 px-16 br-20 align-center text-nowrap bg-slate py-10">
                        <input type="radio" [value]="true" formControlName="categoryType" id="percentage"
                            class="cursor-pointer" (change)="onSave()" style="display: none;">
                        <label for="percentage" class="cursor-pointer d-flex align-center">
                            <div [class.checked]="configForm.get('categoryType').value"
                                class="radio-button cursor-pointer">
                            </div>
                            <span [ngClass]="configForm.get('categoryType').value ? 'text-black' : 'text-light-gray'"
                                class="ml-6 align-center fw-semi-bold">Percentage&nbsp;<span
                                    class="ph-d-none">Based</span></span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="bg-white mt-8 p-12">
                <h5 class="fw-400 field-label-clear-m text-black-100">Assignment Type</h5>
                <div class="d-flex mt-8 gap-1 flex-wrap" [formGroup]="configForm">
                    <!-- Select Users -->
                    <div class="flex-wrap ph-mr-4 mr-10 px-16 br-20 align-center text-nowrap bg-slate py-10">
                        <input type="radio" value="users" formControlName="assignmentType" id="users"
                            class="cursor-pointer" style="display: none;">
                        <label for="users" class="cursor-pointer d-flex align-center">
                            <div [class.checked]="configForm.get('assignmentType').value === 'users'"
                                class="radio-button cursor-pointer">
                            </div>
                            <span
                                [ngClass]="configForm.get('assignmentType').value === 'users' ? 'text-black' : 'text-light-gray'"
                                class="ml-6 align-center fw-semi-bold">Select Users</span>
                        </label>
                    </div>
                    <!-- Select Teams -->
                    <div class="flex-wrap ph-mr-4 mr-10 px-16 br-20 align-center text-nowrap bg-slate py-10">
                        <input type="radio" value="teams" formControlName="assignmentType" id="teams"
                            class="cursor-pointer" style="display: none;">
                        <label for="teams" class="cursor-pointer d-flex align-center">
                            <div [class.checked]="configForm.get('assignmentType').value === 'teams'"
                                class="radio-button cursor-pointer">
                            </div>
                            <span
                                [ngClass]="configForm.get('assignmentType').value === 'teams' ? 'text-black' : 'text-light-gray'"
                                class="ml-6 align-center fw-semi-bold">Select Team</span>
                        </label>
                    </div>
                    <!-- Select Project -->
                    <div class="flex-wrap ph-mr-4 mr-10 px-16 br-20 align-center text-nowrap bg-slate py-10">
                        <input type="radio" value="projects" formControlName="assignmentType" id="projects"
                            class="cursor-pointer" style="display: none;">
                        <label for="projects" class="cursor-pointer d-flex align-center">
                            <div [class.checked]="configForm.get('assignmentType').value === 'projects'"
                                class="radio-button cursor-pointer">
                            </div>
                            <span
                                [ngClass]="configForm.get('assignmentType').value === 'projects' ? 'text-black' : 'text-light-gray'"
                                class="ml-6 align-center fw-semi-bold">Select Project</span>
                        </label>
                    </div>
                    <!-- Select Property -->
                    <div class="flex-wrap ph-mr-4 mr-10 px-16 br-20 align-center text-nowrap bg-slate py-10">
                        <input type="radio" value="properties" formControlName="assignmentType" id="properties"
                            class="cursor-pointer" style="display: none;">
                        <label for="properties" class="cursor-pointer d-flex align-center">
                            <div [class.checked]="configForm.get('assignmentType').value === 'properties'"
                                class="radio-button cursor-pointer">
                            </div>
                            <span
                                [ngClass]="configForm.get('assignmentType').value === 'properties' ? 'text-black' : 'text-light-gray'"
                                class="ml-6 align-center fw-semi-bold">Select Property</span>
                        </label>
                    </div>
                </div>
                <!-- Dropdown (Always Visible, Changes Data Based on Selection) -->
                <div [formGroup]="integrationDuplicateForm">
                    <div class="w-50">
                        <div *ngIf="selectedOption === 'users'" class="field-label">
                            {{'SETTINGS.select-user' | translate}}
                        </div>
                        <form-errors-wrapper [control]="integrationDuplicateForm?.controls?.['assignedUser']"
                            label="{{'SETTINGS.select-user' | translate}}">
                            <ng-select *ngIf="selectedOption === 'users'"
                                [items]="canAssignToAny ? allActiveUsers : activeUsers" ResizableDropdown
                                bindLabel="fullName" bindValue="id" [multiple]="true" groupBy="selectedAllGroup"
                                [selectableGroup]="true" [closeOnSelect]="false" [clearSearchOnAdd]="true"
                                name="assignedUser" [(ngModel)]="assignedUser"
                                (change)="sameAsSelectedUsers = false; initializeCheckedUsers();handleSelectAll(true);filterTeamLeader()"
                                placeholder="ex. Mounika Pampana" class="bg-white" formControlName="assignedUser">
                                <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>Select All</div>
                                </ng-template>
                                <ng-template ng-label-tmp let-item="item">
                                    {{(item?.firstName || item?.lastName) ? (item.firstName+ " " +item.lastName) : ""}}
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container" (click)="lastClickedOption = item">
                                        <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                            [checked]="item$.selected">
                                        <span class="checkmark"></span><span class="text-truncate-1 break-all">
                                            {{item.firstName}}
                                            {{item.lastName}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                    <div *ngIf="selectedOption!== 'users'" class="field-label fw-semi-bold text-black">{{
                        getDropdownLabel() }}</div>
                    <div class="w-50">
                        <form-errors-wrapper [control]="integrationDuplicateForm?.controls?.['selectedTeam']"
                            label="Select team">
                            <ng-select *ngIf="selectedOption === 'teams'" [virtualScroll]="true" [items]="teamsList"
                                ResizableDropdown [ngClass]="{
                    'pe-none blinking': allTeamsIsLoading }" formControlName="selectedTeam" [clearable]="true"
                                id="inpteam" [closeOnSelect]="true" name="selectedTeam" placeholder="Select"
                                class="bg-white" bindLabel="teamName" searchable="true"
                                (change)="onTeamSelectionChange($event);filterTeamLeader()">
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                    <div class="w-50">
                        <form-errors-wrapper [control]="integrationDuplicateForm?.controls?.['selectedProject']"
                            label="Select project">
                            <ng-select *ngIf="selectedOption === 'projects'" [virtualScroll]="true" ResizableDropdown
                                [ngClass]="{
                    'pe-none blinking': projectsListIsLoading }" formControlName="selectedProject"
                                [items]="projectsList" bindLabel="name" [closeOnSelect]="true" name="selectedProject"
                                (change)="onProjectSelectionChange($event);filterTeamLeader()" class="bg-white"
                                placeholder='Select Projects'>
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                    <div class="w-50">
                        <form-errors-wrapper [control]="integrationDuplicateForm?.controls?.['selectedProperty']"
                            label="Select property">
                            <ng-select *ngIf="selectedOption === 'properties'" formControlName="selectedProperty"
                                [virtualScroll]="true" ResizableDropdown [ngClass]="{
                    'pe-none blinking': propertiesListIsLoading }" [items]="propertiesList" bindLabel="title"
                                [closeOnSelect]="true" (change)="onPropertySelectionChange($event);filterTeamLeader()"
                                class="bg-white" placeholder='Select Properties'>
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
                <div class="mt-4"
                    *ngIf="(selectedOption === 'users' || selectedOption === 'projects' || selectedOption === 'properties' || selectedOption === 'teams') && assignedUser?.length > 0">
                    <h6 class="fw-semi-bold text-gray mb-2">Selected</h6>
                    <div class="align-center flex-wrap mt-8"
                        [ngClass]="configForm.get('categoryType').value ? 'gap-2' : 'gap-3'">
                        <div *ngFor="let user of assignedUser; let i = index; let last = last">
                            <div class="align-center" [ngClass]="{
                            'border-right': !last && (i + 1) % (configForm.get('categoryType').value ? 3 : 4) !== 0
                          }">
                                <label class="checkbox-container flex-between align-center mb-0"
                                    [ngClass]="configForm.get('categoryType').value ? 'w-130' : 'w-140'"
                                    [class.disabled]="!isUserActive(user)"
                                    [title]="!isUserActive(user) ? 'This user is inactive' : ''">
                                    <input type="checkbox" [(ngModel)]="checkedUsers[user]"
                                        [checked]="checkedUsers[user] !== false"
                                        [disabled]="!isUserActive(user)"
                                        (change)="onUserCheckChange(user, $event.target.checked)" />
                                    <span class="checkmark"></span>
                                    <h5 class="fw-300 text-truncate-1 break-all"
                                        [ngClass]="isUserActive(user) ? 'text-coal' : 'text-light-gray'">
                                        {{getAssignedToDetails(user, canAssignToAny ? allUserList : userList, true) ||
                                        '--'}}
                                        <span *ngIf="!isUserActive(user)" class="text-danger text-xs ml-2">(Inactive)</span>
                                    </h5>
                                </label>
                                <div [ngClass]="checkedUsers[user] && isUserActive(user) ? 'bg-light-pearl' : 'disabled'"
                                    class="d-flex ml-4 mr-8 br-6 border no-validation p-6 align-center"
                                    *ngIf="configForm.get('categoryType').value">
                                    <input type="number" class="border-0 outline-0 bg-light-pearl"
                                        [(ngModel)]="userPercentages[user]"
                                        [disabled]="!isUserActive(user)"
                                        min="0" max="100">
                                    <span class="fw-semi-bold">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Total percentage indicator -->
                    <div *ngIf="configForm.get('categoryType').value" class="mt-2"
                        [ngClass]="{'text-danger': getTotalPercentage() !== 100, 'text-success': getTotalPercentage() === 100}">
                        Total: {{getTotalPercentage()}}%
                        <span *ngIf="getTotalPercentage() !== 100">(Must equal 100%)</span>
                    </div>
                </div>

            </div>
            <div *ngIf="selectedOption === 'teams'" class="bg-white mb-12 br-6">
                <div class="bg-white p-12 mt-12 flex-between br-6" [formGroup]="configForm">
                    <div>
                        <h5 class="fw-semi-bold align-center">
                            Lead Rotation
                            <div class="ml-4 dot dot-sm bg-black-200 cursor-pointer"
                                title="Integration Leads will rotate inside a Created group or a Team within a Configured time.">
                                <span class="m-auto text-white">?</span>
                            </div>
                        </h5>
                        <h6 class="text-dark-gray mt-4">Create lead auto rotation within the team based on shift timing
                        </h6>

                    </div>
                    <div class="align-center ml-20 position-relative">
                        <div class="text-xs mr-8">{{configForm.get('leadRotationEnabled').value === true ? 'on' :
                            'off'}}
                        </div>
                        <input type="checkbox" formControlName="leadRotationEnabled"
                            class="toggle-switch toggle-active-sold" id="leadToggle">
                        <label for="leadToggle" class="switch-label"></label>
                    </div>
                </div>
                <ng-container *ngIf="configForm.get('leadRotationEnabled').value === true">
                    <div [formGroup]="leadRotationAddGroupForm" class="mx-16 mt-8 d-flex flex-wrap pb-24">
                        <div class="w-33 ph-w-100">
                            <div class="form-group mr-20 mb-4">
                                <h5 class="fw-400 field-label-req text-black-100">Team Name</h5>
                                <form-errors-wrapper label="Team name"
                                    [control]="leadRotationAddGroupForm?.controls?.['teamName']" autocomplete="off">
                                    <input type="text" [ngClass]="{'pe-none disabled': selectedOption === 'teams'}"
                                        required formControlName="teamName" id="inpTeamName"
                                        data-automate-id="inpTeamName" placeholder="ABC Team">
                                </form-errors-wrapper>
                            </div>
                        </div>
                        <div class="w-33 ph-w-100">
                            <div class="mr-20">
                                <h5 class="fw-400 field-label-req text-black-100">Select Team Leader</h5>
                                <form-errors-wrapper label="Leader"
                                    [control]="leadRotationAddGroupForm?.controls?.['teamLeader']" autocomplete="off">
                                    <ng-select [virtualScroll]="true"
                                        [ngClass]="{'pe-none disabled': selectedOption === 'teams'}" searchable="true"
                                        [closeOnSelect]="false" ResizableDropdown formControlName="teamLeader"
                                        id="inpTeamLeader" class="bg-white" bindLabel="fullName" [closeOnSelect]="true"
                                        bindValue="id" placeholder="{{ 'GLOBAL.select' | translate }} user"
                                        (change)="filterTeamLeader()">
                                        <ng-option *ngFor="let user of teamLeaders" [label]="user.fullname"
                                            [value]="user.id" [disabled]="!user.isActive">
                                            <span class="text-truncate-1 break-all"
                                                  [ngClass]="user.isActive ? 'text-coal' : 'text-light-gray'">
                                                {{user.firstName}} {{user.lastName}}
                                            </span>
                                            <span class="d-none">{{user.fullName}}</span>
                                            <span class="text-disabled" *ngIf="!user.isActive"> (Disabled)</span>
                                        </ng-option>
                                    </ng-select>
                                </form-errors-wrapper>
                            </div>
                        </div>
                        <div class="w-33 ph-w-100">
                            <div class="mr-20 error-right">
                                <h5 class="fw-400 field-label-req text-black-100">Shift Time</h5>
                                <div class="d-flex ip-flex-col w-100">
                                    <div class="w-50 ip-w-100 pr-6 ip-pr-0">
                                        <form-errors-wrapper label="Shift time from"
                                            [control]="leadRotationAddGroupForm?.controls?.['shiftTimeFrom']"
                                            autocomplete="off">
                                            <input type="text" readonly formControlName="shiftTimeFrom"
                                                [owlDateTimeTrigger]="dtFrom" [owlDateTime]="dtFrom"
                                                placeholder="From" />

                                            <owl-date-time [pickerType]="'timer'" [hour12Timer]="true"
                                                [startAt]="leadRotationAddGroupForm?.controls?.['shiftTimeFrom'] ? null : currentDate"
                                                #dtFrom="owlDateTime"></owl-date-time>
                                        </form-errors-wrapper>
                                    </div>
                                    <div class="w-50  ip-w-100 pl-6 ip-pl-0 ip-mt-20">
                                        <form-errors-wrapper label="Shift time to"
                                            [control]="leadRotationAddGroupForm?.controls?.['shiftTimeTo']"
                                            autocomplete="off">
                                            <input type="text" readonly formControlName="shiftTimeTo"
                                                [owlDateTimeTrigger]="dtTo" [owlDateTime]="dtTo" placeholder="To" />

                                            <owl-date-time [pickerType]="'timer'" [hour12Timer]="true"
                                                [startAt]="leadRotationAddGroupForm?.controls?.['shiftTimeTo'] ? null : currentDate"
                                                #dtTo="owlDateTime"></owl-date-time>
                                        </form-errors-wrapper>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-33 ph-w-100">
                            <div class="mr-20">
                                <h5 class="fw-400 field-label-req text-black-100">Lead Rotation Time</h5>
                                <form-errors-wrapper label="Rotation time"
                                    [control]="leadRotationAddGroupForm?.controls?.['rotationTime']" autocomplete="off">
                                    <div class="align-center">
                                        <input type="number" min="1" placeholder="ex. enter..."
                                            formControlName="rotationTime" class="brbr-0 brtr-0">
                                        <div class="brtr-4 brbr-4 border border-start-0 fw-semi-bold py-12 px-5">
                                            minutes(s)
                                        </div>

                                    </div>
                                </form-errors-wrapper>
                            </div>
                        </div>
                        <div class="w-33 ph-w-100">
                            <div class="mr-20">
                                <h5 class="fw-400 field-label-req text-black-100">Number of Rotation </h5>
                                <form-errors-wrapper label="No. of rotation"
                                    [control]="leadRotationAddGroupForm?.controls?.['rotationNum']" autocomplete="off">
                                    <ng-select [virtualScroll]="true" [items]="rotationNumOptions" bindLabel="label"
                                        bindValue="value" formControlName="rotationNum" ResizableDropdown
                                        placeholder="{{ 'GLOBAL.select' | translate }} number" class="bg-white">
                                    </ng-select>
                                </form-errors-wrapper>
                            </div>
                        </div>
                        <div class="w-33 ph-w-100">
                            <div class="mr-20">
                                <h5 class="fw-400 field-label text-black-100">Buffer time</h5>
                                <form-errors-wrapper label="Rotation time"
                                    [control]="leadRotationAddGroupForm?.controls?.['bufferTime']" autocomplete="off">
                                    <div class="align-center">
                                        <input type="number" min="1" placeholder="ex. enter..."
                                            formControlName="bufferTime" class="brbr-0 brtr-0">
                                        <div class="brtr-4 brbr-4 border border-start-0 fw-semi-bold py-12 px-5">
                                            minutes(s)
                                        </div>

                                    </div>
                                </form-errors-wrapper>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
        </div>
    </div>
    <div class="flex-end modal-footer bg-white  box-shadow-20 ">
        <h6 class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer"
            (click)="hideAssignmentPopup(); isBulkAssignModel=true">{{
            'BUTTONS.cancel' | translate }}</h6>
        <div class="border-right mx-12 h-16"></div>
        <div class="br-4 bg-coal py-10 px-20 text-white cursor-pointer" (click)="assignAccount()">{{ 'BUTTONS.save' |
            translate }}</div>
    </div>
</div>
<ng-template #changePopup>
    <div class="p-20">
        <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
        <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
        <div class="flex-end mt-30">
            <button class="btn-gray mr-20" (click)="closePopup()" id="clkSettingsCancel"
                data-automate-id="clkSettingsCancel">
                cancel</button>

            <button class="btn-green px-12 text-nowrap min-w-fit-content" (click)="goToGlobalConfig()"
                id="clkSettingsYes" data-automate-id="clkSettingsYes">
                Go to Lead Settings
            </button>
        </div>
    </div>
</ng-template>