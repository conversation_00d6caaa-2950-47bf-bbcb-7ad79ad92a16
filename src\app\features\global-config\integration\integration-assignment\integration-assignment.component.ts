import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { VALIDATION_CLEAR, VALIDATION_SET } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getAssignedToDetails, toggleValidation, validateAllFormFields } from 'src/app/core/utils/common.util';
import { FetchUserAssignmentByEntity, UpdateMultiUserAssignment, UpdateUserAssignment } from 'src/app/reducers/automation/automation.actions';
import { getUserAssignmentByEntity } from 'src/app/reducers/automation/automation.reducer';

@Component({
  selector: 'integration-assignment',
  templateUrl: './integration-assignment.component.html',
})
export class IntegrationAssignmentComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() image: string;
  @Input() selectedAccountName: string;
  @Input() selectedIntegrations: any[];

  @Input() isBulkAssignModel: boolean;

  @Input() canAllowSecondaryUsers: boolean;
  @Input() canAllowDuplicates: boolean;
  @Input() canAssignToAny: boolean;

  @Input() sameAsPrimaryUsers: boolean;
  @Input() sameAsSelectedUsers: boolean;
  @Input() sameAsAbove: boolean;
  @Input() assignedSecondaryUsers: any[] = [];
  @Input() assignedDuplicateUser: any[] = [];
  @Input() assignedPrimaryUsers: any[] = [];
  @Input() assignedUser: any[] = [];
  @Input() assignedUserDetails: Array<string>;
  @Input() allActiveUsers: Array<any>;
  @Input() allUserList: Array<any>;
  @Input() userList: Array<any>;
  @Input() activeUsers: Array<any>;
  @Input() updatedIntegrationList: Array<any>;
  @Input() moduleId: string;
  @Input() selectedAccountId: string;
  @Input() selectedCount: number;
  @Input() canEnableAllowDuplicates: boolean;
  @Input() canEnableAllowSecondaryUsers: boolean;
  @Input() canAssignSequentially: boolean;
  @Input() isFbComponent: boolean;
  @Input() isAdAccount: boolean;
  @Input() isFormAccount: boolean;
  @Input() selectedAdName: string;
  @Input() isBulkFb: boolean = false;
  @Input() gridApi: any;
  @Input() isReferenceId: boolean;
  @Input() selectedReferences: any;
  @Input() displayName: string;
  @Output() isShowAssignModalChanged: EventEmitter<boolean> = new EventEmitter<boolean>();

  integrationDuplicateForm: FormGroup;
  integrationDualOwnerForm: FormGroup;
  allowDuplicatesPopupRef: any;

  selectedSectionLeadAssignment: "Configuration" | "Selected Users" = "Configuration";
  leadAssignmentOptions: string[] = ["Configuration", "Selected Users"];
  listSelection: string = 'original';
  selectedUserType: 'Primary User(s)' | 'Secondary User(s)' = 'Primary User(s)';
  message: string;
  notes: string;

  getAssignedToDetails = getAssignedToDetails;

  get filteredUsers(): string[] {
    return this.listSelection === 'original' ? this.assignedUser : this.assignedDuplicateUser;
  }

  get primarySeondaryUsers(): string[] {
    return this.listSelection === 'primary' ? this.assignedPrimaryUsers :
      this.listSelection === 'secondary' ? this.assignedSecondaryUsers : this.assignedDuplicateUser;
  }
  constructor(
    private store: Store<AppState>,
    private _notificationService: NotificationsService,
    private fb: FormBuilder,
    public modalService: BsModalService,
    private modalRef: BsModalRef,
    public router: Router,
  ) { }

  ngOnInit(): void {
    this.integrationDuplicateForm = this.fb.group({
      assignedUser: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]]
    });

    this.integrationDualOwnerForm = this.fb.group({
      assignedPrimaryUsers: [null, Validators.required],
      assignedSecondaryUsers: [null, Validators.required],
      assignedDuplicateUser: [null, Validators.required],
      selectedUserType: ['Primary User(s)', Validators.required]
    });

    if (this.isBulkAssignModel || this.isFbComponent) {
      if (this.selectedAccountId) {
        this.store.dispatch(new FetchUserAssignmentByEntity(this.selectedAccountId));
      }
      this.store
        .select(getUserAssignmentByEntity)
        .pipe(takeUntil(this.stopper))
        .subscribe((res) => {
          this.canAssignSequentially = !res?.shouldCreateMultipleDuplicates;
          this.canAllowSecondaryUsers = res?.isDualAssignmentEnabled;
          this.canAllowDuplicates = res?.isDuplicateAssignmentEnabled;
          this.assignedUser = res?.userIds;
          this.assignedUserDetails = res?.userIds;
          this.assignedPrimaryUsers = this.assignedUser;
          this.assignedSecondaryUsers = res?.secondaryUserIds;
          this.assignedDuplicateUser = res?.duplicateUserIds;
          this.toggleAssignedUserValidation();
        });
      return;
    }
  }

  toggleAssignedUserValidation() {
    if (this.canAllowDuplicates) {
      toggleValidation(VALIDATION_SET, this.integrationDuplicateForm, 'assignedUser', [Validators.required]);
      return;
    }
    toggleValidation(VALIDATION_CLEAR, this.integrationDuplicateForm, 'assignedUser');
  }

  removeUserFromSelection(userId: any) {
    if (this.canAllowSecondaryUsers) {
      this.sameAsPrimaryUsers = false;
      if (this.listSelection == 'secondary') {
        this.assignedSecondaryUsers = this.assignedSecondaryUsers?.filter((user: any) => user !== userId);
        if (!this.assignedSecondaryUsers?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      } else if (this.listSelection == 'duplicate') {
        this.assignedDuplicateUser = this.assignedDuplicateUser?.filter((user: any) => user !== userId);
        if (!this.assignedDuplicateUser?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      }
      this.assignedPrimaryUsers = this.assignedPrimaryUsers?.filter((user: any) => user !== userId);
      if (!this.assignedPrimaryUsers?.length)
        this.selectedSectionLeadAssignment = 'Configuration';
      return;
    }
    if (this.canAllowDuplicates && !this.canAllowSecondaryUsers) {
      this.sameAsSelectedUsers = false;
      if (this.listSelection == 'original') {
        this.assignedUser = this.assignedUser.filter((user: any) => user !== userId);
        this.assignedUserDetails = this.assignedUser;
        if (!this.assignedUser?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      }
      this.assignedDuplicateUser = this.assignedDuplicateUser.filter((user: any) => user !== userId);
      if (!this.assignedDuplicateUser?.length)
        this.selectedSectionLeadAssignment = 'Configuration';
      return;
    }
    this.assignedUser = this.assignedUser.filter((user: any) => user !== userId);
    this.assignedUserDetails = this.assignedUser;
    if (!this.assignedUser?.length)
      this.selectedSectionLeadAssignment = 'Configuration';
  }

  assignFbAccount() {
    if (!this.validateAssignmentForm())
      return;
    let payload: any = {
      entityId: this.selectedAccountId,
      userIds: this.canAllowSecondaryUsers ? (this.assignedPrimaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedPrimaryUsers) :
        (this.assignedUser?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedUser) || [],
      secondaryUserIds: this.assignedSecondaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedSecondaryUsers || [],
      duplicateUserIds: this.assignedDuplicateUser?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedDuplicateUser || [],
      isDuplicateAssignmentEnabled: this.canAllowDuplicates || false,
      isDualAssignmentEnabled: this.canAllowSecondaryUsers || false,
      moduleId: this.moduleId,
      shouldCreateMultipleDuplicates: !this.canAssignSequentially,
    };

    if (this.isBulkFb) {
      payload.entityIds = this.selectedAccountId;
      delete payload?.entityId;
    }

    this.isBulkFb ? this.store?.dispatch?.(new UpdateUserAssignment(payload, null, true)) : this.store.dispatch(new UpdateUserAssignment(payload));
    this.hideAssignmentPopup();
    this.gridApi?.deselectAll();
  }

  assignAccount() {
    if (this.isFbComponent || this.isBulkFb) {
      this.assignFbAccount();
      return;
    }
    if (!this.validateAssignmentForm())
      return;
    let selectedIds: any
    if (!this.isReferenceId) {
      this.selectedIntegrations = this.updatedIntegrationList?.filter(
        (item: any) => item.isSelected
      );
      selectedIds = this.selectedIntegrations?.map((node: any) =>
      node?.accountId
      );
    } else {
      selectedIds = this.selectedReferences?.map((item: any) => item?.id)
    }
    if (selectedIds?.length > 0) {
      let payload: any = {
        moduleId: this.moduleId,
        entityIds: selectedIds,
        userIds: this.canAllowSecondaryUsers ? (this.assignedPrimaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedPrimaryUsers) :
          (this.assignedUser?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedUser),
        secondaryUserIds: this.assignedSecondaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedSecondaryUsers,
        duplicateUserIds: this.assignedDuplicateUser?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedDuplicateUser,
        isDuplicateAssignmentEnabled: this.canAllowDuplicates,
        isDualAssignmentEnabled: this.canAllowSecondaryUsers,
        shouldCreateMultipleDuplicates: !this.canAssignSequentially,
      };
      this.isReferenceId ?
        this.store.dispatch(new UpdateMultiUserAssignment(payload, 'Reference')) : this.store.dispatch(new UpdateMultiUserAssignment(payload, 'Project'))
    } else {
      let payload: any = {
        entityId: this.selectedAccountId,
        userIds: this.canAllowSecondaryUsers ? (this.assignedPrimaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedPrimaryUsers) :
          (this.assignedUser?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedUser),
        secondaryUserIds: this.assignedSecondaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedSecondaryUsers,
        duplicateUserIds: this.assignedDuplicateUser?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedDuplicateUser,
        isDuplicateAssignmentEnabled: this.canAllowDuplicates,
        isDualAssignmentEnabled: this.canAllowSecondaryUsers,
        moduleId: this.moduleId,
        shouldCreateMultipleDuplicates: !this.canAssignSequentially,
      };
      this.isReferenceId ?
        this.store.dispatch(new UpdateUserAssignment(payload, 'Reference')) :
        this.store.dispatch(new UpdateUserAssignment(payload))
    }
    this.reset();
    this.hideAssignmentPopup();
  }

  hideAssignmentPopup() {
    this.isShowAssignModalChanged.emit(false);
    this.modalService.hide()
  }

  handleSelectAll(isAssignedUser: boolean = false) {
    const allUsers: string[] = (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id);
    if (this.assignedUser?.includes("selectedAllGroup")) {
      this.assignedUser = allUsers;
    }
    if (this.assignedDuplicateUser?.includes("selectedAllGroup")) {
      this.assignedDuplicateUser = allUsers;
    }
    if (this.assignedPrimaryUsers?.includes("selectedAllGroup")) {
      this.assignedPrimaryUsers = allUsers;
    }
    if (this.assignedSecondaryUsers?.includes("selectedAllGroup")) {
      this.assignedSecondaryUsers = allUsers;
    }

    if (isAssignedUser) {
      this.assignedPrimaryUsers = this.assignedUser;
    } else {
      this.assignedUser = this.assignedPrimaryUsers;
    }
  }

  sameAsSelectedUsersClicked(isPrimaryUser: boolean = false) {
    if (this.sameAsPrimaryUsers)
      return;
    if (isPrimaryUser) {
      this.assignedSecondaryUsers = [...this.assignedPrimaryUsers];
      return;
    }
    this.assignedDuplicateUser = [...this.assignedUser];
  }

  reset() {
    this.updatedIntegrationList?.forEach((item) => (item.isSelected = false));
    this.selectedCount = 0;
    this.assignedUser = [];
  }

  resetIntegrationForm() {
    // this.assignedUser = [];
    // this.assignedPrimaryUsers = [];
    // this.assignedSecondaryUsers = [];
    // this.assignedDuplicateUser = [];
    this.sameAsPrimaryUsers = false;
    this.sameAsSelectedUsers = false;
    this.sameAsAbove = false;
    this.listSelection = "original";
    this.toggleAssignedUserValidation();
    this.revertValidation();
  }

  revertValidation() {
    const integrationDuplicateFormControlNames = ['assignedUser', 'assignedDuplicateUser'];
    integrationDuplicateFormControlNames?.forEach((controlName: string) => {
      this.integrationDuplicateForm.get(controlName).markAsPristine();
      this.integrationDuplicateForm.get(controlName).markAsUntouched();
      if (controlName !== 'assignedUser')
        this.integrationDuplicateForm.get(controlName).setErrors(null);
    });

    if (!this.canAllowSecondaryUsers) {
      const integrationDualOwnerFormControlNames = ['assignedPrimaryUsers', 'assignedSecondaryUsers', 'assignedDuplicateUser'];
      integrationDualOwnerFormControlNames?.forEach((controlName: string) => {
        this.integrationDualOwnerForm.get(controlName).markAsPristine();
        this.integrationDualOwnerForm.get(controlName).markAsUntouched();
        this.integrationDualOwnerForm.get(controlName).setErrors(null);
      });
    }

    this.selectedUserType = "Primary User(s)";
  }

  setListSelection() {
    this.listSelection = (this.canAllowDuplicates && !this.canAllowSecondaryUsers) ? this.assignedUser?.length ? 'original' : 'duplicate' : this.assignedPrimaryUsers?.length ? 'primary' : this.assignedSecondaryUsers?.length ? 'secondary' : 'duplicate';
  }

  originalDuplicateListToggle(selection: string) {
    this.listSelection = selection;
  }

  sameAsPrimarySecondaryUsersClicked() {
    if (this.sameAsAbove)
      return;
    this.assignedDuplicateUser = this.selectedUserType == 'Primary User(s)' ? [...this.assignedPrimaryUsers] : [...this.assignedSecondaryUsers];
  }

  validateAssignmentForm() {
    if (this.canAllowDuplicates && !this.canAllowSecondaryUsers) {
      if (!this.integrationDuplicateForm?.valid) {
        validateAllFormFields(this.integrationDuplicateForm);
        return false;
      }
      if (this.assignedUser?.length == 1 && this.assignedDuplicateUser?.length == 1) {
        if (this.assignedUser?.[0] == this.assignedDuplicateUser?.[0]) {
          this._notificationService.warn('Warning', "Duplicate user assignment detected.");
          return false;
        }
      }
    } else if (this.canAllowDuplicates && this.canAllowSecondaryUsers) {
      if (!this.integrationDualOwnerForm?.valid) {
        validateAllFormFields(this.integrationDualOwnerForm);
        return false;
      }
      if (
        (this.assignedPrimaryUsers?.length == 1 && this.assignedSecondaryUsers?.length == 1) ||
        (this.assignedDuplicateUser?.length == 1 && this.assignedSecondaryUsers?.length == 1) ||
        (this.assignedDuplicateUser?.length == 1 && this.assignedPrimaryUsers?.length == 1)
      ) {
        if (
          (this.assignedPrimaryUsers?.[0] == this.assignedDuplicateUser?.[0] && this.assignedPrimaryUsers?.length == 1 && this.assignedDuplicateUser?.length == 1) ||
          (this.assignedPrimaryUsers?.[0] == this.assignedSecondaryUsers?.[0] && this.assignedPrimaryUsers?.length == 1 && this.assignedSecondaryUsers?.length == 1) ||
          (this.assignedSecondaryUsers?.[0] == this.assignedDuplicateUser?.[0] && this.assignedSecondaryUsers?.length == 1 && this.assignedDuplicateUser?.length == 1)
        ) {
          this._notificationService.warn('Warning', "Duplicate user assignment detected.");
          return false;
        }
      }
    } else if (!this.canAllowDuplicates && this.canAllowSecondaryUsers) {
      if (!this.integrationDualOwnerForm.controls['assignedPrimaryUsers'].valid || !this.integrationDualOwnerForm.controls['assignedSecondaryUsers'].valid) {
        validateAllFormFields(this.integrationDualOwnerForm);
        return false;
      }
      if (this.assignedPrimaryUsers?.length && this.assignedSecondaryUsers?.length) {
        if (this.assignedPrimaryUsers?.length == 1 && this.assignedSecondaryUsers?.length == 1) {
          if (this.assignedPrimaryUsers?.[0] == this.assignedSecondaryUsers?.[0]) {
            this._notificationService.warn('Warning', "Duplicate user assignment detected.");
            return false;
          }
        }
        return true;
      }

    }
    return true;
  }

  openConfirmModal(allowDuplicatesPopupRef: any, settingType: string) {
    this.allowDuplicatesPopupRef = this.modalService.show(allowDuplicatesPopupRef, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    switch (settingType) {
      case 'allowDuplicateLeads':
        this.message =
          'To use this feature “Allow Lead Duplicates” must be enabled.';
        this.notes =
          'Please read the instructions clearly and proceed.';
        break;
      case 'allowSecondaryUsers':
        this.message =
          'To use this feature “Dual Lead Ownership” must be enabled.';
        this.notes =
          'Please read the instructions clearly and proceed.';
        break;
    }
  }

  closePopup() {
    this.allowDuplicatesPopupRef.hide();
  }

  goToGlobalConfig() {
    this.hideAssignmentPopup();
    this.closePopup();
    this.closeModal();
    this.router.navigate(['global-config', 'lead-settings']);
  }

  closeModal() {
    this.modalRef.hide();
    this.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
