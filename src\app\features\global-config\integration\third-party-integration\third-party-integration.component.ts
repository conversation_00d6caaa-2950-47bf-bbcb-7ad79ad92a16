import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { EMPTY_GUID, GOOGLE_BUTTON, PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { IntegrationSource, WhatsappThrough } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAppName,
  getPages,
  getTenantName,
  isEmptyObject,
  validateAllFormFields
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import {
  FetchIntegrationAssignment,
  FetchPriorityList,
  updateIntegrationAssignment
} from 'src/app/reducers/automation/automation.actions';
import {
  getIntegrationAssignment,
  getPriorityList,
  getUserAssignmentByEntity,
} from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  AddIntegration,
  AddIntegrationGoogleLead,
  AgencyName,
  AssignPageSize,
  DeleteGoogleIntegration,
  DeleteIntegration,
  DoesExistAccountName,
  FetchAgencyNameList,
  FetchGoogleIntegrationById,
  FetchGoogleIntegrationByIdSuccess,
  FetchGoogleIntegrationList,
  FetchIntegrationById,
  FetchIntegrationByIdSuccess,
  FetchIntegrationCount,
  FetchIntegrationList,
  FetchWebhook,
  FetchWebhookAccount,
  UpdateWebhook,
} from 'src/app/reducers/Integration/integration.actions';
import {
  doesAccountNameExists,
  getAgencyNameList,
  getExcelFileLink,
  getGoogleIntegrationDetails,
  getGoogleIntegrationIsLoading,
  getIntegrationCount,
  getIntegrationDetails,
  getIntegrationDetailsIsLoading,
  getWebhook,
  getWebhookAccount,
  getWebhookAccountIsLoading
} from 'src/app/reducers/Integration/integration.reducer';
import { FetchLeadSourceList } from 'src/app/reducers/master-data/master-data.actions';
import { getLeadSource } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProjectIdWithName } from 'src/app/reducers/project/project.action';
import { getProjectsIDWithName } from 'src/app/reducers/project/project.reducer';
import { FetchAllLocations } from 'src/app/reducers/site/site.actions';
import { getAllLocations } from 'src/app/reducers/site/site.reducer';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
  FetchWhatsappSettingList,
  UpdateWhatsappSettingList,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
  getWhatsappSetting,
} from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment as env } from 'src/environments/environment';
import { IntegrationAssignmentV2Component } from '../integration-assignment-v2/integration-assignment-v2.component';

interface GoogleOAuth {
  oAuthEndpoint: string;
  clientId: string;
  scope: string;
  googleButton: string;
}

@Component({
  selector: 'third-party-integration',
  templateUrl: './third-party-integration.component.html',
})
export class ThirdPartyIntegrationComponent
  implements OnInit, OnDestroy, AfterViewInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  // accountName: FormControl = new FormControl('', Validators.required);
  settingForm: FormGroup;
  assignedUser: any[] = [];
  assignedDuplicateUser: any[] = [];
  project: FormControl = new FormControl(null);
  location: FormControl = new FormControl(null);
  countryCode: FormControl = new FormControl(null);
  agencyName: FormControl = new FormControl(null);
  name: string = '';
  domainName: URL = new URL(location.href);
  googleButton = GOOGLE_BUTTON;
  updatedIntegrationList: Array<any> = [];
  assignedUserDetails: Array<string> = [];
  userList: Array<any> = [];
  allUserList: Array<any> = [];
  activeUsers: Array<any> = [];
  allActiveUsers: Array<any> = [];
  allProjectList: Array<any> = [];
  placesList: Array<any> = [];
  bulkModuleId: string;
  searchTerm: string;
  selectedAccountName: string;
  selectedIntegrations: Array<any> = [];
  selectedAccountId: string = '';
  selectedLeadSource: string;
  agencyAccountId: any;
  agencySource: string;
  agencyNameList: string;
  canAdd: boolean = false;
  canDelete: boolean = false;
  canAssign: boolean = false;
  canAssignToAny: boolean = false;
  isShowContentAndAdd: boolean = true;
  isShowAddAccountBtn: boolean = true;
  isShowAddAtListing: boolean = true;
  isShowAssignModal: boolean = false;
  isShowProjectAndLocationModal: boolean = false;
  isShowAgencyModal: boolean = false;
  selectedCount: number = 0;
  isBulkAssignModel: boolean = false;
  displayName: string;
  isManualChange: boolean = true;
  lastClickedOption: any;
  cities: any = [];
  canAllowDuplicates: boolean = false;
  canEnableAllowDuplicates: boolean = false;
  canAllowSecondaryUsers: boolean = false;
  message: string = '';
  notes: string = '';
  canEnableAllowSecondaryUsers: boolean = false;
  assignedSecondaryUsers: any[] = [];
  assignedPrimaryUsers: any[] = [];
  sameAsPrimaryUsers: boolean = false;
  sameAsSelectedUsers: boolean = false;
  sameAsAbove: boolean = false;
  integrationDuplicateForm: FormGroup;
  integrationDualOwnerForm: FormGroup;
  isIntegrationLoading: boolean = true;
  isShowCountryCodeModal: boolean = false;
  preferredCountries: any[] = [];
  integrationAssignmentData: any;
  webhookfields: any;
  webHookList: any;
  webhookKeys: string[];
  payloadfieldkey: any;
  webhookMappingForm: FormGroup;
  integrationForm: FormGroup;
  payloadOptions: any;
  tempVariables: any;
  uniqueValueArray: any[] = [];
  tenantId: string = getTenantName();
  temp: any[];
  allPayloadOptions: any[];
  leadSources: any[] = [];
  selectedInfo: any;
  isEditWebhook: boolean = false;
  iswebhookAccountLoading: boolean = true;
  doesAccountNameExist: boolean = false;
  canBulkAssignment: boolean = false;
  canBulkReassign: boolean = false;
  getAppName = getAppName;
  image: string;
  receivedData: any;
  email: any
  isSearch: boolean = false;
  isConfigVisible: boolean = false;
  personalList: any[];
  integrationList: any[];
  settingData: any;
  personalList1: any[];
  integrationList1: any[];
  canViewForFilter: boolean;
  currOffset: number = 0;
  currPageNumber: number = 1;
  PageSize: number = PAGE_SIZE;
  totalCount: number;
  getPages = getPages;
  showEntriesSize: number[] = SHOW_ENTRIES;
  pageEntry: FormControl = new FormControl(this.PageSize);
  rowData: any[];
  filtersPayload: any;
  constructor(
    public modalService: BsModalService,
    private modalRef: BsModalRef,
    private _store: Store<AppState>,
    public router: Router,
    private cdr: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private _notificationsService: NotificationsService,
    private fb: FormBuilder,
    private _translateService: TranslateService,
    private headerTitle: HeaderTitleService,
  ) {
    this.headerTitle.setLangTitle('Integration');
    this.integrationDuplicateForm = this.formBuilder.group({
      assignedUser: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
    });

    const storedData = localStorage.getItem('integrationData');
    if (storedData) {
      this.receivedData = JSON.parse(storedData);
      this.image = this.receivedData.image;
      this.displayName = this.receivedData.displayName;
      this.name = this.receivedData.name;
    }
    this.integrationForm = this.fb.group({
      accountName: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      loginEmail: [null, ValidationUtil.emailValidatorMinLength],
      toRecipients: this.fb.array([], [Validators.required, Validators.minLength(1)]),
      ccRecipients: this.fb.array([]),
      bccRecipients: this.fb.array([])
    });

    this.integrationDualOwnerForm = this.formBuilder.group({
      assignedPrimaryUsers: [null, [Validators.required]],
      assignedSecondaryUsers: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
      selectedUserType: ['Primary User(s)', [Validators.required]],
    });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Integration.Delete'))
          this.canDelete = true;
        if (permissions?.includes('Permissions.Integration.Create'))
          this.canAdd = true;
        if (permissions?.includes('Permissions.Integration.Assign'))
          this.canAssign = true;
        if (permissions?.includes('Permissions.Users.ViewForFilter')) {
          this.canViewForFilter = true;
          if (permissions?.includes('Permissions.Users.AssignToAny')) {
            this.canAssignToAny = true;
            this._store.dispatch(new FetchUsersListForReassignment());
          } else {
            this._store.dispatch(new FetchAdminsAndReportees());
          }
        }
      });
    this._store
      .select(getIntegrationDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!isEmptyObject(data)) {
          this.updatedIntegrationList = data?.items?.filter(
            (item: any) =>
              item.leadSource ==
              IntegrationSource[this.name as keyof typeof IntegrationSource]
          )
            .map((item: any) => ({ ...item, isSelected: false }));
          this.rowData = this?.updatedIntegrationList;
          this.totalCount = data?.totalCount;
        }
      });
    this._store.dispatch(new FetchLeadSourceList());

    this._store
      .select(getLeadSource)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          this.leadSources = [...leadSource]
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
        }
      });

    this._store
      .select(getIntegrationDetailsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isIntegrationLoading = isLoading;
      });;


    this._store
      .select(getGoogleIntegrationDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.updatedIntegrationList = (data?.items || []).map((item: any) => ({ ...item, isSelected: false }));
        this.rowData = this?.updatedIntegrationList;
        this.totalCount = data?.totalCount;
      });

    this._store
      .select(getGoogleIntegrationIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isIntegrationLoading = isLoading;
      });

    this._store
      .select(getAllLocations)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this._store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any[]) => {
        this.allProjectList = res
          ?.filter((data: any) => data.name)
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this._store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
        this.personalList = this.activeUsers;
        this.integrationList = this.activeUsers;
        this.selectAllForDropdownItems(this.activeUsers);
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
        this.personalList = this.allActiveUsers;
        this.integrationList = this.allActiveUsers;
        this.selectAllForDropdownItems(this.allActiveUsers);
      });
  }

  ngOnInit() {
    this._store.dispatch(
      new AssignPageSize(this.PageSize, this.currPageNumber)
    );
    this.settingForm = this.fb.group({
      personal: [null],
      integration: [null],
    });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.canEnableAllowDuplicates =
          data?.duplicateFeatureInfo?.isFeatureAdded;
        this.canEnableAllowSecondaryUsers = data?.isDualOwnershipEnabled;
        this.integrationAssignmentData = data;
      });

    if (this.integrationAssignmentData?.isWhatsAppDeepIntegration && this.displayName === 'Whatsapp') {
      this._store.dispatch(new FetchWhatsappSettingList());
    }
    this._store
      .select(getWhatsappSetting)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.settingData = data;
        let personalUsers = this.allUserList
          .filter(
            (user: any) =>
              data?.templateShare?.includes(user?.id) && user?.isActive
          )
          .map((user: any) => user?.id);
        let integrationUsers = this.allUserList
          .filter(
            (user: any) =>
              data?.openConversation?.includes(user?.id) && user?.isActive
          )
          .map((user: any) => user?.id);

        this.settingForm.patchValue({
          personal: personalUsers,
          integration: integrationUsers,
        });
        this.personalList1 = this.personalList.filter(
          (el: any) => !this.settingData?.openConversation?.includes(el?.id)
        );
        this.integrationList1 = this.integrationList.filter(
          (el: any) => !this.settingData?.templateShare?.includes(el?.id)
        );
      });

    this.settingForm.get('personal').valueChanges.subscribe((val) => {
      this.integrationList1 = this.integrationList.filter(
        (el: any) => !val?.includes(el?.id)
      );
    });

    this.settingForm.get('integration').valueChanges.subscribe((val) => {
      this.personalList1 = this.personalList.filter(
        (el: any) => !val?.includes(el?.id)
      );
    });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canBulkAssignment = permissionsSet.has('Permissions.GlobalSettings.BulkAssignment');
        this.canBulkReassign = permissionsSet.has('Permissions.GlobalSettings.BulkReassign');
      });
    this.searchTermSubject.subscribe((data: any) => {
      this.searchTerm = data;
      this.sourceAndSearch();
    });
    this.sourceAndSearch();
    this._store
      .select(getIntegrationAssignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Object.keys(data).length) {
          // this.countryCode.setValue(data?.countryCode);
          const projectId =
            !data.project?.isDeleted && !data.project?.isArchived
              ? data.project?.id
              : null;
          this.project.patchValue(projectId);
          this.location.patchValue(data?.location?.id);
          // this.updatePreferredCountry(data?.countryCode);
        } else {
          this.project.patchValue(null);
          this.location.patchValue(null);
          // this.updatePreferredCountry(null);
        }
      });

    this._store
      .select(getWebhookAccount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.selectedInfo = data;
      });
    this.webhookFormGroup();

    this._store.dispatch(new FetchWebhook());
    this._store
      .select(getWebhook)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.tempVariables = data?.webhookVariablesWithDisplayName;
        this.payloadOptions = this.tempVariables;
        this.webhookfields = data?.webhookConstantVariables;
        this.webHookList = data?.webHookVariables;
        if (data?.webHookVariables) {
          this.webhookKeys = Object.keys(data.webHookVariables);
        }
      });
    this.webhookMappingForm
      .get('formName')
      .valueChanges.pipe(takeUntil(this.stopper))
      .subscribe((selectedWebhook: string) => {
        this.resetPayloadFields();
        if (selectedWebhook && this.webHookList) {
          this.payloadfieldkey = this.webHookList[selectedWebhook] || [];
          this.allPayloadOptions = [...this.payloadfieldkey];
          this.onPayloadChanged();
        }
      });
  }

  sourceAndSearch() {
    this.PageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1;
    if (this.displayName == 'Google ads leads form') {
      const payload = {
        LeadSource:
          IntegrationSource[this.name as keyof typeof IntegrationSource],
        SearchByName: this.searchTerm,
        PageSize: this.PageSize,
        PageNumber: this.currPageNumber,
      };
      this._store.dispatch(new FetchGoogleIntegrationList(payload));
    } else {
      const payload = {
        LeadSource:
          IntegrationSource[this.name as keyof typeof IntegrationSource],
        SearchByName: this.searchTerm,
        PageSize: this.PageSize,
        PageNumber: this.currPageNumber,
      };

      this._store.dispatch(new FetchIntegrationList(payload));
    }
    if (
      this.displayName == 'Google ads landing page' ||
      this.displayName == 'Google ads leads form' ||
      this.displayName == 'Microsoft Ads'
    ) {
      this._store.dispatch(new FetchAgencyNameList());
    }
  }

  ngAfterViewInit() {
    this.cdr.detectChanges();
  }

  preventClick(event: any) {
    event.preventDefault();
    return false;
  }

  handleEmailValidation(form: FormGroup, emailInput: HTMLInputElement, fieldName: string) {
    const control = form.get(fieldName);

    if (!control) return;

    if (emailInput.value && !this.validateEmail(emailInput.value)) {
      control.clearValidators();
    } else if (!emailInput.value) {
      control.setValidators([Validators.required]);
    }

    control.updateValueAndValidity();
  }

  downloadExcelFile() {
    const emailToInput = document.getElementById("emailToInput") as HTMLInputElement;
    if (emailToInput?.value && this.validateEmail(emailToInput.value)) {
      this.addInputField(emailToInput.value, emailToInput, this.integrationToRecipients());
    }
    const emailBccInput = document.getElementById("emailBccInput") as HTMLInputElement;
    if (emailBccInput?.value && this.validateEmail(emailBccInput.value)) {
      this.addInputField(emailBccInput.value, emailBccInput, this.integrationBccRecipients());
    }

    const emailCcInput = document.getElementById("emailCcInput") as HTMLInputElement;
    if (emailCcInput?.value && this.validateEmail(emailCcInput.value)) {
      this.addInputField(emailCcInput.value, emailCcInput, this.integrationCcRecipients());
    }

    if (this.isEditWebhook && this.integrationForm.contains('toRecipients')) {
      this.integrationForm.get('toRecipients')?.clearValidators();
      this.integrationForm.get('toRecipients')?.updateValueAndValidity();
    }
    if (!this.integrationForm.valid || this.doesAccountNameExist) {
      this.integrationForm.markAllAsTouched();
      return;
    }
    if (
      ((emailToInput?.value && !this.validateEmail(emailToInput.value)) ||
        (emailBccInput?.value && !this.validateEmail(emailBccInput.value)) ||
        (emailCcInput?.value && !this.validateEmail(emailCcInput.value))) && !this.isEditWebhook
    ) {
      return;
    }
    const toEmails = this.integrationToRecipients().value;
    const ccEmails = this.integrationCcRecipients().value;
    const bccEmails = this.integrationBccRecipients().value;

    const payload = {
      accountName: this.integrationForm.value.accountName.trim(),
      loginEmail: this.integrationForm.value.loginEmail,
      source: IntegrationSource[this.name as keyof typeof IntegrationSource],
      toRecipients: toEmails,
      ccRecipients: ccEmails,
      bccRecipients: bccEmails,
      PageSize: this.PageSize,
      PageNumber: this.currPageNumber,
    };
    if (this.displayName == 'Google ads leads form') {
      this._store.dispatch(new FetchGoogleIntegrationByIdSuccess(''));
      this._store.dispatch(new AddIntegrationGoogleLead(payload));
    } else {
      this._store.dispatch(new FetchIntegrationByIdSuccess(''));
      this._store.dispatch(new AddIntegration(payload));
    }

    this.isShowContentAndAdd = false;
    this.isShowAddAtListing = true;
    this.integrationForm.reset();
    this.resetIntegrationRecipients();
    this.modalService.hide()
  }

  reDownloadExcel(id: string) {
    this._store.dispatch(new FetchIntegrationByIdSuccess(''));
    if (this.displayName == 'Google ads leads form') {
      this._store.dispatch(new FetchGoogleIntegrationById(id));
    } else {
      this._store.dispatch(new FetchIntegrationById(id));
    }
    this._store
      .select(getExcelFileLink)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          window.open(res, '_self');
        }
      });
  }

  onCheckboxChange(event: any) {
    const isChecked = event.target.checked;
    this.isBulkAssignModel = true;
    if (isChecked) {
      this.selectedCount++;
    } else {
      this.selectedCount--;
    }
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      $event.preventDefault();
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
    this.isBulkAssignModel = false;
    this.isShowAssignModal = false;
    this.deselectAllRowsAndAdd();
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  deselectAllRowsAndAdd() {
    this.selectAllRows({ target: { checked: false } });
  }

  selectedCountReset() {
    this.selectedCount = 0;
    this.updatedIntegrationList.forEach((integration: any) => {
      if (integration.isSelected) this.selectedCount += 1;
    });
    if (this.selectedCount) {
      this.isBulkAssignModel = true;
    }
  }

  selectAllRows(event: any) {
    this.updatedIntegrationList.forEach((integration: any) => {
      integration.isSelected = event.target.checked;
    });
    this.onCheckboxChange(event);
    this.selectedCount = event.target.checked
      ? this.updatedIntegrationList?.length
      : 0;
  }

  isAllSelected(): boolean {
    return this.updatedIntegrationList?.length > 0 &&
      this.updatedIntegrationList.every(item => item.isSelected);
  }

  googleOAuthSignIn() {
    // Google's OAuth 2.0 endpoint for requesting an access token
    let oauth2Endpoint = env.googleOAuthEndpoint;

    // Create <form> element to submit parameters to OAuth 2.0 endpoint.
    let form = document.createElement('form');
    form.setAttribute('method', 'GET'); // Send as a GET request.
    form.setAttribute('action', oauth2Endpoint);
    let subDomain = this.domainName.host;
    if (subDomain.includes('www')) {
      subDomain = subDomain.replace('www.', '');
    }
    // Parameters to pass to OAuth 2.0 endpoint.
    let params: any = {
      client_id: env.googleClientId,
      redirect_uri: `https://${subDomain}/global-config`,
      response_type: 'code',
      scope: env.googleScope,
      include_granted_scopes: 'true',
      state: 'pass-through value',
      access_type: 'offline',
    };

    // Add form parameters as hidden input values.
    Object.entries(params).map((p: any) => {
      let input = document.createElement('input');
      input.setAttribute('type', 'hidden');
      input.setAttribute('name', p[0]);
      input.setAttribute('value', p[1]);
      form.appendChild(input);
    });

    // Add form to page and submit it to open the OAuth 2.0 endpoint.
    document.body?.appendChild(form);
    form.submit();
  }

  initDeleteIntegration(id: string, accountName: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: accountName,
      fieldType: 'account',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.deleteAccount(id);
        }
      });
    }
  }

  deleteAccount(id: string) {
    this.PageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1;
    let payload: any = {
      id: id,
      LeadSource: IntegrationSource[this.name as keyof typeof IntegrationSource],
      PageSize: this.PageSize,
      PageNumber: this.currPageNumber,
    }
    if (this.displayName == 'Google ads leads form') {
      this._store.dispatch(new DeleteGoogleIntegration(payload));
    } else {
      this._store.dispatch(new DeleteIntegration(payload));
    }
    this.modalRef.hide();
    this.isBulkAssignModel = false;
  }

  openAgentNameModal(
    agentModal: TemplateRef<any>,
    integration: any
    // id: string,
    // source: any,
    // accountName: string,
    // selectedAgencyName?: any
  ) {
    // if (this.displayName == 'Google ads leads form') {
    //   this.agencyAccountId = integration?.id;
    // } else {
    this.agencyAccountId = integration?.accountId;
    // }
    this.agencySource = integration?.leadSource;
    this.selectedAccountName = integration.accountName;
    this.agencyName.setValue(integration.agencyName);
    this.modalRef = this.modalService.show(agentModal, {
      class: 'modal-300 modal-dialog-centered ip-modal-unset',
      keyboard: false,
    });
  }


  updateAgentName() {
    let payload: any = {
      accountIds: Array.isArray(this.agencyAccountId)
        ? this.agencyAccountId
        : [this.agencyAccountId],
      source: this.agencySource,
      agencyName: this.agencyName.value || null,
      agencyId: EMPTY_GUID,
      name: this.displayName,
    };
    this._store.dispatch(new AgencyName(payload));
    if (this.displayName == 'Google ads leads form') {
      const payload = {
        SearchByName: this.searchTerm,
      };
      this._store.dispatch(new FetchGoogleIntegrationList(payload));
    }
    this.closeModal();
  }

  openProjectAndLocationModal(ProjectAndLocationModal: TemplateRef<any>, id: string, source: any, accountName: string) {
    this._store.dispatch(new FetchProjectIdWithName());
    this._store.dispatch(new FetchAllLocations());
    this._store.dispatch(new FetchIntegrationAssignment({ id, source }));
    this.isBulkAssignModel = true;
    this.isShowProjectAndLocationModal = true;
    this.selectedAccountName = accountName;
    this.selectedAccountId = id;
    this.selectedLeadSource = source;
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      ProjectAndLocationModal,
      initialState
    );
  }
  openCountryCode(CountryCodeModal: TemplateRef<any>, id: string, source: any, accountName: string) {
    this._store.dispatch(new FetchIntegrationAssignment({ id, source }));
    this.isBulkAssignModel = true;
    this.isShowCountryCodeModal = true;
    this.selectedAccountName = accountName;
    this.selectedAccountId = id;
    this.selectedLeadSource = source;
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      CountryCodeModal,
      initialState
    );
  }

  updateProjectAndLocation() {
    this.selectedIntegrations = this.updatedIntegrationList.filter(
      (item) => item.isSelected
    );
    let selectedIds: any = this.selectedIntegrations?.map((node: any) =>
      node?.accountId
    );
    if (selectedIds.length > 0) {
      const payload = {
        ids: selectedIds,
        projectId: this.project.value,
        locationId: this.location.value,
        source: this.selectedLeadSource,
      };
      this._store.dispatch(new updateIntegrationAssignment(payload, true));
    } else {
      const payload = {
        id: this.selectedAccountId,
        projectId: this.project.value,
        locationId: this.location.value,
        source: this.selectedLeadSource,
      };
      this._store.dispatch(new updateIntegrationAssignment(payload, false));
    }
    this.reset();
    this.isShowProjectAndLocationModal = false;
    this.modalService.hide();
  }

  doesAccountNameExists() {
    const form = this.displayName === 'Webhook' ? this.webhookMappingForm : this.integrationForm;
    const accountNameControl = form.get('accountName');

    if (!accountNameControl) return;

    let accountName = accountNameControl.value;

    accountNameControl.setErrors(null);

    if (!accountName) {
      accountNameControl.setErrors({ required: true });
      return;
    }

    if (!accountName.trim()) {
      accountNameControl.setErrors({ blank: true });
      return;
    }
    const source = IntegrationSource[this.name as keyof typeof IntegrationSource];

    this._store.dispatch(new DoesExistAccountName(accountName.trim(), source));
    this._store
      .select(doesAccountNameExists)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        accountNameControl.setErrors(data ? { alreadyExist: true } : null);
      });
  }

  openBulkReassignModel() {
    this.integrationForm.reset();
    if (this.displayName == 'Webhook') {
      this.webhookMappingForm.reset();
      this.addDefaultParameters();
    }
    this.selectedIntegrations = this.updatedIntegrationList.filter(
      (item) => item.isSelected
    );
    this.isBulkAssignModel = false;
    this.isShowAssignModal = true;
    this._store
      .select(getUserAssignmentByEntity)
      .pipe(takeUntil(this.stopper))
      .subscribe((res) => {
        this.assignedUserDetails = res?.id;
      });
    this._store.dispatch(new FetchPriorityList());
    this._store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'SubSource');
        this.bulkModuleId = filteredData.map((item) => item.id).join(', ');
      });
    let initialState: any = {
      class: 'modal-700 right-modal ip-modal-unset',
      initialState: {
        selectedIntegrations: this.selectedIntegrations,
        isBulkAssignModel: true,
        integration: this.selectedIntegrations,
        updatedIntegrationList: this.updatedIntegrationList
      }
    };
    this.modalRef = this.modalService.show(
      IntegrationAssignmentV2Component,
      initialState
    );
    this.modalRef.onHidden.subscribe(()=>{
      this.selectedCount = 0
    })
  }

  openBulkProjectLocationModel(ProjectAndLocationModal: TemplateRef<any>) {
    this.project.reset();
    this.location.reset();
    this.selectedIntegrations = this.updatedIntegrationList.filter(
      (item) => item.isSelected
    );
    this._store.dispatch(new FetchProjectIdWithName());
    this._store.dispatch(new FetchAllLocations());
    this.isShowProjectAndLocationModal = true;
    this.isBulkAssignModel = false;
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      ProjectAndLocationModal,
      initialState
    );
  }

  openBulkCountryCodeModal(CountryCodeModal: TemplateRef<any>) {
    // this.updatePreferredCountry(null);
    this.selectedIntegrations = this.updatedIntegrationList.filter(
      (item) => item.isSelected
    );
    this.isShowCountryCodeModal = true;
    this.isBulkAssignModel = false;
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      CountryCodeModal,
      initialState
    );
  }

  openBulkAgencyModal(AgencyModal: TemplateRef<any>,) {
    this.agencyName.reset();
    this.selectedIntegrations = this.updatedIntegrationList.filter(
      (item) => item.isSelected
    );
    this.agencyAccountId = this.selectedIntegrations?.map((node: any) =>
      node?.accountId
    );

    this._store.dispatch(new FetchAgencyNameList());
    this.isShowAgencyModal = true;
    this.isBulkAssignModel = false;
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      AgencyModal,
      initialState
    );
  }

  openAssignmentModal(integration: any) {
    this.isBulkAssignModel = true;
    this.isShowAssignModal = true;
    this.selectedAccountName = integration.accountName;
    this.selectedAccountId = integration.accountId;
    this._store.dispatch(new FetchPriorityList());

    let initialState = {
      class: 'modal-700 right-modal ip-modal-unset',
      initialState: {
        integration: integration
      }
    };

    this.modalRef = this.modalService.show(
      IntegrationAssignmentV2Component,
      initialState
    );

  }

  closeModal() {
    this.modalService.hide();
    this.reset();
  }

  reset() {
    this.updatedIntegrationList.forEach((item) => (item.isSelected = false));
    this.selectedCount = 0;
    this.integrationForm.reset();
    this.assignedUser = [];
    this.project.reset();
    this.location.reset();
    this.agencyName.reset();
    if (this.displayName == 'Webhook') {
      this.isEditWebhook = false;
      this.webhookMappingForm.reset();
      this.addDefaultParameters();
    }
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }

  webhookFormGroup() {
    this.webhookMappingForm = this.fb.group({
      accountName: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      loginEmail: [null, ValidationUtil.emailValidatorMinLength],
      toRecipients: this.fb.array([], [Validators.required, Validators.minLength(1)]),
      ccRecipients: this.fb.array([]),
      bccRecipients: this.fb.array([]),
      formName: [null, Validators.required],
      parameters: this.fb.array([])
    });

    this.addDefaultParameters();
  }

  addDefaultParameters() {
    const parametersArray = this.parameters;
    parametersArray.clear();
    parametersArray.push(
      this.fb.group({
        payloadKey: ['#Name#', Validators.required],
        payloadValue: [null, Validators.required],
      })
    );

    parametersArray.push(
      this.fb.group({
        payloadKey: ['#Mobile#', Validators.required],
        payloadValue: [null, Validators.required],
      })
    );
  }

  get parameters() {
    return this.webhookMappingForm.get('parameters') as FormArray;
  }

  onAddPayload() {
    if (
      this.payloadOptions?.length &&
      this.parameters?.length < this.tempVariables?.length
    ) {
      this.parameters?.push(
        this.fb.group({
          payloadKey: [null, Validators.required],
          payloadValue: [null, Validators.required],
        })
      );
      this.onPayloadOptionChanged();
    }
  }

  onPayloadOptionChanged(): void {
    let selectedValues: string[] = this.webhookMappingForm
      .get('parameters')
      .value.map((control: { payloadKey: string }) => control.payloadKey);

    selectedValues = selectedValues?.filter((value) => value !== null);
    const defaultKeys = ['#Name#', '#Mobile#'];
    this.payloadOptions = this.tempVariables?.filter(
      (option: any) =>
        !selectedValues.includes(option.value) &&
        !defaultKeys.includes(option.value)
    );
    const selectedDefaultKeys = selectedValues.filter((key) =>
      defaultKeys.includes(key)
    );
    // this.payloadOptions = [
    //   ...this.payloadOptions
    // ];
    this.temp = [
      ...this.tempVariables?.filter((option: { value: string }) =>
        selectedDefaultKeys.includes(option.value)
      ),
    ];
  }

  downloadwebhookExcelFile() {
    this.PageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1
    const { webhookMappingForm } = this;
    const integrateData = webhookMappingForm.value;
    const webhookEmailToInput = document.getElementById("webhookEmailToInput") as HTMLInputElement;
    if (webhookEmailToInput?.value && this.validateEmail(webhookEmailToInput.value)) {
      this.addInputField(webhookEmailToInput.value, webhookEmailToInput, this.webhookToRecipients());
    }
    const webhookEmailBccInput = document.getElementById("webhookEmailBccInput") as HTMLInputElement;
    if (webhookEmailBccInput?.value && this.validateEmail(webhookEmailBccInput.value)) {
      this.addInputField(webhookEmailBccInput.value, webhookEmailBccInput, this.webhookBccRecipients());
    }

    const webhookEmailCcInput = document.getElementById("webhookEmailCcInput") as HTMLInputElement;
    if (webhookEmailCcInput?.value && this.validateEmail(webhookEmailCcInput.value)) {
      this.addInputField(webhookEmailCcInput.value, webhookEmailCcInput, this.webhookCcRecipients());
    }

    if (this.isEditWebhook && this.webhookMappingForm.contains('toRecipients')) {
      this.webhookMappingForm.get('toRecipients')?.clearValidators();
      this.webhookMappingForm.get('toRecipients')?.updateValueAndValidity();
    }

    if (!webhookMappingForm.valid) {
      webhookMappingForm.markAllAsTouched();
      return;
    }

    if (
      ((webhookEmailToInput?.value && !this.validateEmail(webhookEmailToInput.value)) ||
        (webhookEmailBccInput?.value && !this.validateEmail(webhookEmailBccInput.value)) ||
        (webhookEmailCcInput?.value && !this.validateEmail(webhookEmailCcInput.value))) && !this.isEditWebhook
    ) {
      return;
    }

    const toEmails = this.webhookToRecipients().value;
    const ccEmails = this.webhookCcRecipients().value;
    const bccEmails = this.webhookBccRecipients().value;

    const payloadForPushEndPointObject =
      this.mapPayloadForPushEndPoint(integrateData);
    const keysToInclude = ['#Notes#', '#Name#', '#Project#'];

    for (const key in payloadForPushEndPointObject) {
      if (
        payloadForPushEndPointObject.hasOwnProperty(key) &&
        Array.isArray(payloadForPushEndPointObject[key])
      ) {
        if (keysToInclude.includes(key)) {
          payloadForPushEndPointObject[key] =
            payloadForPushEndPointObject[key].join(', ');
        }
      }
    }
    if (payloadForPushEndPointObject['#LeadSource#']) {
      payloadForPushEndPointObject['#LeadSource#'] = String(
        payloadForPushEndPointObject['#LeadSource#']
      );
    }

    if (!webhookMappingForm.valid || this.doesAccountNameExist) {
      validateAllFormFields(webhookMappingForm);
      this.parameters.controls.forEach((control) => control.markAllAsTouched());
      return;
    }
    let payload: any = {
      accountName: integrateData.accountName.trim(),
      loginEmail: integrateData.loginEmail,
      source: IntegrationSource[this.name as keyof typeof IntegrationSource],
      toRecipients: toEmails,
      ccRecipients: ccEmails,
      bccRecipients: bccEmails,
      formName: integrateData.formName,
      parameters: payloadForPushEndPointObject,
      PageSize: this.PageSize,
      PageNumber: this.currPageNumber,
    };
    this._store.dispatch(new FetchIntegrationByIdSuccess(''));

    this._store.dispatch(new FetchIntegrationByIdSuccess(''));
    if (this.isEditWebhook) {
      payload = {
        ...payload,
        accountId: this.selectedInfo.id,
      };
      this._store.dispatch(new UpdateWebhook(payload));
    } else {
      this._store.dispatch(new AddIntegration(payload));
    }

    this.isShowContentAndAdd = false;
    this.isShowAddAtListing = true;
    this.isEditWebhook = false;
    this.webhookMappingForm.reset();
    this.resetWebhookRecipients();
    this.addDefaultParameters();
    this.modalService.hide();
  }

  mapPayloadForPushEndPoint(integrateData: any) {
    return integrateData.parameters.reduce(
      (
        acc: any,
        {
          payloadKey,
          payloadValue,
        }: { payloadKey: string; payloadValue: string }
      ) => {
        if (payloadKey !== null && payloadValue !== null) {
          acc[payloadKey] = payloadValue;
        }
        return acc;
      },
      {}
    );
  }

  onRemovePayload(index: number) {
    if (this.parameters.length > 1) {
      this.parameters.removeAt(index);
    }
    this.uniqueValueArray = [];
    this.parameters.controls.forEach((element: any) =>
      element.get('payloadValue').updateValueAndValidity()
    );
    this.onPayloadOptionChanged();
  }

  onEditWebhook(listingNewAccount: TemplateRef<any>, id: string) {
    // this.isShowAddAtListing = false;
    this.isBulkAssignModel = false;
    this.isEditWebhook = true;
    this.selectedAccountId = id;
    this.deselectAllRowsAndAdd();
    this._store.dispatch(new FetchWebhookAccount(id));

    this._store
      .select(getWebhookAccountIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.iswebhookAccountLoading = isLoading;
        if (!this.iswebhookAccountLoading) {
          this.webhookMappingForm?.patchValue({
            accountName: this.selectedInfo?.accountName,
            loginEmail: this.selectedInfo?.loginEmail,
            formName: this.selectedInfo?.webhookPayloadMapping?.formName,
          });

          const toRecipientsArray = this.webhookMappingForm.get('toRecipients') as FormArray;
          toRecipientsArray.clear();

          this.selectedInfo.toRecipients?.forEach((email: any) => {
            toRecipientsArray.push(new FormControl(email));
          });

          const ccRecipientsArray = this.webhookMappingForm.get('ccRecipients') as FormArray;
          ccRecipientsArray.clear();

          this.selectedInfo.ccRecipients?.forEach((email: any) => {
            ccRecipientsArray.push(new FormControl(email));
          });

          const bccRecipientsArray = this.webhookMappingForm.get('bccRecipients') as FormArray;
          bccRecipientsArray.clear();

          this.selectedInfo.bccRecipients?.forEach((email: any) => {
            bccRecipientsArray.push(new FormControl(email));
          });

          const webhookMappings =
            this.selectedInfo?.webhookPayloadMapping?.webhookMappings;
          if (webhookMappings) {
            const parametersArray = this.webhookMappingForm.get(
              'parameters'
            ) as FormArray;
            parametersArray.clear();

            const priorityKeys = [
              '#Name#',
              '#Mobile#',
              '#LeadSource#',
              '#Email#',
            ];
            priorityKeys.forEach((priorityKey) => {
              if (webhookMappings[priorityKey] !== undefined) {
                let payloadValue = webhookMappings[priorityKey];
                if (typeof payloadValue === 'string') {
                  payloadValue = payloadValue.trim();
                  if (priorityKey === '#LeadSource#') {
                    payloadValue = +payloadValue;
                  }
                }
                if (priorityKey === '#Name#') {
                  if (!Array.isArray(payloadValue)) {
                    payloadValue = [payloadValue];
                  }
                }
                parametersArray.push(
                  this.fb.group({
                    payloadKey: [priorityKey, Validators.required],
                    payloadValue: [payloadValue, Validators.required],
                  })
                );
              }
            });

            Object.keys(webhookMappings).forEach((key) => {
              if (!priorityKeys.includes(key)) {
                let payloadKey = key;
                let payloadValue = webhookMappings[key];
                if (typeof payloadValue === 'string') {
                  payloadValue = payloadValue
                    .split(',')
                    .map((val) => val.trim());
                }
                if (key === '#Notes#' || key === '#Project#') {
                  if (!Array.isArray(payloadValue)) {
                    payloadValue = [payloadValue];
                  }
                } else {
                  if (Array.isArray(payloadValue)) {
                    payloadValue = payloadValue[0];
                  }
                }
                parametersArray.push(
                  this.fb.group({
                    payloadKey: [payloadKey, Validators.required],
                    payloadValue: [payloadValue, Validators.required],
                  })
                );
              }
            });

            this.parameters.controls.forEach((control) => {
              control.markAsTouched();
              control.updateValueAndValidity();
            });

            this.updatePayloadOptions();
            this.onPayloadChanged();
          }
        }
      });
    let initialState: any = {
      class: 'modal-550 right-modal ip-modal-unset',
    };
    this.modalRef = this.modalService.show(
      listingNewAccount,
      initialState
    );
  }

  updatePayloadOptions() {
    const selectedKeys = this.parameters.controls
      .map((control) => control.get('payloadKey').value)
      .filter((key) => key);
    this.payloadOptions = this.payloadOptions.map((option: { value: any }) => ({
      ...option,
      disabled: selectedKeys.includes(option.value),
    }));
  }

  copyToClipboard(): void {
    const url = `https://connect.leadrat.com/api/v1/integration/webhook/push/properties/${this.tenantId}`;
    navigator.clipboard.writeText(url).then(() => {
      this._notificationsService.success(
        this._translateService.instant(`Copied to Clipboard`)
      );
    });
  }

  onPayloadChanged(): void {
    const selectedValues = this.parameters.controls
      .map((control) => control.get('payloadValue').value)
      .flat()
      .filter((value) => value !== null && value !== '');
    const flattenedSelectedValues = selectedValues.flat();
    this.payloadfieldkey = this.allPayloadOptions.filter((option) => {
      const isSelected = flattenedSelectedValues.includes(option);
      return !isSelected;
    });
  }

  webbookCancel() {
    this.isShowAddAtListing = true;
    this.isEditWebhook = false;
    this.webhookMappingForm.reset();
    this.addDefaultParameters();
    this.modalService.hide();
  }

  resetPayloadFields() {
    const parametersArray = this.parameters;
    parametersArray.clear();
    this.addDefaultParameters();
    this.onPayloadOptionChanged();
  }

  getPayloadValueTooltip(control: AbstractControl | null): string {
    if (!control) return '';

    const payloadKey = control.get('payloadKey')?.value;
    const value = control.get('payloadValue')?.value;

    if (payloadKey === '#LeadSource#') {
      if (Array.isArray(value)) {
        return value
          .map((item: number | string) => IntegrationSource[item as keyof typeof IntegrationSource] || item)
          .join(', ');
      }
      return IntegrationSource[value as keyof typeof IntegrationSource] || value || '';
    }

    if (Array.isArray(value)) {
      return value.map((item: any) => item?.displayName || item).join(', ');
    }

    return value?.displayName || value || '';
  }


  addNewAccount(addAccount: TemplateRef<any>) {
    // this.isShowAddAccountBtn = false;
    this.isEditWebhook = false;
    this.webhookMappingForm.reset();
    this.integrationForm.reset();
    this.addDefaultParameters();
    this.deselectAllRowsAndAdd();
    this.resetIntegrationRecipients();
    this.resetWebhookRecipients();
    let initialState: any = {
      class: 'modal-550 right-modal ip-modal-unset',
    };
    this.modalRef = this.modalService.show(
      addAccount,
      initialState
    );

  }

  addLisitingNewAccount(listingNewAccount: TemplateRef<any>) {
    this.isEditWebhook = false;
    // this.isShowAddAtListing = false;
    // this.isConfigVisible = false
    this.webhookMappingForm.reset();
    this.addDefaultParameters();
    this.deselectAllRowsAndAdd();

    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      listingNewAccount,
      initialState
    );
  }

  integrationToRecipients(): FormArray {
    return this.integrationForm.get('toRecipients') as FormArray;
  }

  integrationCcRecipients(): FormArray {
    return this.integrationForm.get('ccRecipients') as FormArray;
  }

  integrationBccRecipients(): FormArray {
    return this.integrationForm.get('bccRecipients') as FormArray;
  }

  webhookToRecipients(): FormArray {
    return this.webhookMappingForm.get('toRecipients') as FormArray;
  }

  webhookCcRecipients(): FormArray {
    return this.webhookMappingForm.get('ccRecipients') as FormArray;
  }

  webhookBccRecipients(): FormArray {
    return this.webhookMappingForm.get('bccRecipients') as FormArray;
  }

  resetIntegrationRecipients() {
    this.integrationForm.setControl('toRecipients', this.fb.array([], [Validators.required, Validators.minLength(1)]));
    this.integrationForm.setControl('ccRecipients', this.fb.array([]));
    this.integrationForm.setControl('bccRecipients', this.fb.array([]));
  }

  resetWebhookRecipients() {
    this.webhookMappingForm.setControl('toRecipients', this.fb.array([], [Validators.required, Validators.minLength(1)]));
    this.webhookMappingForm.setControl('ccRecipients', this.fb.array([]));
    this.webhookMappingForm.setControl('bccRecipients', this.fb.array([]));
  }

  addInputField(email: string, emailInput: HTMLInputElement, formArray: FormArray) {
    const trimmedEmail = email?.trim();
    if (!trimmedEmail || !this.validateEmail(trimmedEmail)) {
      return;
    }

    formArray.push(new FormControl(trimmedEmail));
    emailInput.value = '';
    formArray.markAsUntouched();
    formArray.updateValueAndValidity();

    const parentForm = formArray === this.integrationToRecipients() ?
      this.integrationForm :
      formArray === this.webhookToRecipients() ?
        this.webhookMappingForm : null;

    if (parentForm) {
      parentForm.updateValueAndValidity();
    }
  }

  removeEmail(index: number, formArray: FormArray, emailInput?: HTMLInputElement) {
    formArray.removeAt(index);

    const parentForm = formArray === this.integrationToRecipients() ?
      this.integrationForm :
      formArray === this.webhookToRecipients() ?
        this.webhookMappingForm : null;

    const isInputEmpty = !emailInput?.value.trim();
    const isFormArrayEmpty = formArray.length === 0;

    if (parentForm) {
      const control = parentForm.get('toRecipients');
      control?.setValidators([Validators.required]);
      if (isFormArrayEmpty && isInputEmpty) {
        control?.markAsTouched();
      } else {
        control?.markAsUntouched();
      }

      control?.updateValueAndValidity();
    }
  }

  validateEmail(email: string): boolean {
    if (!email || !email.trim()) return false;
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailPattern.test(email.trim());
  }

  toggleWhatsappSettings(whatsappSetting: TemplateRef<any>) {
    // this.isConfigVisible = !this.isConfigVisible;
    this.isShowAddAtListing = true;
    this.deselectAllRowsAndAdd();
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      whatsappSetting,
      initialState
    );
  }

  updateSettingsList() {
    const settingData = this.settingForm.value;
    const prevSelectedData = [
      ...(this.settingData?.templateShare ?? []),
      ...(this.settingData?.openConversation ?? []),
    ];
    const presentSelectedData = [
      ...settingData?.personal,
      ...settingData?.integration,
    ];
    let everyTimeUserIds: any = prevSelectedData.filter(
      (el: any) => !presentSelectedData.includes(el)
    );

    let payload: any = {
      settings: [
        {
          whatsappThrough: WhatsappThrough.AskEveryTime,
          userIds: everyTimeUserIds,
        },
        {
          whatsappThrough: WhatsappThrough.TemplateShare,
          userIds: settingData?.personal,
        },
        {
          whatsappThrough: WhatsappThrough.OpenConversation,
          userIds: settingData?.integration,
        },
      ],
    };
    this._store.dispatch(new UpdateWhatsappSettingList(payload));
    this.closeModal();
  }

  assignPageSize() {
    this.PageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1;
    this._store.dispatch(
      new AssignPageSize(this.PageSize, this.currPageNumber)
    );
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: this.currPageNumber,
      LeadSource: IntegrationSource[this.name as keyof typeof IntegrationSource],
      SearchByName: this.searchTerm,
    };
    if (this.displayName == 'Google ads leads form') {
      this._store.dispatch(new FetchGoogleIntegrationList(this.filtersPayload));
    } else {
      this._store.dispatch(new FetchIntegrationList(this.filtersPayload));
    }
    this.deselectAllRowsAndAdd();
  }

  onPageChange(offset: number) {
    this.currOffset = offset;
    this.currPageNumber = offset + 1;
    this._store.dispatch(
      new AssignPageSize(this.PageSize, this.currPageNumber)
    );
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: this.currPageNumber,
      LeadSource: IntegrationSource[this.name as keyof typeof IntegrationSource],
      SearchByName: this.searchTerm,
    };
    if (this.displayName == 'Google ads leads form') {
      this._store.dispatch(new FetchGoogleIntegrationList(this.filtersPayload));
    } else {
      this._store.dispatch(new FetchIntegrationList(this.filtersPayload));
    }
    this.deselectAllRowsAndAdd();

  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
