  <div class="border banner-hover py-12 br-30 mr-16 ph-mr-0 cursor-pointer h-55px" *ngIf="!isSkeleton" (click)="navigate()">
    <div class="flex-between px-12">
      <div class="align-center">
        <div class="w-26 h-26 br-20 flex-center mr-12" [ngClass]="moduleSetting?.iconColor">
          <span class="icon ic-xxs" [ngClass]="moduleSetting?.iconClass"></span>
        </div>
        <div>
        <div class="text-black-10 fw-600 clear-margin">{{ moduleSetting?.label | translate}}
        </div>
        <div [title]="moduleSetting?.description" class="fw-semi-bold text-dark-800 text-sm text-truncate-1 break-all">
          {{ moduleSetting?.description | translate}} </div>
        </div>
      </div>
      <div class="arrow-icon rotate-90 position-absolute right-20 top-22 flex-center opacity-0">
        <span class="icon ic-dark-800 ic-triangle-up ic-x-xs"></span>
      </div>
    </div>
  </div>

  <div class="border banner-hover py-12 br-30 mr-16 ph-mr-0 cursor-pointer pe-none blinking" *ngIf="isSkeleton">
    <div class="flex-between px-12">
      <div class="align-center">
        <div class="w-26 h-26 br-20 flex-center mr-12 bg-grey">
        </div>
        <div>
        <div class="text-black-10 clear-margin pb-2">
          <span class="bg-grey">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        </div>
        <div class="fw-semi-bold text-dark-800 text-sm pt-2">
          <span class="bg-grey text-truncate-1">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        </div>
        </div>
      </div>
    </div>
  </div>