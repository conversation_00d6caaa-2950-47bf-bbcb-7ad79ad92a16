import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ModuleSettings } from 'src/app/core/interfaces/global-settings';
import { IvrComponent } from 'src/app/features/global-config/settings/communication/ivr/ivr.component';

@Component({
  selector: 'module-settings-card',
  templateUrl: './module-settings-card.component.html',
})
export class ModuleSettingsCardComponent {

  @Input() moduleSetting: ModuleSettings;
  @Input() isSkeleton: boolean = false;

  constructor(
    public router: Router,
    private modalService: BsModalService
  ) {
  }

  navigate() {
    if (this.moduleSetting?.name === 'ivr') {
      this.openIntegrateIvr();
      return;
    }
    this.router.navigate([this.moduleSetting?.path]);
  }

  openIntegrateIvr() {
    let initialState: any = {
      image: 'assets/images/integration/ivr-logo.svg',
      name: 'IVR',
    };

    this.modalService.show(
      IvrComponent,
      Object.assign({}, { class: 'right-modal ip-modal-unset', initialState , ignoreBackdropClick: true })
    );
  }

}
