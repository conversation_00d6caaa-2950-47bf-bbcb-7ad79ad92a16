import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON>ter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
} from '@angular/core';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { skipWhile, Subject, take, takeUntil } from 'rxjs';

import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getAssignedToDetails,
  getPages,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  AssignFilterPayload,
  CreateEmailSMTP,
  DeleteEmailSMTP,
  EditEmailSMTP,
  SendTestEmail,
  getEmailSMTPList,
} from 'src/app/reducers/email/email-settings.action';
import {
  getAddSMTPIsLoading,
  getEditSMTPIsLoading,
  getFiltersPayload,
  getTotalCount,
  selectEmailSettings,
  selectEmailSettingsIsLoading,
} from 'src/app/reducers/email/email-settings.reducer';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'email-settings',
  templateUrl: './email-settings.component.html',
})
export class EmailSettingsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  searchTermSubject = new Subject<string>();
  emailSettingsForm: FormGroup;
  emailSMTPList: any[];
  allUsersList: any[];
  getAssignedToDetails = getAssignedToDetails;
  selectedEmail: any = null;
  currPageNumber: number;
  currPageSize: number = PAGE_SIZE;
  searchTerm: string;
  filterPayload: any;
  totalPages: number;
  getPages = getPages;
  currOffset: number = 0;
  showEntriesSize: number[] = SHOW_ENTRIES;
  pageEntry: FormControl = new FormControl(this.currPageSize);
  emailSMTPListIsLoading: boolean;
  addSMTPIsLoading: boolean;
  editSMTPIsLoading: boolean;

  constructor(
    private metaTitle: Title,
    private headerTitle: HeaderTitleService,
    private router: Router,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private store: Store<AppState>,
    private fb: FormBuilder
  ) {
    this.metaTitle.setTitle('CRM | Global Config | Email');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this.emailSettingsForm = this.fb.group({
      senderEmail: ['', Validators.required],
      SMTPServer: ['', Validators.required],
      port: [null, Validators.required],
      username: ['', Validators.required],
      password: ['', Validators.required],
      selectionType: ['selectAll'],
      usersSelection: [null],
    });
  }

  ngOnInit(): void {
    this.searchTermSubject.subscribe(() => {
      this.currOffset = 0;
      this.currPageNumber = 1;
      this.currPageSize = this.pageEntry.value;
      this.filterFormList();
    });

    this.store.dispatch(new FetchUsersListForReassignment());
    this.store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => {
        if (data) {
          this.filterPayload = data;
          this.currPageNumber = data.pageNumber;
          this.currPageSize = data.pageSize;
          this.searchTerm = data.searchTerm;
          this.currOffset = data.pageNumber - 1;
          this.pageEntry.setValue(this.currPageSize);
        }
      });
    this.store.dispatch(new getEmailSMTPList());
    this.store
      .select(getTotalCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => {
        this.totalPages = data;
      });
    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => {
        if (data) {
          this.allUsersList = data
            .filter((user: any) => user.isActive)
            .map((user: any) => {
              return {
                ...user,
                fullName: `${user.firstName} ${user.lastName}`,
              };
            });
        }
      });

    this.store
      .select(selectEmailSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => {
        if (data) {
          this.emailSMTPList = data;
        }
      });

    this.store
      .select(selectEmailSettingsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.emailSMTPListIsLoading = loading;
      });

    this.modalService.onHide.subscribe(() => {
      this.emailSettingsForm.reset();
      this.emailSettingsForm.patchValue({
        senderEmail: '',
        SMTPServer: '',
        port: null,
        username: '',
        password: '',
        selectionType: 'selectAll',
        usersSelection: null,
      });
      this.selectedEmail = null;
      Object.keys(this.emailSettingsForm.controls).forEach((controlName) => {
        this.emailSettingsForm.get(controlName).markAsUntouched();
      });
    });
  }

  onSaveEmailSMTP() {
    if (!this.emailSettingsForm.valid) {
      validateAllFormFields(this.emailSettingsForm);
      return;
    }
    let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    let emailForm = this.emailSettingsForm.value;
    let payload: any = {
      userId: userId,
      from: emailForm.senderEmail,
      serverName: emailForm.SMTPServer,
      port: emailForm.port,
      userName: emailForm.username,
      password: emailForm.password,
      isEnabledForAll: emailForm.selectionType === 'selectAll' ? true : false,
      userIds: emailForm.usersSelection,
    };
    if (this.selectedEmail) {
      payload.id = this.selectedEmail.id;
      this.editSMTPIsLoading = true;
      this.store.dispatch(new EditEmailSMTP(payload));
      this.store.select(getEditSMTPIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => isLoading),
        take(1)
      )
      .subscribe((isLoading: boolean) => {
        this.editSMTPIsLoading = isLoading;
        this.modalRef.hide();
      });
      return;
    }
    this.addSMTPIsLoading = true;
    this.store.dispatch(new CreateEmailSMTP(payload));
    this.store.select(getAddSMTPIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => isLoading),
        take(1)
      )
      .subscribe((isLoading: boolean) => {
        this.addSMTPIsLoading = isLoading;
        this.modalRef.hide();
      });
  }

  sendTestMail() {
    if (!this.emailSettingsForm.valid) {
      validateAllFormFields(this.emailSettingsForm);
      return;
    }
    let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    let emailForm = this.emailSettingsForm.value;
    let payload = {
      CurrentUserId: userId,
      From: emailForm.senderEmail,
      ServerName: emailForm.SMTPServer,
      Port: emailForm.port,
      UserName: emailForm.username,
      Password: emailForm.password,
      Priority: 1,
      To: ['<EMAIL>'],
      Body: 'Test Mail Success!',
      Subject: 'Test Mail',
    };
    this.store.dispatch(new SendTestEmail(payload));
  }

  openDeleteModal(id: string, deleteModal: TemplateRef<any>) {
    this.modalRef = this.modalService.show(deleteModal, {
      class: 'modal-400 top-modal ph-modal-unset',
    });
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteEmailSMTP(id));
        }
      });
    }
  }

  openEditModal(form: any, template: TemplateRef<any>) {
    this.selectedEmail = form;
    this.emailSettingsForm.patchValue({
      senderEmail: form.from,
      SMTPServer: form.serverName,
      port: form.port,
      username: form.userName,
      password: form.password,
      selectionType: form.isEnabledForAll ? 'selectAll' : 'selectUsers',
      usersSelection: form.userIds,
    });
    this.modalRef = this.modalService.show(template, {
      class: 'right-modal modal-350 ip-modal-unset',
    });
  }

  openAssignUserModal(form: any, assignUserModal: TemplateRef<any>) {
    this.emailSettingsForm.get('usersSelection').setValue(form.userIds);
    this.emailSettingsForm
      .get('selectionType')
      .setValue(form.isEnabledForAll ? 'selectAll' : 'selectUsers');
    this.selectedEmail = form;
    this.modalRef = this.modalService.show(assignUserModal, {
      class: 'right-modal modal-350 ip-modal-unset',
    });
  }

  updateUsers() {
    let payload = {
      ...this.selectedEmail,
      isEnabledForAll:
        this.emailSettingsForm.get('selectionType').value === 'selectAll'
          ? true
          : false,
      userIds:
        this.emailSettingsForm.get('selectionType').value === 'selectUsers'
          ? this.emailSettingsForm.get('usersSelection').value
          : [],
    };
    this.store.dispatch(new EditEmailSMTP(payload));
    this.modalRef.hide();
  }

  openAddEmailSMTPModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, {
      class: 'right-modal modal-350 ip-modal-unset',
    });
  }

  onPageChange(offset: number) {
    this.currPageNumber = offset + 1;
    this.currOffset = offset;
    this.filterFormList();
  }

  assignPageSize() {
    this.currPageNumber = 1;
    this.currOffset = 0;
    this.currPageSize = this.pageEntry.value;
    this.filterFormList();
  }

  filterFormList() {
    this.filterPayload = {
      pageNumber: this.currPageNumber,
      pageSize: this.currPageSize,
      searchTerm: this.searchTerm,
    };
    this.store.dispatch(new AssignFilterPayload(this.filterPayload));
    this.store.dispatch(new getEmailSMTPList());
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  removeUser(userId: string) {
    let filteredUsers = this.emailSettingsForm
      .get('usersSelection')
      ?.value?.filter((user: string) => user !== userId);
    this.emailSettingsForm.get('usersSelection').setValue(filteredUsers);
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
    this.filterPayload = {
      ...this.filterPayload,
      searchTerm: '',
    };
    this.store.dispatch(new AssignFilterPayload(this.filterPayload));
  }
}
