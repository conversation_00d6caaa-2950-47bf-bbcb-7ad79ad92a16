import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import { distinctUntilChanged, takeUntil } from 'rxjs';

import {
  IVR_TYPE,
  VALIDATION_CLEAR,
  VALIDATION_SET
} from 'src/app/app.constants';
import { CallType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  onlyNumbers,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  CommonIvrIntegration,
  CommonIvrIntegrationSuccess,
  FetchIVRList,
} from 'src/app/reducers/Integration/integration.actions';
import {
  getAgencyNameList,
  getCommonIvrExcelLink,
} from 'src/app/reducers/Integration/integration.reducer';
import { FetchTempVariables } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous, getTempVariables } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getProjectsIDWithName } from 'src/app/reducers/project/project.reducer';
import { getAllLocations } from 'src/app/reducers/site/site.reducer';

@Component({
  selector: 'add-ivr',
  templateUrl: './add-ivr.component.html',
})
export class AddIvrComponent implements OnInit {
  @Output() closeAdd = new EventEmitter<string>();
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  integrateForm: FormGroup;
  ivrTypeList: Array<Object> = IVR_TYPE;
  allProjectList: Array<any> = [];
  placesList: Array<any> = [];
  agencyNameList: string;
  tempVariables: any;
  payloadOptions: any;
  uniqueValueArray: any[] = [];
  globalSettingsData: any;
  onlyNumbers = onlyNumbers

  constructor(private store: Store<AppState>, private fb: FormBuilder) { }

  ngOnInit() {
    this.assignFormGroup();

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
      });

    this.store.dispatch(new FetchTempVariables());
    this.store.select(getTempVariables)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.tempVariables = data?.ivrVariablesWithDisplayName;
        this.payloadOptions = this.tempVariables;

      });
    this.store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        this.allProjectList = res?.filter((data: any) => data.name)
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this.store
      .select(getAllLocations)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.integrateForm
      .get('ivr')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(() => {
        const ivrControl = this.integrateForm.get('ivr');
        this.integrateForm.reset({ ivr: ivrControl.value });

        Object.keys(this.integrateForm.controls).forEach((key) => {
          if (key !== 'ivr') {
            this.integrateForm.get(key)?.reset();
          }
        });
      });
  }

  downloadExcelFile() {
    const { integrateForm } = this;
    if (integrateForm.get('ivr').value === 'Inbound') {
      toggleValidation(VALIDATION_CLEAR, integrateForm, 'methodType');
      toggleValidation(VALIDATION_CLEAR, integrateForm, 'contentType');
      toggleValidation(VALIDATION_CLEAR, integrateForm, 'baseURL');
      this.clearValidators(this.queryParameters.controls);
      this.clearValidators(this.headerVariables.controls);
      this.clearValidators(this.bodyVariables.controls);
    } else {
      toggleValidation(VALIDATION_SET, integrateForm, 'methodType', [
        Validators.required,
      ]);
      toggleValidation(VALIDATION_SET, integrateForm, 'contentType', [
        Validators.required,
      ]);
      toggleValidation(VALIDATION_SET, integrateForm, 'baseURL', [
        Validators.required,
      ]);
    }

    if (!integrateForm.valid) {
      validateAllFormFields(integrateForm);
      this.payloadForPushEndpoint.controls.forEach((control) =>
        control.markAllAsTouched()
      );
      this.queryParameters.controls.forEach((control) =>
        control.markAllAsTouched()
      );
      this.headerVariables.controls.forEach((control) =>
        control.markAllAsTouched()
      );
      this.bodyVariables.controls.forEach((control) =>
        control.markAllAsTouched()
      );
      return;
    }

    const integrateData = integrateForm.value;
    const payloadForPushEndPointObject =
      this.mapPayloadForPushEndPoint(integrateData);

    let payload: any = {
      accountName: integrateData.accountName,
      source: 1,
      callType: CallType[integrateData.ivr],
      canIncludeApiKeyInPayloadHeader: integrateData.canIncludeApiKeyInHeader || false,
      ivrAssignmentDtos: integrateData.isVirtualNumberRequiredForOutbound
        ? integrateData.ivrAssignmentDtos
          .filter(
            (item: any) =>
              item.virtualNumber !== '' && item.virtualNumber !== null
          )
          .map((item: any) => ({
            ...item,
            virtualNumber: String(item.virtualNumber),
          }))
        : [],
      serviceProviderName: integrateData.serviceProvider
        .replace(/ /g, '')
        .trim(),
      shouldSetPrimary: integrateData.setPrimary || false,
      payloadForPushEndPoint: payloadForPushEndPointObject,
      payloadContentType: integrateData.payloadContentType,
      payloadMethodType: integrateData.payloadMethodType,
    };

    if (integrateData.ivr === 'Outbound') {
      const queryParametersObject = this.mapQueryParameters(integrateData);
      const headerVariablesObject = this.mapHeaderVariables(integrateData);
      const bodyVariablesObject = this.mapBodyVariables(integrateData);

      payload = {
        ...payload,
        isVirtualNumberRequiredForOutbound:
          integrateData.isVirtualNumberRequiredForOutbound || false,
        ivrOutboundConfigurationDto: {
          methodType: integrateData.methodType,
          contentType: integrateData.contentType,
          baseURL: integrateData.baseURL,
          resources: integrateData.resources,
          queryParameters: queryParametersObject,
          headerVariables: headerVariablesObject,
          bodyVariables: bodyVariablesObject,
        },
      };
    }

    this.store.dispatch(new CommonIvrIntegrationSuccess(''));
    this.store.dispatch(new CommonIvrIntegration(payload));

    this.store
      .select(getCommonIvrExcelLink)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res) {
          window.open(res, '_self');
        }
      });

    this.closeAdd.emit();
    this.reset();
    this.store.dispatch(new FetchIVRList());
  }

  clearValidators(controls: AbstractControl[]) {
    controls.forEach((control) => {
      Object.keys(control.value).forEach((key) => {
        control.get(key)?.clearValidators();
        control.get(key)?.updateValueAndValidity();
      });
    });
  }

  mapPayloadForPushEndPoint(integrateData: any) {
    return integrateData.payloadForPushEndpoint.reduce(
      (
        acc: any,
        {
          payloadKey,
          payloadValue,
        }: { payloadKey: string; payloadValue: string }
      ) => {
        if (payloadKey !== null && payloadValue !== null) {
          acc[payloadKey] = payloadValue;
        }
        return acc;
      },
      {}
    );
  }

  mapQueryParameters(integrateData: any) {
    return integrateData.queryParameters.reduce(
      (
        acc: any,
        { queryKey, queryValue }: { queryKey: string; queryValue: any }
      ) => {
        if (queryKey !== null && queryValue !== null) {
          acc[queryKey] = queryValue?.value
            ? queryValue?.value
            : queryValue?.displayName;
        }
        return acc;
      },
      {}
    );
  }

  mapHeaderVariables(integrateData: any) {
    return integrateData.headerVariables.reduce(
      (
        acc: any,
        { headerKey, headerValue }: { headerKey: string; headerValue: any }
      ) => {
        if (headerKey !== null && headerValue !== null) {
          acc[headerKey] = headerValue?.value
            ? headerValue?.value
            : headerValue?.displayName;
        }
        return acc;
      },
      {}
    );
  }
  mapBodyVariables(integrateData: any) {
    return integrateData.bodyVariables.reduce(
      (
        acc: any,
        { bodyKey, bodyValue }: { bodyKey: string; bodyValue: any }
      ) => {
        if (bodyKey !== null && bodyValue !== null) {
          acc[bodyKey] = bodyValue?.value
            ? bodyValue?.value
            : bodyValue?.displayName;
        }
        return acc;
      },
      {}
    );
  }

  assignFormGroup() {
    this.integrateForm = this.fb.group({
      serviceProvider: ['', Validators.required],
      accountName: ['', Validators.required],
      isVirtualNumberRequiredForOutbound: [false],
      ivr: ['Inbound'],
      canIncludeApiKeyInHeader: [false],
      setPrimary: [false],
      payloadMethodType: [null, Validators.required],
      payloadContentType: [null, Validators.required],
      payloadForPushEndpoint: this.fb.array([
        this.fb.group({
          payloadKey: [null, Validators.required],
          payloadValue: [
            '',
            [Validators.required, this.duplicatePayloadValuesValidator()],
          ], // Include the validator in an array
        }),
      ]),

      ivrAssignmentDtos: this.fb.array([
        this.fb.group({
          virtualNumber: [''],
          agencyName: [null],
          projectId: [null],
          locationId: [null],
          userIds: [null],
          showProjectLocation: false,
          showUsers: false,
          showAgency: false,
        }),
      ]),
      methodType: [null, Validators.required],
      contentType: [null, Validators.required],
      baseURL: ['', Validators.required],
      resources: [''],
      queryParameters: this.fb.array([
        this.fb.group({
          queryKey: [''],
          queryValue: [null],
        }),
      ]),
      headerVariables: this.fb.array([
        this.fb.group({
          headerKey: [''],
          headerValue: [null],
        }),
      ]),
      bodyVariables: this.fb.array([
        this.fb.group({
          bodyKey: [''],
          bodyValue: [null],
        }),
      ]),
    });
  }

  get ivrAssignmentDtos() {
    return this.integrateForm.get('ivrAssignmentDtos') as FormArray;
  }

  get payloadForPushEndpoint() {
    return this.integrateForm.get('payloadForPushEndpoint') as FormArray;
  }

  get queryParameters() {
    return this.integrateForm.get('queryParameters') as FormArray;
  }

  get headerVariables() {
    return this.integrateForm.get('headerVariables') as FormArray;
  }

  get bodyVariables() {
    return this.integrateForm.get('bodyVariables') as FormArray;
  }

  queryValueChange(i: number) {
    this.queryParameters.controls[i]
      .get('queryKey')
      .setValidators([Validators.required]);
    this.queryParameters.controls[i]
      .get('queryValue')
      .setValidators([Validators.required]);
    this.queryParameters.controls[i].get('queryKey').updateValueAndValidity();
    this.queryParameters.controls[i].get('queryValue').updateValueAndValidity();
  }

  headerValueChange(i: number) {
    this.headerVariables.controls[i]
      .get('headerKey')
      .setValidators([Validators.required]);
    this.headerVariables.controls[i]
      .get('headerValue')
      .setValidators([Validators.required]);
    this.headerVariables.controls[i].get('headerKey').updateValueAndValidity();
    this.headerVariables.controls[i]
      .get('headerValue')
      .updateValueAndValidity();
  }

  bodyValueChange(i: number) {
    this.bodyVariables.controls[i]
      .get('bodyKey')
      .setValidators([Validators.required]);
    this.bodyVariables.controls[i]
      .get('bodyValue')
      .setValidators([Validators.required]);
    this.bodyVariables.controls[i].get('bodyKey').updateValueAndValidity();
    this.bodyVariables.controls[i].get('bodyValue').updateValueAndValidity();
  }

  onAddPayload() {
    if (
      this.payloadOptions?.length &&
      this.payloadForPushEndpoint?.length < this.tempVariables?.length
    ) {
      this.payloadForPushEndpoint?.push(
        this.fb.group({
          payloadKey: [null, Validators.required],
          payloadValue: [
            '',
            [Validators.required, this.duplicatePayloadValuesValidator()],
          ],
        })
      );
      this.onPayloadOptionChanged();
    }
  }

  uniqueValueCheck(index: number) {
    this.uniqueValueArray = this.payloadForPushEndpoint.controls.map(
      (element: any) => element.value.payloadValue
    );
  }

  duplicatePayloadValuesValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const value = control.value;
      if (this.uniqueValueArray.some((element) => element === value)) {
        return { duplicate: true };
      }
      return null;
    };
  }

  onPayloadOptionChanged(): void {
    let selectedValues: string[] = this.integrateForm
      .get('payloadForPushEndpoint')
      .value.map((control: { payloadKey: string }) => control.payloadKey);

    selectedValues = selectedValues.filter((value) => value !== null);

    this.payloadOptions = this.tempVariables.filter(
      (option: any) => !selectedValues.includes(option.value)
    );
  }

  reset() {
    this.integrateForm.reset();
    this.integrateForm.controls['ivr'].setValue('Inbound');
  }

  onAddVN() {
    this.ivrAssignmentDtos?.push(
      this.fb.group({
        virtualNumber: [''],
        agencyName: [null],
        projectId: [null],
        locationId: [null],
        userIds: [null],
        showProjectLocation: false,
        showUsers: false,
        showAgency: false,
      })
    );
  }

  onaddQueryParams() {
    this.queryParameters?.push(
      this.fb.group({
        queryKey: [''],
        queryValue: [null],
      })
    );
  }

  onaddHeaderVariables() {
    this.headerVariables?.push(
      this.fb.group({
        headerKey: [''],
        headerValue: [null],
      })
    );
  }

  onaddBodyVariables() {
    this.bodyVariables?.push(
      this.fb.group({
        bodyKey: [''],
        bodyValue: [null],
      })
    );
  }

  onRemoveVN(index: number) {
    this.ivrAssignmentDtos.removeAt(index);
  }

  onRemovePayload(index: number) {
    if (this.payloadForPushEndpoint.length > 1) {
      this.payloadForPushEndpoint.removeAt(index);
    }
    this.uniqueValueArray = [];
    this.payloadForPushEndpoint.controls.forEach((element: any) =>
      element.get('payloadValue').updateValueAndValidity()
    );
    this.onPayloadOptionChanged();
  }

  onRemoveQueryParams(index: number) {
    if (this.queryParameters.length > 1) {
      this.queryParameters.removeAt(index);
    }
  }

  onRemoveHeaderVariables(index: number) {
    if (this.headerVariables.length > 1) {
      this.headerVariables.removeAt(index);
    }
  }

  onRemoveBodyVariables(index: number) {
    if (this.bodyVariables.length > 1) {
      this.bodyVariables.removeAt(index);
    }
  }

  onToggleProjectLocation(index: number) {
    let control: any;
    control = this.ivrAssignmentDtos.controls[index] as FormGroup;
    const showSelectUserControl = control.get('showProjectLocation');
    showSelectUserControl.setValue(!showSelectUserControl.value);
  }

  onToggleAgency(index: number) {
    let control: any;
    control = this.ivrAssignmentDtos.controls[index] as FormGroup;
    const showSelectUserControl = control.get('showAgency');
    showSelectUserControl.setValue(!showSelectUserControl.value);
  }

  onToggleUsers(index: number) {
    let control: any;
    control = this.ivrAssignmentDtos.controls[index] as FormGroup;
    const showSelectUserControl = control.get('showUsers');
    showSelectUserControl.setValue(!showSelectUserControl.value);
  }
}
