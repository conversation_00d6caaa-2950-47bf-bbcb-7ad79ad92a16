<form class="h-100vh text-coal">
    <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
        <h3 class="fw-semi-bold">{{ 'SIDEBAR.integration' | translate }} </h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide();"></div>
    </div>
    <ng-container
        *ngIf="(!updatedInboundIntegrationList?.length && !updatedOutboundIntegrationList?.length) else addAndListingPage">
        <ng-container *ngIf="isShowAddAccountBtn else addAccount">
            <div class="w-100 max-w-350 min-w-350 px-20 pt-20 ph-modal-unset">
                <img [type]="'leadrat'" [appImage]="image" alt="img" />
                <div class="mt-30 justify-center-col">
                    <div class="align-center">
                        <div class="d-flex">
                            <div class="align-center-col">
                                <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                                    <span class="icon ic-download ic-lg ic-coal"></span>
                                </div>
                                <div class="border-left-dotted h-60"></div>
                            </div>
                            <p class="text-coal fw-600 ml-20 mt-20">
                                {{ 'INTEGRATION.download-excel' | translate }}
                            </p>
                        </div>
                    </div>
                    <div class="align-center">
                        <div class="d-flex">
                            <div class="align-center-col">
                                <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                                    <span class="icon ic-share ic-lg ic-coal"></span>
                                </div>
                                <div class="border-left-dotted h-60"></div>
                            </div>
                            <div class="text-gray ml-20 mt-10">
                                <p>
                                    <span class="fw-600 text-coal">{{ 'GLOBAL.ivr' | translate }}</span>
                                    {{ 'INTEGRATION.share-message2' | translate }}
                                    <span class="fw-600 text-coal">{{ 'GLOBAL.ivr' | translate }}</span>
                                    {{ 'INTEGRATION.share-message3' | translate }} {{getAppName()}}
                                    {{ 'INTEGRATION.share-message4' | translate }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="align-center">
                        <div class="d-flex">
                            <div class="align-center-col">
                                <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                                    <span class="icon ic-connection ic-lg ic-coal"></span>
                                </div>
                            </div>
                            <div class="text-gray ml-20">
                                <p>{{ 'INTEGRATION.connection-message' | translate }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="justify-center">
                    <button *ngIf="canAdd" class="btn-coal w-150 mt-40 flex-center"
                        (click)="isShowAddAccountBtn = false" id="btnAddAccount" data-automate-id="btnAddAccount">
                        {{ 'SIDEBAR.add' | translate }} {{'SIDEBAR.account' | translate}}</button>
                </div>
            </div>
        </ng-container>
        <ng-template #addAccount>
            <div class="w-100 max-w-500 min-w-500 ip-modal-unset px-20 pt-20 ph-modal-unset ip-width-unset"
                *ngIf="!isShowAddAccountBtn">
                <div class="mb-30">
                    <img [type]="'leadrat'" [appImage]="image" alt="img" />
                </div>
                <div class="px-12 my-20 scrollbar h-100-176">
                    <add-ivr (closeAdd)="isShowAddAccountBtn=!isShowAddAccountBtn"></add-ivr>
                </div>
            </div>
        </ng-template>
    </ng-container>
    <ng-template #addAndListingPage>
        <ng-container *ngIf="!isShowEditModal">
            <div class="pt-20 pb-30 flex-column max-w-650 min-w-650 ip-width-unset scrollbar h-100-44">
                <div class="flex-between mb-30 px-16" *ngIf="showingSection == 'Home'">
                    <img [type]="'leadrat'" [appImage]="image" alt="img" />
                    <div class="align-center">
                        <div *ngIf="canAdd" class="btn-coal" (click)="showingSection = 'AddIVR'">
                            {{ 'SIDEBAR.add' | translate }}</div>
                        <div class="btn-coal ml-10" (click)="showingSection = 'Config'">Config</div>
                    </div>
                </div>
                <div *ngIf="showingSection == 'AddIVR'">
                    <div class="mb-30 px-16">
                        <div class="flex-between">
                            <img [type]="'leadrat'" [appImage]="image" alt="img" />
                            <div class="btn-coal" (click)="showingSection = 'Config'">Config</div>
                        </div>
                        <add-ivr (closeAdd)="showingSection = 'Home'"></add-ivr>
                    </div>
                </div>
                <!-- settings -->
                <div *ngIf="showingSection == 'Config'">
                    <div class="px-16">
                        <div class="flex-between">
                            <img [type]="'leadrat'" [appImage]="image" alt="img" />
                            <div class="btn-coal" (click)="showingSection = 'AddIVR'">Add</div>
                        </div>
                        <div class="p-20">
                            <u class="field-label header-4">IVR Settings:</u>
                            <div class="align-center" *ngIf="canViewGS">
                                <h5 class="field-label mr-20">Direction Of Data Flow:<span type="button"
                                        data-bs-toggle="tooltip" data-bs-html="true" title="This setting refers to the Data flow of IVR call 
where CRM Should create new record of the data which is not in the CRM. 
Choose 'Lead' if data should be created in Leads Module and 'Data' for Data Management. 
Choose 'Both' if need a copy of the data in both Lead and Data.">
                                        <img src="../../../../../../assets/images/i-btn.svg" class="ml-4 mb-2" /></span>
                                </h5>
                                <ng-container *ngFor="let type of directionOfLeadCreation">
                                    <label
                                        class="form-check ph-flex-col form-check-inline bg-light-pearl br-20 p-10 mt-12"
                                        [ngClass]="{'pe-none' : !canUpdateGS}"
                                        (click)="canUpdateGS ? updateCreationDirection(type.value) : ''"
                                        for="inpDirection{{type.value}}">
                                        <input type="radio" id="inpDirection{{type.value}}"
                                            [formControl]="directionOfCreation" [value]="type.value"
                                            class="radio-check-input">
                                        <div class="text-dark-gray cursor-pointer text-large text-sm ml-6">
                                            {{type.displayName}}</div>
                                    </label>
                                </ng-container>
                            </div>
                            <form class="align-center" [formGroup]="ivrSettingForm">
                                <div class="w-50">
                                    <div class="field-label">IVR Only</div>
                                    <ng-select [virtualScroll]="true" [items]="agentListForIVR1" [multiple]="true"
                                        ResizableDropdown [closeOnSelect]="false" bindValue="id"
                                        formControlName="ivrList" bindLabel="fullName" placeholder="ex. Mounika Pampana"
                                        class="bg-white">
                                        <ng-template ng-label-tmp let-item="item">
                                            {{item.firstName}} {{item.lastName}}
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span>{{item.firstName}} {{item.lastName}}</div>
                                        </ng-template>
                                    </ng-select>
                                </div>
                                <div class="w-50 ml-20">
                                    <div class="field-label">Dialer Only</div>
                                    <ng-select [virtualScroll]="true" [items]="agentListForDialer1" [multiple]="true"
                                        ResizableDropdown [closeOnSelect]="false" bindValue="id"
                                        formControlName="dialerList" bindLabel="fullName"
                                        placeholder="ex. Mounika Pampana" class="bg-white">
                                        <ng-template ng-label-tmp let-item="item">
                                            {{item.firstName}} {{item.lastName}}
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span>{{item.firstName}} {{item.lastName}}</div>
                                        </ng-template>
                                    </ng-select>
                                </div>
                            </form>
                            <div class="flex-end mt-20">
                                <button class="btn-gray mr-20" (click)="showingSection = 'Home'; reset()">
                                    {{ 'BUTTONS.cancel' | translate }}</button>
                                <button class="btn-coal" (click)="updateIVRSettingsList()">Save</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- listing accounts -->
                <div class="px-16">
                    <div class="table-scrollbar scrollbar scroll-hide ip-w-100-40">
                        <!-- Inbound -->
                        <div *ngIf="updatedInboundIntegrationList?.length" class="mb-20">
                            <div class="justify-center">
                                <u class="text-xl text-black my-12 fw-semi-bold">Inbound
                                    Account(s):</u>
                            </div>
                            <table class="table standard-table no-vertical-border">
                                <thead>
                                    <tr class="w-100 text-nowrap">
                                        <th *ngIf="canUpdate" class="w-80">{{ 'INTEGRATION.set' | translate }} as {{
                                            'INTEGRATION.primary' | translate }}</th>
                                        <th class="w-100px">{{ 'SIDEBAR.account' | translate }}</th>
                                        <th class="w-100px">Service Provider</th>
                                        <th class="w-80">{{ 'INTEGRATION.leads-count' | translate }}</th>
                                        <th class="w-110">{{ 'GLOBAL.actions' | translate }}</th>
                                    </tr>
                                </thead>
                                <tbody class="text-secondary fw-semi-bold">
                                    <ng-container
                                        *ngFor="let integration of updatedInboundIntegrationList; let i = index">
                                        <tr>
                                            <td *ngIf="canUpdate" class="text-center w-80">
                                                <div class="radio-button" [class.checked]="integration.isPrimary"
                                                    (click)="setPrimary(integration.id)">
                                                </div>
                                            </td>
                                            <td class="w-100px" [title]="integration.accountName">
                                                <p class="text-truncate-1 break-all">{{ integration.accountName }}</p>
                                            </td>
                                            <td class="w-100px" [title]="integration.serviceProviderName">
                                                <p class="text-truncate-1 break-all">{{ integration.serviceProviderName
                                                    }}</p>
                                            </td>
                                            <td class="w-80">
                                                <p>{{ integration?.leadCount }}</p>
                                            </td>
                                            <td class="w-110">
                                                <div class="align-center">
                                                    <div title="Download" class="bg-accent-green icon-badge"
                                                        id="btnIvrDownload" data-automate-id="btnIvrDownload"
                                                        (click)="reDownloadExcel(integration.id)">
                                                        <span class="icon ic-download ic-xxxs"></span>
                                                    </div>
                                                    <div *ngIf="canDelete">
                                                        <div title="Delete" class="bg-light-red icon-badge"
                                                            (click)="initDeleteIntegration(integration.id, integration.accountName)">
                                                            <span class="icon ic-delete ic-xxxs"></span>
                                                        </div>
                                                    </div>
                                                    <div title="Assignments" class="bg-blue-250 icon-badge"
                                                        (click)="openEdit(integration?.id, integration.accountName);isSelectedIVRAccInbound = true">
                                                        <span class="icon ic-setting-solid ic-xxxs"></span>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </ng-container>
                                </tbody>
                            </table>
                        </div>
                        <!-- Outbound -->
                        <div *ngIf="updatedOutboundIntegrationList?.length">
                            <div class="justify-center">
                                <u class="text-xl text-black my-12 fw-semi-bold">Outbound
                                    Account(s):</u>
                            </div>
                            <table class="table standard-table no-vertical-border">
                                <thead>
                                    <tr class="w-100 text-nowrap">
                                        <th *ngIf="canUpdate" class="w-80">{{ 'INTEGRATION.set' | translate }} {{
                                            'INTEGRATION.primary' | translate }}</th>
                                        <th class="w-100px">{{ 'SIDEBAR.account' | translate }}</th>
                                        <th class="w-100px">Service Provider</th>
                                        <th class="w-110">{{ 'GLOBAL.actions' | translate }}</th>
                                    </tr>
                                </thead>
                                <tbody class="text-secondary fw-semi-bold">
                                    <ng-container
                                        *ngFor="let integration of updatedOutboundIntegrationList; let i = index">
                                        <tr>
                                            <td *ngIf="canUpdate" class="text-center w-80">
                                                <div class="radio-button" [class.checked]="integration.isPrimary"
                                                    (click)="setPrimary(integration.id)">
                                                </div>
                                            </td>
                                            <td class="w-100px">
                                                <p class="text-truncate-2">{{ integration.accountName }}</p>
                                            </td>
                                            <td class="w-100px">
                                                <p class="text-truncate-2">{{ integration.serviceProviderName }}</p>
                                            </td>
                                            <td class="w-110">
                                                <div class="align-center">
                                                    <div title="Download" class="bg-accent-green icon-badge"
                                                        id="btnIvrDownload" data-automate-id="btnIvrDownload"
                                                        (click)="reDownloadExcel(integration.id)">
                                                        <span class="icon ic-download ic-xxxs"></span>
                                                    </div>
                                                    <div *ngIf="canDelete">
                                                        <div title="Delete" class="bg-light-red icon-badge"
                                                            (click)="initDeleteIntegration(integration.id, integration.accountName)">
                                                            <span class="icon ic-delete ic-xxxs"></span>
                                                        </div>
                                                    </div>
                                                    <div title="Assignments" class="bg-blue-250 icon-badge"
                                                        (click)="openEdit(integration?.id, integration.accountName, integration.ivrCallType);isSelectedIVRAccInbound = false">
                                                        <span class="icon ic-setting-solid ic-xxxs"></span>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </ng-container>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </ng-container>
    </ng-template>
    <ng-container *ngIf="isShowEditModal">
        <div class="p-16 min-w-450 max-w-450 ip-width-unset h-100-44 scrollbar">
            <img [type]="'leadrat'" [appImage]="image" alt="img" />
            <div class="align-center mt-30">
                <span class="icon ic-chevron-left ic-xxs ic-black cursor-pointer mr-10"
                    (click)="isShowEditModal = false"></span>
                <h3 class="fw-700">Assignment</h3>
            </div>
            <div class="bg-light-pearl mt-20 br-6 flex-between break-all bg-profile">
                <div class="flex-column pt-20 pl-10 pb-20">
                    <div class="fw-semi-bold fv-sm-caps">
                        {{ 'INTEGRATION.account-name' | translate }}
                    </div>
                    <div class="fw-700 text-small">{{ selectedAccount?.name }}</div>
                </div>
            </div>
            <form [formGroup]="editForm" class="scrollbar h-100-333">
                <div for="inpVirtual" class="field-label-req">
                    {{ 'INTEGRATION.virtual-number' | translate }}</div>
                <div formArrayName="ivrAssignments">
                    <div class="mb-12" *ngFor="let control of ivrAssignmentsArray?.controls; let i = index">
                        <ng-container [formGroupName]="i">
                            <div class="align-center">
                                <form-errors-wrapper label="{{ 'INTEGRATION.virtual-number' | translate }}"
                                    [control]="editForm.controls['virtualNumber']" class="flex-grow-1">
                                    <input type="text" formControlName="virtualNumber" id="inpVirtual"
                                        data-automate-id="inpVirtual" autocomplete="off" placeholder="ex. 9133XXXXXX"
                                        (keydown)="onlyNumbers($event)" />
                                </form-errors-wrapper>
                                <div class="align-center border br-4 px-16 py-10 ml-12 w-110">
                                    <div (click)="onToggleUsers(i)"
                                        *ngIf="canAssign && selectedAccount?.IVRCallType == 1">
                                        <div title="Assign To" class="bg-blue-800 icon-badge">
                                            <span class="icon ic-assign-to ic-xxxs"></span>
                                        </div>
                                    </div>
                                    <div *ngIf="selectedAccount?.IVRCallType !== 1" (click)="onToggleAgency(i)"
                                        title="Agency Name" class="bg-blue-850 icon-badge">
                                        <span class="icon ic-suitcase ic-xxs"></span>
                                    </div>
                                    <div class="bg-violet icon-badge" (click)="onToggleProjectLocation(i)"
                                        [title]="selectedAccount?.IVRCallType == 1 ? 'Project' : 'Project & Location'">
                                        <span class="icon ic-building-secondary ic-xxxs"></span>
                                    </div>
                                    <div title="Delete" class="bg-light-red icon-badge" (click)="onRemoveVN(i)">
                                        <span class="icon ic-delete ic-xxxs"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="align-center flex-wrap">
                                <div class="w-50 px-12"
                                    *ngIf="!isSelectedIVRAccInbound && control.get('showUsers').value">
                                    <div class="field-label"> {{'SETTINGS.select-user' | translate}}
                                    </div>
                                    <ng-select [virtualScroll]="true" ResizableDropdown
                                        [items]="canAssignToAny ? allActiveUsers : activeUsers" [multiple]="true"
                                        [closeOnSelect]="false" bindLabel="fullName" bindValue="id" name="assignedUser"
                                        formControlName="userIds" placeholder="ex. Mounika Pampana" class="bg-white">
                                        <ng-template ng-label-tmp let-item="item">
                                            {{item.firstName}} {{item.lastName}}
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span>{{item.firstName}}
                                                {{item.lastName}}</div>
                                        </ng-template>
                                    </ng-select>
                                </div>
                                <div class="w-50 px-12"
                                    *ngIf="isSelectedIVRAccInbound && control.get('showAgency').value">
                                    <div class="field-label">Select Agency Name</div>
                                    <ng-select [virtualScroll]="true" [items]="agencyNameList" [addTag]="true"
                                        ResizableDropdown bindLabel="agencyName" bindValue="agencyName"
                                        formControlName="agencyName" class="bg-white"
                                        addTagText="Create New Agency Name"
                                        placeholder="ex. Mounika pampana"></ng-select>
                                </div>
                                <div class="w-50 px-12" *ngIf="control.get('showProjectLocation').value">
                                    <div class="field-label">Select Project</div>
                                    <ng-select [virtualScroll]="true" [items]="allProjectList" class="bg-white"
                                        ResizableDropdown bindLabel="name" bindValue="id" placeholder="ex. ABC project"
                                        formControlName="projectId"></ng-select>
                                </div>
                                <div class="w-50 px-12"
                                    *ngIf="isSelectedIVRAccInbound && control.get('showProjectLocation').value">
                                    <div class="field-label">Select {{'LOCATION.location' |
                                        translate}}
                                    </div>
                                    <ng-select [virtualScroll]="true" [items]="placesList" class="bg-white"
                                        ResizableDropdown bindLabel="location" bindValue="id"
                                        placeholder="ex. ABC location" formControlName="locationId"></ng-select>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </div>
                <div class="cursor-pointer align-center fw-700" (click)="onAddVN()">
                    <span class="icon ic-xs ic-add ic-accent-green"></span>
                    <span class="text-accent-green">add additional number</span>
                </div>
            </form>
            <div class="flex-end mt-20">
                <button class="btn-gray mr-20" (click)="isShowEditModal = false">
                    {{ 'BUTTONS.cancel' | translate }}</button>
                <button class="btn-coal" (click)="updateIVRAccount()">
                    {{ 'BUTTONS.save' | translate }}</button>
            </div>
        </div>
    </ng-container>
</form>