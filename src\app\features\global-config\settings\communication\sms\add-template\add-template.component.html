<div class="bg-coal py-16 px-20 text-white">Add Template</div>
<div class="bg-white p-20 brbl-12 brbr-12">
    <ng-container *ngIf="currentStep == 1">
        <div class="d-flex flex-wrap w-100">
            <div class="w-33 ip-w-50 ph-w-100">
                <div class="field-label-req clear-margin-t">Template Name</div>
                <div class="bg-white border-gray br-4 mr-30 ip-mr-10">
                    <div class="align-center py-8 px-10">
                        <input type="text" placeholder="type here.." name="search" class="border-0 outline-0 w-100">
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-50 ph-w-100 ph-mt-10">
                <div class="field-label-req clear-margin-t">DLT Template ID</div>
                <div class="bg-white border-gray br-4 mr-30 ip-mr-10">
                    <div class="align-center py-8 px-10">
                        <input type="text" placeholder="type here.." name="search" class="border-0 outline-0 w-100">
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-50 ph-w-100 ng-select-sm ph-mt-10">
                <div class="d-flex">
                    <div class="field-label-req mr-16">Select Sender ID</div>
                    <span class="dot dot-sm bg-black-200 cursor-pointer">
                        <span class="m-auto text-white">?</span>
                    </span>
                </div>
                <div class="ip-mr-10">
                    <ng-select [virtualScroll]="true" placeholder="{{'GLOBAL.select' | translate}}">
                        <ng-template>
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
        </div>
        <div class="d-flex w-100 ip-flex-col">
            <div class="w-62pr ip-w-100">
                <div class="position-relative">
                    <div class="flex-between w-100 mt-20 mb-4">
                        <div class="field-label-req clear-margin-t">Message Body </div>
                        <a class="align-center p-4 bg-black-200 br-10">
                            <span class="ic-add icon ic-sm mr-8"></span>
                            <span class="text-white fw-semi-bold">{{'SIDEBAR.add' | translate }}
                                {{ 'GLOBAL.variable' | translate }}</span>
                        </a>
                    </div>
                    <div class="position-absolute bg-light-pearl w-360 ph-w-300 right-0 br-4 z-index-2">
                        <div class="p-12">
                            <h5 class="fw-semi-bold text-coal"> {{ 'GLOBAL.lead' | translate }}</h5>
                            <div class="pt-10 d-flex flex-wrap w-100">
                                <div class="py-4 px-12 border-accent-green br-10 mr-6 mb-6 cursor-pointer">username
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <textarea formControlName="message" rows="8" class="scrollbar" placeholder="type here.."></textarea>
                </div>
            </div>
            <div class="ml-30 mt-20 w-30 ip-w-100 ip-ml-0">
                <h6 class="text-accent-green fw-semi-bold">Tips:</h6>
                <div class="mt-6 text-xs text-black-200 d-flex"><span
                        class="dot dot-xxs mr-6 bg-black-200 mt-4"></span><span>Ensure the template you are adding is
                        approved in DLT. SMS will not
                        be
                        sent if using
                        unapproved DLT templates.</span></div>
                <div class="mt-6 text-xs text-black-200 d-flex"><span
                        class="dot dot-xxs mr-6 bg-black-200 mt-4"></span><span>Kindly ensure to add correct Template Id
                        that is approved on DLT Platform.
                    </span></div>
            </div>
        </div>
        <div class="mt-20 flex-center">
            <button class="btn-gray mr-10" (click)="modalRef.hide()">
                {{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal">Add template</button>
        </div>
    </ng-container>
    <ng-container *ngIf="currentStep == 2">
        <div class="w-100 d-flex ip-flex-col">
            <div class="w-60pr ip-w-100 mr-60 ip-mr-0">
                <h4 class="fw-semi-bold text-black-100">Verify the SMS template to add into the system</h4>
                <div class="mt-10 br-6 border bg-light-pearl p-10 text-xs">
                    <div class="fw-700 text-dark-gray">template</div>
                    <div class="mt-4">
                        <span>Dear</span>
                        <span class="fw-700"> manasa, </span>
                        <span>we have scheduled a callback
                            on</span> <span class="fw-700"> 18-07-2025 </span> at <span class="fw-700"> 11:00AM
                        </span>
                        as
                        per
                        the
                        last discussion.
                        For
                        any
                        changes in plan or
                        further queries, please connect with your agent <span> Ravi J</span> on <span
                            class="fw-700">+918860000123.</span>
                        <div> Thanks</div>
                    </div>
                </div>
            </div>
            <div class="w-40pr ip-w-100 ip-mt-10">
                <h4 class="text-black-100 fw-semi-bold">To number</h4>
                <div class="mt-4 text-sm text-dark-gray">Send this sample test message to</div>
                <div class="bg-white border-gray br-4 mt-10 w-200">
                    <div class="align-center py-8 px-10">
                        <input type="text" class="border-0 outline-0 w-100">
                    </div>
                </div>
            </div>
        </div>
        <ng-container *ngIf="currentStep == 2">
            <div class="mt-30 flex-center"><button class="btn btn-linear-green w-150">Send test SMS </button></div>
        </ng-container>
        <ng-container *ngIf="currentStep == 3"></ng-container>
    </ng-container>
    <ng-container *ngIf="currentStep == 4">
        <div class="flex-center-col">
            <ng-lottie [options]='message' class="h-60"></ng-lottie>
            <div class="header-4 text-accent-green"> <span>SMS has been initiated & sent to</span> <span
                    class="fw-600 text-nowrap">
                    +91
                    8860073924 </span></div>
            <h4 class="text-black-200">Please check & confirm if you have received the SMS or not.</h4>
            <div class="d-flex mt-16">
                <div class="border py-8 px-16 cursor-pointer mr-10 br-4">Yes, received the SMS</div>
                <div class="border py-8 px-16 cursor-pointer mr-10 br-4">No, resend the SMS</div>
            </div>
        </div>
    </ng-container>
    <ng-container *ngIf="currentStep == 5">
        <div class="flex-center-col">
            <ng-lottie [options]='sucesss' class="h-80"></ng-lottie>
            <h4 class="text-accent-green">Yohooo! Your template has been verified and added to the system.</h4>
        </div>

    </ng-container>
</div>