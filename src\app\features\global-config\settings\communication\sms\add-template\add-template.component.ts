import { Component } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';

@Component({
  selector: 'add-template',
  templateUrl: './add-template.component.html',
})
export class AddTemplateComponent {
  modalRef: BsModalRef;
  currentStep: number = 4;
  sucesss: AnimationOptions = {
    path: 'assets/animations/circle-green-tick.json',
  };

  message: AnimationOptions = {
    path: 'assets/animations/message.json',
  };

  constructor() { }



  closeModal() {
    this.modalRef.hide();
  }

}
