
<div class="px-20 py-30">
    <div class="flex-between">
        <div class="align-center">
            <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/integration'></div>
            <div class="text-black-100 fw-semi-bold header-4"> SMS GateWays</div>
        </div>
        <button class="btn  btn-linear-green align-center w-170" (click)="navigateToSmsSettings()">
            <span class="ic-add icon ic-sm mr-10"></span>
            <span class="text-white">{{'SIDEBAR.add' | translate }} {{'SHARE.sms' | translate }}
                {{'BULK_LEAD.template' | translate}}</span>
        </button>
    </div>
    <div class="pt-30">
        <div class="bg-white w-100 border-gray">
            <div class="align-center py-10 px-12">
                <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
                <input type="text" placeholder="type to search" name="search" class="border-0 outline-0 w-100">
            </div>
        </div>
    </div>
    <div class="bg-white pt-10"></div>
    <ng-template #smsData>
        <div class="scrollbar scroll-hide tb-w-100-60 table-scrollbar">
            <table class="table standard-table no-vertical-border">
                <thead>
                    <tr class="w-100 text-nowrap">
                        <th class="w-150">GateWay Name</th>
                        <th class="w-150">API Key</th>
                        <th class="w-150">Added Date</th>
                        <th class="w-110">Modified Date</th>
                        <th class="w-110">Actions</th>
                    </tr>
                </thead>
                <tbody class="text-secondary fw-semi-bold h-100-233">
                    <tr>
                        <td class="w-150"></td>
                        <td class="w-150"></td>
                        <td class="w-150"></td>
                        <td class="w-110"></td>
                        <td class="w-110">
                            <div class="align-center p-10">
                                <div title="Edit" class="bg-accent-green icon-badge"><span
                                        class="icon m-auto ic-xxs ic-pen"></span></div>
                                <div title="Delete" class="bg-light-red icon-badge">
                                    <span class="icon ic-delete m-auto ic-xxs"></span></div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </ng-template>
    <ng-container *ngIf="!data.length; else smsData">
        <div class="scrollbar scroll-hide tb-w-100-40 table-scrollbar">
            <table class="table standard-table no-vertical-border">
                <thead class="ip-d-none">
                    <tr class="w-100 text-nowrap">
                        <th class="w-150">GateWay Name</th>
                        <th class="w-150">API Key</th>
                        <th class="w-150">Added Date</th>
                        <th class="w-110">Modified Date</th>
                        <th class="w-110">Actions</th>
                    </tr>
                </thead>
                <div class="flex-center-col bg-white br-4 ip-bg-unset">
                    <img src="assets/images/layered-cards.svg" alt="No lead found" class="mt-20">
                    <div class="header-4 text-dark-gray">no templates added yet...</div>
                    <button class="btn btn-linear-green my-10 align-center w-180 text-nowrap" (click)="navigateToSmsSettings()">
                        <span class="ic-add icon ic-sm mr-10"></span>
                        <span class="text-white">{{'SIDEBAR.add' | translate }} {{'SHARE.sms' | translate }}
                            {{'BULK_LEAD.template' | translate}}</span>
                    </button>
                </div>
            </table>
        </div>
    </ng-container>
</div>