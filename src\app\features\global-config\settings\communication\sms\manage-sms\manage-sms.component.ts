import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'manage-sms',
  templateUrl: './manage-sms.component.html',
})
export class ManageSmsComponent {
  data: any[] = [];


  constructor(
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private router: Router,
  ) {
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
  }



  navigateToSmsSettings() {
    this.router.navigate(['integration/sms-settings']);
  }

}
