<!-- <div routerLink='/integration/manage-sms' [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
    class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer"> -->
<div class="p-30 position-relative">
    <h5 class="fw-600 text-accent-green position-absolute right-30 ip-top-10 ip-right-5 cursor-pointer">How to import?
    </h5>
    <div class="flex-center-col">
        <div class="header-3 text-coal">integrate your SMS service provider</div>
        <div class="flex-center mt-20">
            <img src="../../../../assets/images/msg-code.svg">
            <span class="w-170 ip-w-100px ph-w-70"
                [ngClass]="currentStep == 1 ? 'border-dashed-bottom-2' : 'border-accent-green'"></span>
            <img src="../../../../assets/images/cloud.svg"
                [ngClass]="{'gray-scale' : currentStep == 1 || currentStep == 2}">
            <span class="w-170 ip-w-100px ph-w-70"
                [ngClass]="currentStep !== 3 ? 'border-dashed-bottom-2' : 'border-accent-green'"></span>
            <img src="../../../../assets/images/note.svg" [ngClass]="{'gray-scale' : currentStep !== 3}">
        </div>
        <div class="flex-center">
            <div class="flex-center-col pl-16">
                <div class="fw-600 text-sm text-gray-90 mt-6">Step 1</div>
                <div class="fw-semi-bold text-large text-black-100 mt-2 text-center">
                    <div> Select</div>
                    <div>
                        service provider </div>
                </div>
            </div>
            <span class="w-115 ip-w-45 ph-w-15"></span>
            <div class="flex-center-col pl-16">
                <div class="fw-600 text-sm text-gray-90 mt-6">Step 2</div>
                <div class="fw-semi-bold text-large text-black-100 mt-2 text-center">
                    <div>enter API key &</div>
                    <div>other details</div>
                </div>
            </div>
            <span class="w-115 ip-w-45 ph-w-15"></span>
            <div class="flex-center-col">
                <div class="fw-600 text-sm text-gray-90 mt-6">Step 3</div>
                <div class="fw-semi-bold text-large text-black-100 mt-2 text-center">
                    add SMS templates
                </div>
            </div>
        </div>
    </div>
</div>
<ng-container *ngIf="currentStep == 1">
    <div class="justify-center h-100-294 scrollbar">
        <div class="tb-d-none"></div>
        <div>
            <div class="mt-60 fw-semi-bold text-large ip-mt-10 ph-ml-10">
                Select SMS Provider
            </div>
            <div class="mt-2 text-sm text-dark-gray ph-ml-10">Who is your SMS Gateway provider?</div>
            <div class="d-flex ip-flex-col">
                <div
                    class="mt-20 pl-12 pt-12 border-gray h-100px w-330 mr-20 ip-mr-0 br-4 fw-semi-bold position-relative">
                    <div class="align-center">
                        <div class="mr-0 form-check form-check-inline p-0">
                            <input type="radio" class="radio-check-input">
                        </div>
                        <div class="ml-10">
                            <h4 class="text-black-100">Textlocal</h4>
                            <div class="align-center mt-4 cursor-pointer" (click)="navigateToTextlocal()">
                                <span class="mr-4 text-sm text-dark-gray">textlocal.in</span>
                                <span class="icon ic-alarm ic-light-gray ic-xxs"></span>
                            </div>
                        </div>
                    </div>
                    <img src="../../../../assets/images/text-local.svg" alt="img"
                        class="position-absolute bottom-0 right-0" />
                </div>
                <div class="mt-20 pl-12 pt-12 border-gray h-100px w-330 br-4 fw-semi-bold position-relative">
                    <div class="align-center">
                        <div class="mr-0 form-check form-check-inline p-0">
                            <input type="radio" class="radio-check-input">
                        </div>
                        <div class="ml-10">
                            <h4 class="text-black-100">Daksh Infosoft</h4>
                            <div class="align-center mt-4 cursor-pointer" (click)="navigateToDakshInfosoft()">
                                <span class="mr-4 text-sm text-dark-gray">dakshinfosoft.com</span>
                                <span class="icon ic-alarm ic-light-gray ic-xxs "></span>
                            </div>
                        </div>
                    </div>
                    <img src="../../../../assets/images/daksh-infosoft.svg" alt="img"
                        class="position-absolute bottom-0 right-0" />
                </div>
            </div>
        </div>
        <div class="tb-d-none"></div>
    </div>
</ng-container>
<ng-container *ngIf="currentStep == 1">
    <div class="flex-end p-10 bg-white">
        <span class="fw-600 text-large text-black-200 text-decoration-underline mr-20">{{'BUTTONS.cancel' |
            translate
            }} {{'SIDEBAR.integration' | translate}}</span>
        <span class="btn-gray text-white">{{'BUTTONS.proceed' | translate }}</span>
    </div>
</ng-container>
<ng-container *ngIf="currentStep == 2">
    <div class="h-100-307 scrollbar">
        <div class="mt-40 mx-20 p-20 bg-white br-4 position-relative ip-mt-10">
            <h3 class="fw-600 text-black-100 ip-mt-10">SMS service configuration</h3>
            <div class="mt-20 align-center text-sm ip-flex-col ip-flex-start">
                <div class="mr-20">
                    <div class="fw-semi-bold text-black-200 field-label-clear-m-req">Textlocal API Key</div>
                    <div class="text-dark-gray">
                        <div>You can get this API key from</div>
                        <div> your SMS Gateway provider</div>
                    </div>
                </div>
                <div class="bg-white w-370 ip-mt-10 ph-w-220px border-gray br-2">
                    <div class="align-center p-12">
                        <input type="text" placeholder="enter the API key here..." name="search"
                            class="border-0 outline-0 w-100">
                    </div>
                </div>
                <h5 class="fw-600 text-accent-green position-absolute right-30 ip-top-10 cursor-pointer">How to find API
                    Key?</h5>
            </div>
            <div class="border-bottom my-20"></div>
            <div>
                <h5 class="fw-600 text-accent-green position-absolute right-30 cursor-pointer">How to find verified
                    SenderID?</h5>
                <div class="align-center"><span class="fw-semi-bold mr-6 field-label-clear-m">Sender ID </span>
                    <div class="dot dot-sm bg-black-200 cursor-pointer">
                        <span class="m-auto text-white">?</span>
                    </div>
                </div>
                <div class="text-dark-gray text-sm">Add your verified Sender ID(s)</div>
                <div class="mt-20 align-center ph-flex-col ph-flex-start ml-20">
                    <div class="align-center">
                        <h5 class="fw-semi-bold text-black-200 mr-10">1. Sender Name</h5>
                        <div class="bg-white w-160 border-gray br-2 mr-10">
                            <div class="align-center py-8 px-10">
                                <input type="text" placeholder="type here.." name="search"
                                    class="border-0 outline-0 w-100">
                            </div>
                        </div>
                    </div>
                    <div class="btn-gray"><span class="icon ic-plus ic-xxxs mr-8"></span>
                        <span class="text-sm text-white fw-400">Add</span>
                    </div>
                </div>
                <div class="d-flex flex-wrap mt-12 ml-30">
                    <div class="align-center border br-12 p-4 mr-8"><span
                            class="fw-600 text-sm mr-4 text-black-100">self</span><span
                            class="icon ic-xxs ic-close-secondary ic-gray cursor-pointer"></span></div>
                </div>
            </div>
        </div>
    </div>
</ng-container>
<ng-container *ngIf="currentStep == 2">
    <div class="d-flex flex-end p-10 bg-white mt-10">
        <span class="fw-600 text-large text-black-200 text-decoration-underline mr-20 cursor-pointer">{{'BUTTONS.cancel'
            |
            translate
            }} {{'SIDEBAR.integration' | translate}}</span>
        <span class="br-4 p-8 border mr-10 cursor-pointer"><span
                class="ic-chevron-left ic-light-gray ic-x-xs mr-8"></span><span
                class="fw-600 text-large text-dark-gray">{{'BUTTONS.back' | translate }}</span></span>
        <span class="btn-gray text-white">{{'BUTTONS.proceed' | translate }}</span>
    </div>
</ng-container>
<ng-container *ngIf="currentStep == 3">
    <div class="h-100-307 scrollbar">
        <div class="p-20 mt-40 mx-20 bg-white br-4">
            <div class="flex-between">
                <h3 class="fw-600 text-black-100">SMS templates</h3>
                <button class="btn btn-linear-green align-center w-170" (click)="openAddTemplate()">
                    <span class="ic-add icon ic-sm mr-10"></span>
                    <span class="text-white">{{'SIDEBAR.add' | translate }} {{'SHARE.sms' | translate }}
                        {{'BULK_LEAD.template' | translate}}</span>
                </button>
            </div>
            <ng-template #smsTemplate>
                <div class="scrollbar scroll-hide mt-20 tb-w-100-80 table-scrollbar">
                    <table class="table standard-table no-vertical-border">
                        <thead>
                            <tr class="w-100 text-nowrap">
                                <th class="w-50px">S.No</th>
                                <th class="w-150">Name</th>
                                <th class="w-150">Template Message</th>
                                <th class="w-110">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="text-secondary fw-semi-bold">
                            <tr>
                                <td class="w-50px"></td>
                                <td class="w-150"></td>
                                <td class="w-150"></td>
                                <td class="w-110">
                                    <div class="align-center p-10">
                                        <div title="Edit" class="bg-accent-green icon-badge"><span
                                                class="icon m-auto ic-xxs ic-pen"></span></div>
                                        <div title="Delete" class="bg-light-red icon-badge">
                                            <span class="icon ic-delete m-auto ic-xxs"></span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </ng-template>
            <ng-container *ngIf="!data.length; else smsTemplate">
                <div class="scrollbar scroll-hide mt-20 tb-w-100-80 table-scrollbar">
                    <table class="table standard-table no-vertical-border">
                        <thead class="ip-d-none">
                            <tr class="w-100 text-nowrap">
                                <th class="w-50px">S.No</th>
                                <th class="w-150">Name</th>
                                <th class="w-150">Template Message</th>
                                <th class="w-110">Actions</th>
                            </tr>
                        </thead>
                        <div class="flex-center flex-column bg-white br-4 ip-bg-unset">
                            <img src="assets/images/layered-cards.svg" alt="No lead found" class="mt-20">
                            <div class="header-4 text-dark-gray">no templates added yet...</div>
                            <button class="btn btn-linear-green mt-10 align-center w-180 text-nowrap"
                                (click)="openAddTemplate()">
                                <span class="ic-add icon ic-sm mr-10"></span>
                                <span class="text-white">{{'SIDEBAR.add' | translate }} {{'SHARE.sms' | translate }}
                                    {{'BULK_LEAD.template' | translate}}</span>
                            </button>
                        </div>
                    </table>
                </div>
            </ng-container>
        </div>
    </div>

</ng-container>
<ng-container *ngIf="currentStep == 3">
    <div class="d-flex flex-end p-10 bg-white mt-10">
        <span class="fw-600 text-large text-black-200 text-decoration-underline mr-20">{{'BUTTONS.cancel' |
            translate
            }} {{'SIDEBAR.integration' | translate}}</span>
        <span class="br-4 p-8 border mr-10 cursor-pointer"><span
                class="ic-chevron-left ic-light-gray ic-x-xs mr-8"></span><span
                class="fw-600 text-large text-dark-gray cursor-pointer">{{'BUTTONS.back' | translate }}</span></span>
        <span class="btn-green text-white">Finish</span>
    </div>
</ng-container>