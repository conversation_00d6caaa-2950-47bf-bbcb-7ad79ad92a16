import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { AddTemplateComponent } from '../add-template/add-template.component';
import { BsModalService } from 'ngx-bootstrap/modal';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';


@Component({
  selector: 'sms-settings',
  templateUrl: './sms-settings.component.html',
})
export class SmsSettingsComponent {
  currentStep: number = 3;
  data: any[] = [];

  constructor(
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private modalService: BsModalService,

  ) {
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
  }


  openAddTemplate() {
    this.modalService.show(
      AddTemplateComponent,
      Object.assign({}, { class: 'top-modal modal-800 tb-modal-unset' })
    );
  }

  navigateToTextlocal() {
    window.location.href = 'https://www.textlocal.in';
  }

  navigateToDakshInfosoft() {
    window.location.href = 'https://www.dakshinfosoft.com/';
  }


}
