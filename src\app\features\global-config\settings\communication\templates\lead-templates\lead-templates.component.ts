import { Component, EventEmitter, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { Subject, takeUntil } from 'rxjs';
import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getPages } from 'src/app/core/utils/common.util';
import { AddLeadTemplateComponent } from 'src/app/features/global-config/settings/communication/templates/lead-templates/add-lead-template/add-lead-template.component';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  AssignPageSize,
  DeleteTemplate,
  FetchTemplateModule,
} from 'src/app/reducers/template/template.actions';
import {
  getTemplatesModule,
  getTemplatesModuleIsLoading,
} from 'src/app/reducers/template/template.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'lead-templates',
  templateUrl: './lead-templates.component.html',
})
export class LeadTemplatesComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  currOffset: number = 0;
  currPageNumber: number = 1;
  pageSize: number = PAGE_SIZE;
  totalCount: number;
  getPages = getPages;
  showEntriesSize: number[] = SHOW_ENTRIES;
  searchTermSubject = new Subject<string>();
  modalRef: any;
  templates: [];
  canTemplatesDelete: boolean = false;
  canTemplatesCreate: boolean = false;
  canTemplatesUpdate: boolean = false;
  isTemplateLoading: boolean = true;
  searchTerm: string;
  pageEntry: FormControl = new FormControl(this.pageSize);

  constructor(
    private sanitizer: DomSanitizer,
    private modalService: BsModalService,
    private _store: Store<AppState>
  ) { }

  ngOnInit() {
    this._store.dispatch(
      new AssignPageSize(this.pageSize, this.currPageNumber)
    );
    this.searchTermSubject.subscribe(() => {
      this.currOffset = 0;
      this.currPageNumber = 1;
      this._store.dispatch(
        new FetchTemplateModule(
          0,
          this.currPageNumber,
          this.pageSize,
          this.searchTerm
        )
      );
    });

    this._store.dispatch(
      new FetchTemplateModule(0, this.currPageNumber, this.pageSize)
    );
    this._store
      .select(getTemplatesModule)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          this.templates = res.templates;
          this.totalCount = res.totalCount;
        }
      });

    this._store
      .select(getTemplatesModuleIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isTemplateLoading = loading;
      });
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Templates.Update')) {
          this.canTemplatesUpdate = true;
        }
        if (permissions?.includes('Permissions.Templates.Create')) {
          this.canTemplatesCreate = true;
        }
        if (permissions?.includes('Permissions.Templates.Delete')) {
          this.canTemplatesDelete = true;
        }
      });
  }

  openLeadTemplate() {
    this.modalService.show(
      AddLeadTemplateComponent,
      Object.assign({}, { class: 'right-modal modal-400 ph-modal-unset' })
    );
  }

  editTemplate(template: any) {
    let initialState = {
      selectedTemplate: template,
    };
    this.modalService.show(AddLeadTemplateComponent, {
      class: 'right-modal modal-350 ph-modal-unset',
      initialState,
    });
  }

  initDeleteTemplate(id: string, template: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: template,
      fieldType: 'template',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    let payload: any = {
      id: id,
      moduleName: 0,
    };
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.deleteTemplate(payload);
        }
      });
    }
  }
  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }
  assignPageSize() {
    this.pageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1;
    this._store.dispatch(
      new AssignPageSize(this.pageSize, this.currPageNumber)
    );
    this.filterTemplates();
  }
  filterTemplates() {
    this._store.dispatch(
      new FetchTemplateModule(0, this.currPageNumber, this.pageSize)
    );
  }
  onPageChange(offset: number) {
    this.currOffset = offset;
    this.currPageNumber = offset + 1;
    this._store.dispatch(
      new AssignPageSize(this.pageSize, this.currPageNumber)
    );
    this.filterTemplates();
  }

  deleteTemplate(payload: string) {
    this._store.dispatch(new DeleteTemplate(payload));
    this.modalRef.hide();
  }

  getSanitizedHtml(html: string) {
    return this.sanitizer.bypassSecurityTrustHtml(html.replace(/\n/g, '<br>'));
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
    this._store.dispatch(new AssignPageSize(null, null));
  }
}
