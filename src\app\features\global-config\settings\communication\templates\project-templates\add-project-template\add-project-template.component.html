<form [formGroup]="templateForm" class="h-100vh text-coal min-w-355">
    <div class="bg-coal w-100 px-20 py-12 text-white flex-between">
        <h3 class="fw-semi-bold">{{ (selectedTemplate ? selectedTemplate.isEditable ? 'GLOBAL.edit' : '' :
            'PROPERTY.GALLERY.add-new') | translate }} Project {{ 'BULK_LEAD.template' | translate }}</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="bg-white">
        <div class="px-16 h-100-108 scrollbar">
            <div class="field-label-req">{{ 'BULK_LEAD.template' | translate }} {{ 'GLOBAL.name' | translate}}</div>
            <form-errors-wrapper [control]="templateForm.controls['name']" label="{{'GLOBAL.name' | translate }}">
                <input type="text" required placeholder="ex. Project Details Template" formControlName="name">
            </form-errors-wrapper>
            <div class="d-flex">
                <div class="field-label-underline">{{ 'LEADS.message'| translate }}:</div>
            </div>
            <div class="position-relative">
                <div class="flex-between w-100 mt-10 mb-4">
                    <div class="field-label clear-margin-t">Header</div>
                    <a class="align-center"
                        (click)="isShowHeaderVariable = !isShowHeaderVariable; isShowBodyVariable = false; isShowFooterVariable = false">
                        <span class="ic-add icon ic-sm mr-8"
                            [ngClass]="isShowHeaderVariable ? 'ic-red rotate-45' : 'ic-accent-green'"></span>
                        <span class="fw-600"
                            [ngClass]="isShowHeaderVariable ? 'text-red' : 'text-accent-green'">{{'SIDEBAR.add' |
                            translate }} {{ 'GLOBAL.variable' | translate }} in Header</span>
                    </a>
                </div>
                <div class="position-absolute bg-light-pearl w-370 br-10 z-index-2 p-20 ph-w-330"
                    *ngIf="isShowHeaderVariable">
                    <div class="d-flex flex-wrap w-100">
                        <div class="py-4 px-8 border-accent-green br-10 mr-8 mb-8 cursor-pointer"
                            *ngFor="let variable of globalVariables;let i=index"
                            (click)="addVariable(variable, 'header', $event)">
                            {{(i + 1) + '. ' + variable}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <textarea #headerTextarea formControlName="header" rows="2" class="scrollbar"
                    placeholder="ex. Hey,"></textarea>
            </div>
            <div class="position-relative">
                <div class="flex-between w-100 mt-12 mb-4">
                    <div class="field-label-req clear-margin-t">Body</div>
                    <a class="align-center"
                        (click)="isShowBodyVariable = !isShowBodyVariable; isShowHeaderVariable = false; isShowFooterVariable = false">
                        <span class="ic-add icon ic-sm mr-8"
                            [ngClass]="isShowBodyVariable ? 'ic-red rotate-45' : 'ic-accent-green'"></span>
                        <span class="fw-600"
                            [ngClass]="isShowBodyVariable ? 'text-red' : 'text-accent-green'">{{'SIDEBAR.add' |
                            translate }} {{ 'GLOBAL.variable' | translate }} in Body</span>
                    </a>
                </div>
                <div class="position-absolute bg-light-pearl w-370 br-10 z-index-2 p-20 ph-w-330"
                    *ngIf="isShowBodyVariable">
                    <div class="flex-between">
                        <h5 class="fw-600 text-coal">Project</h5>
                        <div class="align-center h-35px px-10 py-12 border br-20 no-validation bg-white">
                            <span class="icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
                            <input name="search" placeholder="search for variables" class="border-0 outline-0 w-100"
                                autocomplete="off" formControlName="searchControl">
                        </div>
                    </div>
                    <div class="pt-20 d-flex flex-wrap w-100">
                        <div class="py-4 px-8 border-accent-green br-10 mr-8 mb-8 cursor-pointer"
                            *ngFor="let variable of filteredVariables;let i=index"
                            (click)="addVariable(variable, 'message', $event)">
                            {{(i + 1) + '. ' + variable}}
                        </div>
                    </div>
                </div>
            </div>
            <form-errors-wrapper [control]="templateForm.controls['message']" label="{{'LEADS.message' | translate }}">
                <textarea #messageTextarea formControlName="message" rows="8" class="scrollbar" required
                    placeholder="ex. We selected property that might be matching to your interest. Check it out now."></textarea>
            </form-errors-wrapper>
            <div class="position-relative">
                <div class="flex-between w-100 mt-10 mb-4">
                    <div class="field-label clear-margin-t">Footer</div>
                    <a class="align-center"
                        (click)="isShowFooterVariable = !isShowFooterVariable; isShowHeaderVariable = false; isShowBodyVariable = false">
                        <span class="ic-add icon ic-sm mr-8"
                            [ngClass]="isShowFooterVariable ? 'ic-red rotate-45' : 'ic-accent-green'"></span>
                        <span class="fw-600"
                            [ngClass]="isShowFooterVariable ? 'text-red' : 'text-accent-green'">{{'SIDEBAR.add' |
                            translate }} {{ 'GLOBAL.variable' | translate }} in Footer</span>
                    </a>
                </div>
                <div class="position-absolute bg-light-pearl w-370 br-10 z-index-2 p-20 ph-w-330"
                    *ngIf="isShowFooterVariable">
                    <div class="d-flex flex-wrap w-100">
                        <div class="py-4 px-8 border-accent-green br-10 mr-8 mb-8 cursor-pointer"
                            *ngFor="let variable of globalVariables;let i=index"
                            (click)="addVariable(variable, 'footer', $event)">
                            {{(i + 1) + '. ' + variable}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <textarea #footerTextarea formControlName="footer" rows="2" class="scrollbar" placeholder="ex. Thanks,
      Mounika."></textarea>
            </div>
        </div>
        <div class="flex-center w-100 pb-20 position-absolute bottom-0">
            <div class="btn-gray mr-20" (click)="modalRef.hide()">
                {{ 'BUTTONS.cancel' | translate }}</div>
            <div class="btn-coal" (click)="onClickSave()" id="btnAddTemplate" data-automate-id="btnAddTemplate">
                <span *ngIf="!isAddtemplateLoading && !isUpdatetemplateLoading else buttonDots">Save</span>
            </div>
        </div>
    </div>
</form>
<ng-template #buttonDots>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>