<button class="btn-coal position-absolute top-20 right-30 ph-position-relative ph-ntop-12 ph-left-12"
    *ngIf="canTemplatesCreate" (click)="openProjectUnitTemplate()">
    <span class="ic-add icon ic-sm"></span>
    <span class="ml-8 ph-d-none">{{'SIDEBAR.add' | translate }} Project Unit {{'BULK_LEAD.template' | translate}}
    </span>
</button>
<div>
    <div class="bg-white w-100 border-gray flex-between mb-3">
        <form autocomplete="off" class="align-center border-end  w-100 py-10 px-12 no-validation">
            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
            <input placeholder="type template name" name="searchQrForm" class="border-0 outline-0 w-100"
                (keydown.enter)="searchTermSubject.next($event.target.value)" (input)="isEmptyInput($event)"
                [(ngModel)]="searchTerm" autocomplete="off" id="inpSearchQrForm">
            <small class="text-muted text-nowrap ph-d-none pr-8">({{ 'LEADS.lead-search-prompt' | translate }})</small>
        </form>
        <div class="show-dropdown-white align-center position-relative ip-br-0">
            <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                    {{ 'GLOBAL.show' | translate}}</span> {{ 'GLOBAL.entries' | translate }}</span>
            <ng-select [items]="showEntriesSize" [formControl]="pageEntry" (change)="assignPageSize()"
                [virtualScroll]="true" [placeholder]="pageSize" class="w-150 tb-w-120px" [searchable]="false">
            </ng-select>
        </div>

    </div>
</div>
<div *ngIf="!isTemplateLoading; else loader">
    <div class="scrollbar scroll-hide tb-w-100-60 table-scrollbar">
        <ng-container *ngIf="templates?.length; else noDataFound">
            <table class="table standard-table no-vertical-border">
                <thead>
                    <tr class="w-100 text-nowrap">
                        <!-- <th class="w-50px">{{'GLOBAL.s-no' | translate}}</th> -->
                        <th class="w-150">{{ 'GLOBAL.name' | translate }}</th>
                        <th class="w-150">Header</th>
                        <th class="w-210">Body</th>
                        <th class="w-150">Footer</th>
                        <th class="w-110">{{ 'GLOBAL.actions' | translate }}</th>
                    </tr>
                </thead>
                <tbody class="text-secondary fw-semi-bold max-h-100-300">
                    <tr *ngFor="let data of templates; index as srNo">
                        <!-- <td class="w-50px">{{srNo+ 1}}</td> -->
                        <td class="w-150">{{data.title}}</td>
                        <td class="w-150">{{data.header}}</td>
                        <td class="w-210">
                            <div class="py-8" [innerHTML]="getSanitizedHtml(data.message)"></div>
                        </td>
                        <td class="w-150">{{data.footer}}</td>
                        <td class="w-110">
                            <div class="align-center">
                                <div title="Edit" class="bg-accent-green icon-badge" *ngIf="canTemplatesUpdate"
                                    (click)="editTemplate(data)"><span class="icon ic-pen ic-xxs"></span></div>
                                <div title="Delete" *ngIf="canTemplatesDelete" class="bg-light-red icon-badge"
                                    (click)="initDeleteTemplate(data.id, data.title)">
                                    <span class="icon ic-delete ic-xxs"></span></div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

        </ng-container>
        <ng-template #noDataFound>
            <div class="flex-center-col mt-40">
                <img src="assets/images/layered-cards.svg" alt="No Data Found">
                <div class="header-3 fw-600 text-center">{{'PROFILE.no-data-found' | translate }}</div>
            </div>
        </ng-template>
    </div>
    <div class="mt-3 flex-end" *ngIf="totalCount">
        <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{currOffset*pageSize + 1}}
            {{ 'GLOBAL.to-small' | translate }} {{currOffset*pageSize + templates?.length}}
            {{ 'GLOBAL.of-small' | translate }} {{totalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
        <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]='getPages(totalCount,pageSize)'
            (pageChange)="onPageChange($event)">
        </pagination>
    </div>
</div>
<ng-template #loader>
    <div class="flex-center h-100-270">
        <application-loader></application-loader>
    </div>
</ng-template>