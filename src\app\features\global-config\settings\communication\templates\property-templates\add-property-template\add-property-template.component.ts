import { Component, ElementRef, EventEmitter, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { skipWhile, take, takeUntil } from 'rxjs';

import {
  GLOBAL_TEMPLATE_VARIABLES,
  PROPERTY_TEMPLATE_VARIABLES,
} from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  AddTemplate,
  UpdateTemplate,
} from 'src/app/reducers/template/template.actions';
import { getAddTemplatesIsLoading, getUpdateTemplatesIsLoading } from 'src/app/reducers/template/template.reducer';

@Component({
  selector: 'add-property-template',
  templateUrl: './add-property-template.component.html',
})
export class AddPropertyTemplateComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('headerTextarea') headerTextarea: ElementRef;
  @ViewChild('messageTextarea') messageTextarea: ElementRef;
  @ViewChild('footerTextarea') footerTextarea: ElementRef;
  isShowHeaderVariable: boolean = false;
  isShowBodyVariable: boolean = false;
  isShowFooterVariable: boolean = false;
  selectedTemplate: any;
  templateForm: FormGroup;
  variables: string[] = PROPERTY_TEMPLATE_VARIABLES.sort();
  globalVariables: string[] = GLOBAL_TEMPLATE_VARIABLES.slice(0, 4);
  filteredVariables: string[] = this.variables;
  searchTerm: string = '';
  isAddtemplateLoading: boolean = false;
  isUpdatetemplateLoading: boolean = false;
  constructor(
    private formBuilder: FormBuilder,
    private _store: Store<AppState>,
    public modalService: BsModalService,
    public modalRef: BsModalRef
  ) {
    this.templateForm = this.formBuilder.group({
      name: ['', ValidationUtil.cannotBeBlank],
      message: [''],
      header: [''],
      footer: [''],
      searchControl: [''],
    });
  }

  ngOnInit(): void {
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const excludeVariables = new Set<string>();

        if (data?.shouldEnablePropertyListing) {
          excludeVariables.add('#NetArea#');
          excludeVariables.add('#No Of BHK#');
          excludeVariables.add('#BHK Type#');
        } else {
          excludeVariables.add('#No Of BR#');
          excludeVariables.add('#BR Type#');
        }
        this.variables = PROPERTY_TEMPLATE_VARIABLES.filter(variable => !excludeVariables.has(variable)).sort();
        this.filteredVariables = this.variables;
      });
    if (this.selectedTemplate) {
      this.templateForm.patchValue({
        name: this.selectedTemplate.title?.trim(),
        message: this.selectedTemplate.message,
        header: this.selectedTemplate.header,
        footer: this.selectedTemplate.footer,
      });
    }

    this.templateForm.get('searchControl').valueChanges.subscribe((value) => {
      this.filteredVariables = this.variables.filter((variable) =>
        variable.toLowerCase()?.replace(" ", "").includes(value.toLowerCase()?.replace(" ", ""))
      );
    });
  }

  onClickSave() {
    if (!this.templateForm.valid) {
      validateAllFormFields(this.templateForm);
      return;
    }
    let payload: any = {
      isEditable: true,
      title: this.templateForm.value.name,
      message: this.templateForm.value.message,
      header: this.templateForm.value.header,
      footer: this.templateForm.value.footer,
      moduleName: 6,
    };
    if (this.selectedTemplate) {
      payload = {
        ...payload,
        id: this.selectedTemplate.id,
      };
      this.isUpdatetemplateLoading = true;
      this._store.dispatch(new UpdateTemplate(payload));
      this._store.select(getUpdateTemplatesIsLoading)
        .pipe(
          skipWhile((isLoading: boolean) => isLoading),
          take(1)
        )
        .subscribe((isLoading: boolean) => {
          this.isUpdatetemplateLoading = isLoading;
          this.modalService.hide();
        });
    } else {
      this.isAddtemplateLoading = true;
      this._store.dispatch(new AddTemplate(payload));
      this._store.select(getAddTemplatesIsLoading)
        .pipe(
          skipWhile((isLoading: boolean) => isLoading),
          take(1)
        )
        .subscribe((isLoading: boolean) => {
          this.isAddtemplateLoading = isLoading;
          this.modalService.hide();
        });
    }
  }

  addVariable(variable: string, section: string) {
    let textarea: HTMLTextAreaElement;
    switch (section) {
      case 'header':
        textarea = this.headerTextarea.nativeElement;
        break;
      case 'message':
        textarea = this.messageTextarea.nativeElement;
        break;
      case 'footer':
        textarea = this.footerTextarea.nativeElement;
        break;
    }
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const updatedText =
        text.substring(0, start) + ' ' + variable + ' ' + text.substring(end);
      this.templateForm.patchValue({
        [section]: updatedText,
      });
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd =
          start + variable.length + 2;
        textarea.focus();
      }, 0);
      this.hideVariable(section);
    }
  }
  hideVariable(section: string) {
    switch (section) {
      case 'header':
        this.isShowHeaderVariable = false;
        break;
      case 'message':
        this.isShowBodyVariable = false;
        break;
      case 'footer':
        this.isShowFooterVariable = false;
        break;
    }
  }
}
