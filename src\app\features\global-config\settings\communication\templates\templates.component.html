<div routerLink='/global-config' [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
    class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<div class="px-30 py-20 position-relative">
    <div class="d-flex">
        <div class="border br-20 bg-white align-center user">
            <div class="activation" [ngClass]="{'active' : selectedSection == 'Lead'}"
                (click)="selectedSection = 'Lead'">
                <span class="icon ic-secondary-filter-solid ic-sm mr-8 ip-mr-4"
                    [ngClass]="{'active' : selectedSection !== 'Lead'}"></span>
                <span class="ip-d-none"> Lead </span>
            </div>
            <div class="activation" [ngClass]="{'active' : selectedSection == 'Property'}"
                (click)="selectedSection = 'Property'">
                <span class="icon ic-house-solid ic-sm mr-8 ip-mr-4"
                    [ngClass]="{'active' : selectedSection !== 'Property'}"></span>
                <span class="ip-d-none"> Property </span>
            </div>
            <div class="activation" [ngClass]="{'active' : selectedSection == 'Project'}"
                (click)="selectedSection = 'Project'">
                <span class="icon ic-buliding-secondary-solid ic-sm mr-8 ip-mr-4"
                    [ngClass]="{'active' : selectedSection !== 'Project'}"></span>

                <span class="ip-d-none"> Project </span>

            </div>
            <div class="activation" [ngClass]="{'active' : selectedSection == 'ProjectUnit'}"
                (click)="selectedSection = 'ProjectUnit'">
                <span class="icon ic-buliding-secondary-cube ic-sm mr-8 ip-mr-4"
                    [ngClass]="{'active' : selectedSection !== 'ProjectUnit'}"></span>

                <span class="ip-d-none"> Project </span><span class="ml-4 ip-d-none">Unit</span>

            </div>
            <div class="activation" [ngClass]="{'active' : selectedSection == 'EngageTo'}"
                (click)="selectedSection = 'EngageTo'">
                <span class="icon ic-engageto ic-sm mr-8 ip-mr-4"
                    [ngClass]="{'active' : selectedSection !== 'EngageTo'}"></span>

                <span class="ip-d-none"> EngageTo </span>
            </div>
        </div>
    </div>
    <div class="mt-20" *ngIf="selectedSection == 'Lead'">
        <lead-templates></lead-templates>
    </div>
    <div class="mt-20" *ngIf="selectedSection == 'Property'">
        <property-templates></property-templates>
    </div>
    <div class="mt-20" *ngIf="selectedSection == 'Project'">
        <project-templates></project-templates>
    </div>
    <div class="mt-20" *ngIf="selectedSection == 'ProjectUnit'">
        <project-unit-templates></project-unit-templates>
    </div>
    <div class="mt-20" *ngIf="selectedSection == 'EngageTo'">
        <iframe #iframeElement [src]="safeUrl" (load)="onIframeLoad()" class="w-100 h-100-160"></iframe>
    </div>
</div>