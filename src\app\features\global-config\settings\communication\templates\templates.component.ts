import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import {
  DomSanitizer,
  SafeResourceUrl,
  Title,
} from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { getTenantName } from 'src/app/core/utils/common.util';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'templates',
  templateUrl: './templates.component.html',
})
export class TemplatesComponent implements OnInit {
  public modules: Array<object> = [
    { title: 'Lead Templates', link: 'lead-templates' },
    { title: 'Property Templates', link: 'property-templates' },
    { title: 'Project Templates', link: 'project-templates' },
    { title: 'Project Unit Templates', link: 'project-unit-templates' },
  ];
  showLeftNav: boolean = true;
  selectedSection: string = 'Lead';
  @ViewChild('iframeElement') iframeElement: ElementRef<HTMLIFrameElement>;
  engageToUrl: string = environment.engageToURL;
  safeUrl: SafeResourceUrl;

  constructor(
    private store: Store<AppState>,
    private shareDataService: ShareDataService,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private sanitizer: DomSanitizer
  ) {
    this.metaTitle.setTitle('CRM | Global Config | Templates');
    this.headerTitle.setLangTitle('BULK_LEAD.templates');
    this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
      this.engageToUrl + 'templates-all'
    );
    // this.store.select(getPermissions).subscribe((permissions: any) => {
    //   if (permissions?.includes('Permissions.Templates.View')) {
    //     this.canTemplatesView = true;
    //   }
    // });
  }

  ngOnInit() {
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
  }

  onIframeLoad() {
    if (this.iframeElement?.nativeElement) {
      const iframe = this.iframeElement.nativeElement;
      iframe.contentWindow?.postMessage(
        {
          tenantId: getTenantName(),
        },
        '*'
      );
    }
  }
}
