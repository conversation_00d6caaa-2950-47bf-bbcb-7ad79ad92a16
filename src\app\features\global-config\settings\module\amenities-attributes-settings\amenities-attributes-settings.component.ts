import { Component, <PERSON>E<PERSON>ter, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { debounceTime, skipWhile, Subject, take, takeUntil } from 'rxjs';

import { PropertyType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { Icon } from 'src/app/core/interfaces/common.interface';
import { Property } from 'src/app/core/interfaces/leads.interface';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { AddCategory, CreateAmenities, CreateAttributes, DeleteAmenity, DeleteAttribute, DoesAmenityNameExists, DoesAttributeNameExists, FetchAllAmenities, FetchAllAttributes, FetchAmenityById, FetchAttributeById, FetchCategoryList, UpdateAmenities, UpdateAttributes } from 'src/app/reducers/amenities-attributes/amenities-attributes.action';
import { doesAemnityNameExists, doesAttrNameExists, getAllAmenities, getAllAttributes, getAmenitiesLoading, getAmenityData, getAmenityDataIsLoading, getAttributeData, getAttributeDataIsLoading, getAttributesLoading, getCategoryList, getCategoryListIsLoading } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import { FetchIconsList } from 'src/app/reducers/custom-tags/custom-tags.actions';
import { getIconsList, getIconsListIsLoading } from 'src/app/reducers/custom-tags/custom-tags.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';

@Component({
  selector: 'amenities-attributes-settings',
  templateUrl: './amenities-attributes-settings.component.html',
})
export class AmenitiesAttributesSettingsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('deleteConfirmFlagModal') deleteConfirmFlagModal: TemplateRef<any>;
  selectedSection: string = 'Amenities';
  searchTermSubject = new Subject<string>();
  searchTerm: string;
  amenitiesCategory: any[];
  amenitiesCategoryLoading: boolean;
  isIconsListLoading: boolean = true;
  iconsList: Icon[];
  filteredIconsList: Icon[];
  modalRef: BsModalRef;
  amenitiesForm: FormGroup;
  attributesForm: FormGroup;
  selectedIcon: Icon;
  iconSearchTerm: string;
  allAmenities: any[];
  filteredAmenities: any[];
  allAttributes: any[];
  filteredAttributes: any;
  amenitiesLoading: boolean;
  attributesLoading: boolean;
  selectedData: any;
  isEditModal: boolean;
  newCategory: string = '';
  canView: boolean;
  canUpdate: boolean;
  amenitiesSearchTerm: string = '';
  attributesSearchTerm: string = '';
  doesAmenityNameExist: boolean = false;
  doesAttrNameExist: boolean = false;
  initialEditState: boolean = false;

  propertyTypeList: Array<Property> = JSON.parse(
    localStorage.getItem('propertyType')
  );
  amenityData: any;
  attributeData: any;
  get noAmenitiesFound(): boolean {
    return this.filteredAmenities?.every(category => category?.amenities?.length === 0);
  }

  constructor(
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private store: Store<AppState>,
    private modalService: BsModalService,
    private fb: FormBuilder,
    private _notificationsService: NotificationsService,
  ) {
    this.metaTitle.setTitle('CRM | Settings');
    this.headerTitle.setLangTitle('Settings');
  }

  ngOnInit() {
    this.amenitiesForm = this.fb.group({
      name: ['', Validators.required],
      category: [null, Validators.required],
      propertyType: [null, Validators.required],
    })

    this.attributesForm = this.fb.group({
      name: ['', Validators.required],
      propertyType: [null, Validators.required],
    })

    this.fetchIcons();
    this.store.dispatch(new FetchAllAmenities());
    this.store.dispatch(new FetchAllAttributes());
    this.store.dispatch(new FetchCategoryList());
    this.store
      .select(getAmenitiesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.amenitiesLoading = isLoading;
      })

    this.store
      .select(getAllAmenities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allAmenities = data.map((category: any) => ({
          ...category,
          amenities: [...category.amenities].sort((a, b) => {
            if (a.isActive !== b.isActive) {
              return Number(b.isActive) - Number(a.isActive);
            }
            const nameA = a.amenityDisplayName || '';
            const nameB = b.amenityDisplayName || '';
            return nameA.localeCompare(nameB);
          })
        }));

        this.filteredAmenities = [...this.allAmenities];
      });



    this.store
      .select(getAttributesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.attributesLoading = isLoading;
      })

    this.store
      .select(getAllAttributes)
      .pipe(skipWhile(() => this.attributesLoading), takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allAttributes = data
          ?.filter((attr: any) => attr?.attributeType === 'Additional')
          ?.sort((a: any, b: any) => {
            if (a.isActive && !b.isActive) {
              return -1;
            } else if (!a.isActive && b.isActive) {
              return 1;
            }
            return a.attributeDisplayName.localeCompare(b.attributeDisplayName);
          });

        this.filteredAttributes = [...this.allAttributes];
      });


    this.store
      .select(getCategoryList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.amenitiesCategory = data
        if (this.newCategory.length) {
          let newCategory: any = this.amenitiesCategory.filter(
            (item) => item?.name === this.newCategory
          );
          if (newCategory[0]?.id) {
            let userData = this.amenitiesForm.value;
            this.amenitiesForm.patchValue({
              ...userData,
              category: newCategory[0]?.id,
            });
          }
        }
      })

    this.store
      .select(getCategoryListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.amenitiesCategoryLoading = isLoading;
      })

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        const permissionsSet = new Set(permissions);
        this.canView = permissionsSet.has('Permissions.GlobalSettings.View');
        this.canUpdate = permissionsSet.has('Permissions.GlobalSettings.Update');
      });

    this.store
      .select(getAmenityData)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.amenityData = data;
      });

    this.store
      .select(getAttributeData)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.attributeData = data;
      });

    this.amenitiesForm
      .get('name')
      .valueChanges.pipe(debounceTime(300))
      .subscribe((value: any) => {
        if (this.initialEditState && this.isEditModal) {
          this.initialEditState = false;
          return;
        }

        if (
          value &&
          this.amenitiesForm.controls.name.status === 'VALID'
        ) {
          this.doesIconNameExists();
        }
      });

    this.attributesForm
      .get('name')
      .valueChanges.pipe(debounceTime(300))
      .subscribe((value: any) => {
        if (this.initialEditState && this.isEditModal) {
          this.initialEditState = false;
          return;
        }

        if (
          value &&
          this.attributesForm.controls.name.status === 'VALID'
        ) {
          this.doesIconNameExists();
        }
      });
  }

  fetchIcons() {
    this.store.dispatch(new FetchIconsList());
    this.store
      .select(getIconsListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isIconsListLoading = isLoading;
      })

    this.store
      .select(getIconsList)
      .pipe(skipWhile(() => this.isIconsListLoading), take(1))
      .subscribe((iconsList: Icon[]) => {
        this.filteredIconsList = iconsList;
        this.iconsList = iconsList;
      })
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  onSearchIcons(searchTerm: string) {
    this.iconSearchTerm = searchTerm.trim().toLowerCase();
    this.filteredIconsList = this.iconsList
      .filter((icon: Icon) => icon.name.toLowerCase().replace(" ", "").includes(this.iconSearchTerm.replace(" ", "")));

    if (this.selectedIcon) {
      this.filteredIconsList = this.filteredIconsList.filter((icon: Icon) => icon.activeImagePath !== this.selectedIcon.activeImagePath);

      this.filteredIconsList.unshift(this.selectedIcon);
    }
  }


  openAddModal(addAmenities: any) {
    this.amenitiesForm.markAsUntouched();
    this.amenitiesForm.markAsPristine();
    this.amenitiesForm.updateValueAndValidity();
    this.attributesForm.markAsUntouched();
    this.attributesForm.markAsPristine();
    this.attributesForm.updateValueAndValidity();

    this.iconSearchTerm = ''
    this.filteredIconsList = this.iconsList;
    if (this.isEditModal) {
      this.selectedIcon = null;
      this.isEditModal = false;
      this.initialEditState = false;
      this.amenitiesForm.reset();
      this.attributesForm.reset();
    }
    this.modalRef = this.modalService.show(addAmenities, {
      class: 'right-modal modal-350 ip-modal-unset'
    });
  }

  create() {
    if (this.selectedSection === 'Amenities') {
      if (!this.amenitiesForm.valid) {
        validateAllFormFields(this.amenitiesForm)
        return;
      }
      if (
        !this.amenitiesForm.valid ||
        this.doesAmenityNameExist
      ) {
        validateAllFormFields(this.amenitiesForm);
        return;
      }
      if (!this.selectedIcon) {
        this._notificationsService.warn("Select an icon to create amenity");
        return
      }
      let amenities = this.amenitiesForm.value;

      let payload = {
        amenityName: amenities?.name,
        amenityDisplayName: amenities?.name,
        category: amenities.category,
        propertyType: (amenities?.propertyType && amenities?.propertyType.length > 0)
          ? amenities.propertyType.map((item: any) => PropertyType[item])
          : Object.values(PropertyType).filter(value => typeof value === 'number'),
        imageURL: this.selectedIcon.activeImagePath,
        inActiveImageURL: this.selectedIcon.inActiveImagePath,
        isActive: true,
      }
      this.store.dispatch(new CreateAmenities(payload))
    } else if (this.selectedSection === "Attribute") {
      if (!this.attributesForm.valid) {
        validateAllFormFields(this.attributesForm)
        return;
      }
      if (
        !this.attributesForm.valid ||
        this.doesAttrNameExist
      ) {
        validateAllFormFields(this.attributesForm);
        return;
      }
      if (!this.selectedIcon) {
        this._notificationsService.warn("Select an icon to create attribute");
        return
      }
      let attributes = this.attributesForm.value;
      let payload = {
        attributeName: attributes?.name,
        attributeDisplayName: attributes?.name,
        activeImageURL: this.selectedIcon.activeImagePath,
        attributeType: 'Additional',
        basePropertyType: (attributes?.propertyType && attributes?.propertyType.length > 0)
          ? attributes.propertyType.map((item: any) => PropertyType[item])
          : Object.values(PropertyType).filter(value => typeof value === 'number'),
        inActiveImageURL: this.selectedIcon.inActiveImagePath,
        isActive: true,
      }
      this.store.dispatch(new CreateAttributes(payload))
    }
    this.hideModal();
    this.searchTerm = null;
  }

  addNewCategory = (category: string = '') => {
    if (category.length) {
      this.store.dispatch(new AddCategory(category));
      this.newCategory = category;
    }
  };

  openEditModal(data: any, editModal: any) {
    this.selectedData = data;
    this.isEditModal = true;
    this.initialEditState = true;
    this.iconSearchTerm = '';
    this.doesAmenityNameExist = false;
    this.doesAttrNameExist = false;
    if (this.selectedSection === 'Amenities') {
      this.amenitiesForm?.get('name').setValue(data?.amenityDisplayName);
      this.amenitiesForm?.get('category').setValue(data?.category);
      this.amenitiesForm?.get('propertyType').setValue(data?.propertyType?.map((item: any) => PropertyType[item]));

      this.selectedIcon = this.iconsList?.find((icon: Icon) => icon.activeImagePath === data?.imageURL);

      this.filteredIconsList = this.selectedIcon ? [
        this.selectedIcon,
        ...this.iconsList?.filter((icon: Icon) => icon.activeImagePath !== data?.imageURL)
      ] : [...this.iconsList?.filter((icon: Icon) => icon.activeImagePath !== data?.imageURL)
      ]

    } else {
      this.attributesForm?.get('name')?.setValue(data?.attributeDisplayName);
      this.attributesForm?.get('propertyType')?.setValue(data?.basePropertyType?.map((item: any) => PropertyType[item]));

      this.selectedIcon = this.iconsList?.find((icon: Icon) => icon.activeImagePath === data.activeImageURL);

      this.filteredIconsList = this.selectedIcon ? [
        this.selectedIcon,
        ...this.iconsList?.filter((icon: Icon) => icon.activeImagePath !== data.activeImageURL)
      ] : [...this.iconsList]
    }

    this.modalRef = this.modalService.show(editModal, {
      class: 'right-modal modal-350 ip-modal-unset'
    });
  }


  onAmenityChecked(event: Event, amenity: any) {
    // amenity.isActive = event.target.checked;
    const isChecked = (event.target as HTMLInputElement).checked;
    let updatedAmenity = {
      ...amenity,
      isActive: isChecked,
    };

    this.store.dispatch(new UpdateAmenities(updatedAmenity));
    this.searchTerm = null;
  }

  onAttributeChecked(event: Event, attribute: any) {
    const isChecked = (event.target as HTMLInputElement).checked;
    let updatedAmenity = {
      ...attribute,
      isActive: isChecked,
    };

    this.store.dispatch(new UpdateAttributes(updatedAmenity));
    this.searchTerm = null;
  }

  update() {
    if (this.selectedSection === 'Amenities') {
      if (!this.amenitiesForm.valid) {
        validateAllFormFields(this.amenitiesForm);
        return;
      }

      const currentName = this.amenitiesForm.get('name').value;
      const originalName = this.selectedData?.amenityDisplayName;

      if (currentName !== originalName && this.doesAmenityNameExist) {
        validateAllFormFields(this.amenitiesForm);
        this._notificationsService.warn("Amenity name already exists");
        return;
      }

      if (!this.selectedIcon) {
        this._notificationsService.warn("Select an icon to update amenity");
        return
      }

      let payload = {
        id: this.selectedData?.id,
        amenityName: this.amenitiesForm.get('name').value,
        amenityDisplayName: this.amenitiesForm.get('name').value,
        propertyType: this.amenitiesForm.get('propertyType').value.length > 0 ?
          this.amenitiesForm.get('propertyType').value.map((item: any) => PropertyType[item]) :
          Object.values(PropertyType).filter(value => typeof value === 'number'),
        imageURL: this.selectedIcon.activeImagePath || this.selectedData.imageURL,
        isActive: this.selectedData?.isActive,
        inActiveImageURL: this.selectedIcon.inActiveImagePath || this.selectedData.inActiveImageURL,
        category: this.amenitiesForm.get('category').value
      }

      this.store.dispatch(new UpdateAmenities(payload))
      this.amenitiesSearchTerm = null;
    } else {
      if (!this.attributesForm.valid) {
        validateAllFormFields(this.attributesForm);
        return;
      }

      const currentName = this.attributesForm.get('name').value;
      const originalName = this.selectedData?.attributeDisplayName;

      if (currentName !== originalName && this.doesAttrNameExist) {
        validateAllFormFields(this.attributesForm);
        this._notificationsService.warn("Attribute name already exists");
        return;
      }

      if (!this.selectedIcon) {
        this._notificationsService.warn("Select an icon to update attribute");
        return
      }

      let payload = {
        id: this.selectedData?.id,
        attributeName: this.attributesForm.get('name').value,
        attributeDisplayName: this.attributesForm.get('name').value,
        basePropertyType: this.attributesForm.get('propertyType').value.length > 0 ?
          this.attributesForm.get('propertyType').value.map((item: any) => PropertyType[item]) :
          Object.values(PropertyType).filter(value => typeof value === 'number'),
        attributeType: 'Additional',
        isActive: this.selectedData?.isActive,
        activeImageURL: this.selectedIcon.activeImagePath || this.selectedData.activeImageURL,
        inActiveImageURL: this.selectedIcon.inActiveImagePath || this.selectedData.inActiveImageURL,
      }

      this.store.dispatch(new UpdateAttributes(payload))
      this.attributesSearchTerm = null;
    }
    this.searchTerm = null;
    this.hideModal();
  }

  delete(data: any) {
    this.selectedData = data;
    if (this.modalRef) {
      this.modalRef.hide();
    }

    if (this.selectedSection === 'Amenities') {
      this.store.dispatch(new FetchAmenityById(this.selectedData?.id));
    } else {
      this.store.dispatch(new FetchAttributeById(this.selectedData?.id));
    }

    const isLoading$ =
      this.selectedSection === 'Amenities'
        ? this.store.select(getAmenityDataIsLoading)
        : this.store.select(getAttributeDataIsLoading);

    isLoading$
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        if (!isLoading && this.modalService.getModalsCount() === 0) {
          const data =
            this.selectedSection === 'Amenities'
              ? this.amenityData
              : this.attributeData;

          if (data?.project || data?.properties || data?.units) {
            let associatedItems: string[] = [];

            if (data?.project) {
              associatedItems.push(`${data.project} project(s)`);
            }
            if (data?.properties) {
              associatedItems.push(`${data.properties} property(s)`);
            }
            if (data?.units) {
              associatedItems.push(`${data.units} unit(s)`);
            }

            const associatedMessage = associatedItems.join(', ');

            let initialState: any = {
              type: 'amenityAttr',
              data: {
                fieldType: 'Delete',
                message: `${associatedMessage} associated with <b>"${this.selectedData?.amenityDisplayName || this.selectedData?.attributeDisplayName}"</b>. Are you sure you want to delete?`,
                heading: this.selectedSection === 'Amenities' ? 'Deleting Amenity?' : 'Deleting Attribute?',
              },
              class: 'modal-400 modal-dialog-centered ph-modal-unset',
            };

            this.modalRef = this.modalService.show(UserAlertPopupComponent, {
              class: 'modal-450 modal-dialog-centered ph-modal-unset',
              initialState,
            });
          } else {
            let initialState: any = {
              type: 'amenityAttr',
              data: {
                fieldType: 'Delete',
                message: `Are you sure you want to delete <b>"${this.selectedData?.amenityDisplayName || this.selectedData?.attributeDisplayName}"</b>?`,
                heading: this.selectedSection === 'Amenities' ? 'Deleting Amenity?' : 'Deleting Attribute?',
              },
              class: 'modal-400 modal-dialog-centered ph-modal-unset',
            };

            this.modalRef = this.modalService.show(UserAlertPopupComponent, {
              class: 'modal-450 modal-dialog-centered ph-modal-unset',
              initialState,
            });
          }

          if (this.modalRef?.onHide) {
            this.modalRef.onHide.subscribe((reason: string) => {
              if (reason === 'confirmed') {
                if (this.selectedSection === 'Amenities') {
                  this.store.dispatch(new DeleteAmenity(this.selectedData?.id));
                  this.amenitiesSearchTerm = null;
                } else {
                  this.store.dispatch(new DeleteAttribute(this.selectedData?.id));
                  this.attributesSearchTerm = null;
                }
                this.searchTerm = null;
                this.hideModal();
              }
            });
          }
        }
      });
  }

  search($event: any) {
    const searchTerm = $event.target.value.trim();

    if (this.selectedSection === 'Amenities') {
      this.amenitiesSearchTerm = searchTerm;
    } else if (this.selectedSection === 'Attribute') {
      this.attributesSearchTerm = searchTerm;
    }
    this.applySearch();
  }

  applySearch() {
    const searchTerm =
      this.selectedSection === 'Amenities'
        ? this.amenitiesSearchTerm || ''
        : this.attributesSearchTerm || '';

    // Just convert to lowercase and trim, don't remove spaces yet
    const normalizedSearchTerm = searchTerm.toLowerCase().trim();

    // If search term is empty, show all items
    if (!normalizedSearchTerm) {
      if (this.selectedSection === 'Amenities') {
        this.filteredAmenities = [...this.allAmenities];
      } else if (this.selectedSection === 'Attribute') {
        this.filteredAttributes = [...this.allAttributes];
      }
      return;
    }

    // Split by spaces to get individual search words
    const searchWords = normalizedSearchTerm.split(/\s+/).filter(word => word.length > 0);

    if (this.selectedSection === 'Amenities') {
      this.filteredAmenities = this.allAmenities?.map((category: any) => {
        const filteredData = category?.amenities?.filter((amenity: any) => {
          // Convert amenity name to lowercase for case-insensitive comparison
          const amenityName = (amenity?.amenityDisplayName || amenity?.amenityName || '').toLowerCase();

          // Check if all search words are included in the amenity name
          return searchWords.every(word => amenityName.includes(word));
        });
        return { ...category, amenities: [...filteredData] };
      });
    } else if (this.selectedSection === 'Attribute') {
      this.filteredAttributes = this.allAttributes?.filter((attribute: any) => {
        // Convert attribute name to lowercase for case-insensitive comparison
        const attributeName = (attribute?.attributeDisplayName || attribute?.attributeName || '').toLowerCase();

        // Check if all search words are included in the attribute name
        return searchWords.every(word => attributeName.includes(word));
      });
    }
  }

  onSectionChange(newSection: string) {
    this.selectedSection = newSection;
    if (this.selectedSection === 'Amenities') {
      this.searchTerm = this.amenitiesSearchTerm || '';
    } else if (this.selectedSection === 'Attribute') {
      this.searchTerm = this.attributesSearchTerm || '';
    }

    this.applySearch();
  }

  doesIconNameExists() {
    let name = '';
    if (this.selectedSection === 'Amenities') {
      name = this.amenitiesForm.value.name.replace(/\s+/g, '');

      if (this.isEditModal && this.selectedData?.amenityDisplayName === name) {
        this.doesAmenityNameExist = false;
        return;
      }

      this.store.dispatch(new DoesAmenityNameExists(name));
      this.store.dispatch(new LoaderHide());
      this.store
        .select(doesAemnityNameExists)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.doesAmenityNameExist = data;
        });
    } else if (this.selectedSection === 'Attribute') {
      name = this.attributesForm.value.name.replace(/\s+/g, '');

      if (this.isEditModal && this.selectedData?.attributeDisplayName === name) {
        this.doesAttrNameExist = false;
        return;
      }

      this.store.dispatch(new DoesAttributeNameExists(name));
      this.store.dispatch(new LoaderHide());
      this.store
        .select(doesAttrNameExists)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.doesAttrNameExist = data;
        });
    }
  }


  getFirstCharacter(name: string) {
    return name?.charAt(0).toUpperCase();
  }

  hideModal() {
    // if (this.isEditModal) {
    this.amenitiesForm.reset();
    this.attributesForm.reset();
    this.selectedData = null;
    this.selectedIcon = null;
    this.isEditModal = false;
    this.initialEditState = false;
    // }
    this.modalRef?.hide();
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
