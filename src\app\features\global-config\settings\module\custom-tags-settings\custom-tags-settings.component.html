<div class="pt-20 px-30">
    <div class=" bg-light-pearl">
        <div class="flex-between">
            <div class="align-center">
                <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-12" routerLink='/global-config'></div>
                <span class="icon ic-green-190 ic-tags ic-lg"></span>
                <h5 class="fw-600 ml-4">Tags</h5>
            </div>
            <div class="btn-coal" (click)="openAddTagModal(addTag)">
                <span class="ic-add icon ic-xxs"></span>
                <span class="ml-8 ph-d-none">Add New Tag</span>
            </div>
        </div>
        <div class="pt-16">
            <div class="bg-white w-100 border-gray">
                <form autocomplete="off" class="align-center py-10 px-12 no-validation">
                    <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
                    <input (input)="searchFlags($event)" placeholder="type to search" name="searchCustomFlag"
                        class="border-0 outline-0 w-100" autocomplete="off" [(ngModel)]="flagSearchTerm"
                        id="inpSearchCustomFlag">
                </form>
            </div>
        </div>
    </div>
    <ng-container *ngIf="!isFlagsLoading; else loader">
        <form [formGroup]="customTagsForm" class="pt-20 bg-white w-100">
            <ng-container *ngIf="flagsList?.length">
                <div class="d-flex flex-wrap bg-white pl-30 pr-20 flex-grow-1 scrollbar max-h-100-210">
                    <div class="bg-white mb-16 w-25 tb-w-50 ip-w-100 position-relative tag-card"
                        *ngFor="let flag of flagsList">
                        <div class="mr-10 border br-6 shadow-hover-sm py-10 px-8"
                            [ngClass]="{'border-bottom-black': flag?.isActive}">
                            <div class="d-flex flex-between">
                                <div class="align-center mr-10">
                                    <div class="w-40 h-40 obj-fill flex-center">
                                        <img [type]="'leadrat'" [appImage]="flag?.isActive ? flag.activeImagePath : flag.inactiveImagePath"
                                            class="w-20px h-20px">
                                    </div>
                                    <div class="flex-col align-start ml-12">
                                        <h5 class="fw-600 text-truncate-1 break-all" [title]="convertStatus(flag.name)">
                                            {{convertStatus(flag.name)}}</h5>
                                        <h6 class="text-dark-gray pt-4 text-truncate-1 break-all" [title]="flag.notes">
                                            {{flag.notes}}</h6>
                                    </div>
                                </div>
                                <div class="align-center position-relative">
                                    <input type="checkbox" (click)="flagConfirmModal(flag)"
                                        class="toggle-switch toggle-active-sold" formControlName="{{flag.name}}"
                                        id={{flag.name}} name="exportLeads">
                                    <label for={{flag.name}} class="switch-label"></label>
                                </div>
                            </div>
                            <div class="border-bottom my-10 w-100"></div>
                            <div class="w-100 flex-between">
                                <div class="d-flex w-33 text-nowrap">Leads: <div
                                        class="text-truncate-1 break-all fw-600 ml-6"> {{flag?.leadCount ?? 0}}</div>
                                </div>
                                <!-- <div class="d-flex w-33 text-nowrap">Data:<div class="text-truncate-1 break-all">0</div></div>
                                <div class="d-flex w-33 text-nowrap">User:<div class="text-truncate-1 break-all">0</div></div> -->
                                <div class="d-flex">
                                    <div title="Edit" class="bg-accent-green icon-badge "
                                        (click)="openEditModal(flag,addTag)"> <span
                                            class="icon ic-pen m-auto ic-xxs"></span></div>
                                    <div title="Delete" class="bg-red-350 icon-badge " (click)="DeleteTag(flag)"> <span
                                            class="icon ic-delete m-auto ic-xxs"></span></div>
                                </div>
                            </div>

                        </div>
                        <!-- <div class="d-flex position-absolute bg-white top-4 left-4 bottom-4 h-80 w-90 flex-center action-option opacity-0">
                            <div title="Edit"  class="bg-accent-green icon-badge" (click)="openEditModal(flag,addTag)">  <span class="icon ic-pen m-auto ic-xxs"></span></div>
                            <div class="border m-6 h-10"></div>
                            <div title="Delete"  class="bg-red-350 icon-badge" (click)="DeleteTag(flag)">  <span class="icon ic-delete m-auto ic-xxs"></span></div>
                        </div> -->
                    </div>
                </div>
            </ng-container>
            <ng-container *ngIf="!flagsList?.length">
                <div class="flex-center-col h-100-200">
                    <img src="assets/images/layered-cards.svg" alt="No lead found">
                    <div class="header-3 fw-600 text-center">No Tags Found</div>
                </div>
            </ng-container>
        </form>
    </ng-container>
</div>

<ng-template #confirmFlagModal>
    <div class="p-20">
        <h3 class="text-black-100 fw-semi-bold mb-20">Are you sure you want to {{enableOrDisable}} the <span
                class="fw-600">"{{ confirmFlagMOdalMessage }}"</span> tag?</h3>
        <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Description: {{confirmFlagMOdalDescription}}
        </div>
        <div class="flex-end mt-30">
            <button class="btn-gray mr-20" (click)="selectFlags(false)" id="clkSettingsNo"
                data-automate-id="clkSettingsNo">
                {{ 'GLOBAL.no' | translate }}</button>
            <button class="btn-green" (click)="selectFlags(true)" id="clkSettingsYes" data-automate-id="clkSettingsYes">
                {{ 'GLOBAL.yes' | translate }}</button>
        </div>
    </div>
</ng-template>

<ng-template #deleteConfirmFlagModal>
    <div class="p-20">
        <h3 class="text-black-100 fw-semi-bold mb-20">Are you sure you want to delete the <span class="fw-600">"{{
                selectedFlag?.name }}"</span> tag?</h3>
        <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Description: {{selectedFlag?.notes}}</div>
        <div class="flex-end mt-30">
            <button class="btn-gray mr-20" (click)="hideModal()" id="clkSettingsNo" data-automate-id="clkSettingsNo">
                {{ 'GLOBAL.no' | translate }}</button>
            <button class="btn-green" (click)="confirmTagDelete()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
                {{ 'GLOBAL.yes' | translate }}</button>
        </div>
    </div>
</ng-template>

<ng-template #addTag>
    <div class="max-w-350 min-w-350">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{isEditModal ? 'Update Tag' :'Add New Tag'}}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="hideModal()"></div>

        </div>
        <div class="p-10 bg-light-slate">
            <form [formGroup]="addTagForm">
                <div class="bg-white px-10 py-16 mb-10">
                    <div for="iconName" class="field-label-req mt-0">
                        Tag Name</div>
                    <form-errors-wrapper label="Tag Name" [control]="addTagForm.controls['iconName']">
                        <input type="text" appDebounceInput [debounceTime]="500"
                            (debounceEvent)="duplicateFlagNameValidator(addTagForm.get('iconName'))"
                            formControlName="iconName" id="iconName" data-automate-id="iconName" autocomplete="off"
                            placeholder="ex. Warm" />
                    </form-errors-wrapper>
                </div>
                <div class="bg-white px-10 py-16 mb-10">
                    <div for="iconDescription" class="field-label-req mt-0">
                        Description</div>
                    <form-errors-wrapper label="Description" [control]="addTagForm.controls['iconDescription']">
                        <input type="text" formControlName="iconDescription" id="iconDescription"
                            data-automate-id="iconDescription" autocomplete="off"
                            placeholder="ex. A Warm tag represents a lead that has......." />
                    </form-errors-wrapper>
                </div>

            </form>
            <div class="bg-white px-10 py-10">
                <div class="align-center flex-between">
                    <div for="icons" class="field-label mt-0">
                        Select tags</div>
                    <div>
                        <div class="flex-between no-validation px-10 py-8 border-gray br-16 bg-white">
                            <input type="text" placeholder="type to search" class="border-0 outline-0 w-100"
                                [(ngModel)]="customIconSearchTerm" (input)="onSearchIcons($event?.target?.value)" />
                            <span class="search icon ic-search-solid ic-sm ic-slate-90"></span>

                        </div>
                    </div>
                </div>
                <div class="h-10 flex-end">
                    <div *ngIf="iconError" class=" mt-1 me-3 text-xs text-red  fw-semi-bold z-index-1021">Select Tag
                        Icon</div>
                </div>


                <div class="h-100-400">
                    <div class="max-h-100-400 scrollbar scrollbar-hide mt-10 w-100 d-flex flex-wrap">
                        <span *ngFor="let icon of filteredIconsList;" class="cursor-pointer w-16pr"
                            (click)="selectedCustomFlag = icon; iconError = false">
                            <div class="w-40 h-40 flex-center obj-fill"
                                [ngClass]="icon?.id===selectedCustomFlag?.id ? ' bg-light-slate br-4 ': ''">
                                <img [type]="'leadrat'" [appImage]="icon?.id===selectedCustomFlag?.id ? icon?.activeImagePath : icon?.inActiveImagePath"
                                    [alt]="icon?.name" class="w-20px h-20px" [title]="icon?.name">
                            </div>
                        </span>
                    </div>
                </div>
            </div>

        </div>
        <div class="flex-end p-10 bg-white w-100">
            <button class="btn-gray mr-20" (click)="hideModal()">{{ 'BUTTONS.cancel' | translate
                }}</button>
            <button class="btn-coal" (click)="isEditModal ? updateFlag() : createFlag()">Save Tag</button>
        </div>
    </div>
</ng-template>

<ng-template #loader>
    <div class="flex-center h-100 mt-60">
        <application-loader></application-loader>
    </div>
</ng-template>