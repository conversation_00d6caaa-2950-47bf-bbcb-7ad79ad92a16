import { Component, EventEmitter, OnD<PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { debounceTime, distinctUntilChanged, skipWhile, take, takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { Icon } from 'src/app/core/interfaces/common.interface';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { CreateTag, DeleteTagById, FetchIconsList, FetchTagsListWithCount, UpdateTag, UpdateTagsLists } from 'src/app/reducers/custom-tags/custom-tags.actions';
import { getCustomTagsWithCountIsLoading, getIconsList, getIconsListIsLoading, getTagsListWithCount } from 'src/app/reducers/custom-tags/custom-tags.reducer';
import { CustomTagService } from 'src/app/services/controllers/custom-tag.service';

@Component({
  selector: 'custom-tags-settings',
  templateUrl: './custom-tags-settings.component.html',
})
export class CustomTagsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('confirmFlagModal') confirmFlagModal: TemplateRef<any>;
  @ViewChild('deleteConfirmFlagModal') deleteConfirmFlagModal: TemplateRef<any>;
  confirmFlagMOdalMessage: string;
  confirmFlagMOdalDescription: string;
  modalRef: BsModalRef;
  selectedFlag: any;
  enableOrDisable: string;
  originalFlagsList: any;
  flagsList: Array<any> = [];
  customTagsForm: FormGroup;
  addTagForm: FormGroup;
  isIconsListLoading: boolean = true;
  iconsList: Icon[];
  filteredIconsList: Icon[];
  selectedCustomFlag: Icon;
  customIconSearchTerm: string;
  flagSearchTerm: string;
  isEditModal: boolean;
  isFlagsLoading: boolean;
  iconError: boolean;

  constructor(
    private store: Store<AppState>,
    private modalService: BsModalService,
    private fb: FormBuilder,
    private _notificationsService: NotificationsService,
    private customService: CustomTagService,
  ) { }

  ngOnInit(): void {
    this.fetchIcons();
    this.customTagsForm = this.fb.group({});
    this.addTagForm = this.fb.group({
      iconName: [null, [Validators.required, Validators.maxLength(20)]],
      iconDescription: [null, Validators.required]
    });
    this.store.dispatch(new FetchTagsListWithCount());
    this.store
      .select(getCustomTagsWithCountIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isFlagsLoading = data;
      });
    this.store
      .select(getTagsListWithCount)
      .subscribe((data: any) => {
        let flagData = data || [];
        if (flagData.length > 0) {
          this.flagSearchTerm = '';
          this.flagsList = flagData;
          const formGroup: any = {};
          flagData?.forEach((flag: any) => {
            formGroup[flag.name] = [flag.isActive];
          });
          this.customTagsForm = this.fb.group(formGroup);
          this.sortFlagsInAssending();
          this.originalFlagsList = [...this.flagsList];
        }
      });
  }

  fetchIcons() {
    this.store.dispatch(new FetchIconsList());
    this.store
      .select(getIconsListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isIconsListLoading = isLoading;
      });
    this.store
      .select(getIconsList)
      .pipe(skipWhile(() => this.isIconsListLoading), take(1))
      .subscribe((iconsList: Icon[]) => {
        this.filteredIconsList = iconsList;
        this.iconsList = iconsList;
      });

  }
  duplicateFlagNameValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null;
    }

    if (control.value.replace(/\s+/g, '') === this.selectedFlag?.name?.replace(/\s+/g, '')) {
      this.updateErrors(control, 'alreadyExist', false);
      return null;
    }

    this.customService.checkDuplicate(control.value.replace(/\s+/g, '')).pipe(take(1)).subscribe((data: any) => {
      this.updateErrors(control, 'alreadyExist', data?.data);
    });

    return null;
  }

  updateErrors(control: AbstractControl, errorKey: string, addError: boolean) {
    if (!control) return;

    const errors = control.errors || {};

    if (addError) {
      errors[errorKey] = true;
    } else {
      delete errors[errorKey];
    }

    control.setErrors(Object.keys(errors).length ? errors : null);
  }



  onSearchIcons(searchTerm: string) {
    this.customIconSearchTerm = searchTerm;
    this.filteredIconsList = this.iconsList.filter((icon: Icon) => icon.name.toLowerCase().replace(" ", "").includes(searchTerm.toLowerCase().replace(" ", "")));
  }

  searchFlags($event: any) {
    const searchTerm = $event.target.value.toLowerCase();
    this.flagsList = this.originalFlagsList.filter((flag: any) => flag.name.toLowerCase().includes(searchTerm.toLowerCase().trim()));
    this.sortFlagsInAssending();
  }

  selectFlags(isYesClicked: boolean): void {
    if (isYesClicked) {
      this.selectedFlag = { ...this.selectedFlag, isActive: !this.selectedFlag?.isActive };
      this.flagsList = this.flagsList?.map((flag: any) => {
        if (flag?.id == this.selectedFlag?.id) {
          return this.selectedFlag;
        }
        return flag;
      });
      this.store.dispatch(new UpdateTagsLists(this.selectedFlag));
      this.modalRef.hide();
      return;
    }
    this.flagsList = [...this.flagsList];
    this.customTagsForm?.patchValue({
      [this.selectedFlag.name]: this.selectedFlag?.isActive
    });
    this.modalRef.hide();
    return;
  }

  sortFlagsInAssending() {
    const sortedFlags = [...this.flagsList];
    sortedFlags.sort((a, b) => {
      if (a.isActive === b.isActive) {
        const nameA = a.name.toUpperCase();
        const nameB = b.name.toUpperCase();
        return nameA.localeCompare(nameB);
      } else {
        return a.isActive ? -1 : 1;
      }
    });
    this.flagsList = sortedFlags;
  }

  flagConfirmModal(message: any) {
    if (message?.isActive) {
      this.enableOrDisable = 'disable';
    } else {
      this.enableOrDisable = 'enable';
    }
    this.selectedFlag = message;
    this.confirmFlagMOdalMessage = message?.name;
    this.confirmFlagMOdalDescription = message?.notes;
    this.modalRef = this.modalService.show(this.confirmFlagModal, {
      class: 'modal-500 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
    });
  }

  openAddTagModal(addTag: any) {
    this.addTagForm.markAsUntouched();
    this.addTagForm.markAsPristine();
    this.addTagForm.updateValueAndValidity();
    this.iconError = false;
    this.customIconSearchTerm = '';
    this.filteredIconsList = this.iconsList;
    this.selectedCustomFlag = null;
    this.isEditModal = false;
    this.selectedFlag = null;
    this.addTagForm.reset();

    this.modalRef = this.modalService.show(addTag, {
      class: 'right-modal modal-350 ip-modal-unset'
    });
  }

  convertStatus(originalStatus: string): string {
    // Remove the 'Is' prefix and convert the remaining string to title case
    const convertedStatus = originalStatus.replace(/^Is/, '').replace(/([a-z])([A-Z])/g, '$1 $2');
    return convertedStatus;
  }

  createFlag() {
    if (!this.addTagForm?.valid || !this.selectedCustomFlag) {
      validateAllFormFields(this.addTagForm);
      this.iconError = !this.selectedCustomFlag;
      return;
    }


    const form: any = this.addTagForm?.value;

    this.store?.dispatch(new CreateTag({
      "name": form?.iconName,
      "activeImagePath": this.selectedCustomFlag?.activeImagePath,
      "inactiveImagePath": this.selectedCustomFlag?.inActiveImagePath,
      "notes": form?.iconDescription,
      "module": "leads",
      "isActive": true
    }));

    this.selectedCustomFlag = null;
    this.customIconSearchTerm = null;
    this.addTagForm.reset();
    this.modalRef?.hide();
  }

  DeleteTag(tag: any) {
    this.selectedFlag = tag;
    this.modalRef = this.modalService.show(this.deleteConfirmFlagModal, {
      class: 'modal-500 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
    });
  }

  confirmTagDelete() {
    this.store.dispatch(new DeleteTagById(this.selectedFlag?.id));
    this.hideModal();
  }

  openEditModal(flag: any, addTag: any) {
    this.iconError = false;
    this.selectedFlag = flag;
    this.isEditModal = true;
    this.customIconSearchTerm = '';
    this.filteredIconsList = this.iconsList;
    this.addTagForm?.get('iconName').setValue(flag?.name?.trim());
    this.addTagForm?.get('iconDescription').setValue(flag?.notes?.trim());
    this.selectedCustomFlag = this.iconsList?.filter((icon: Icon) => icon.activeImagePath === flag.activeImagePath)[0];
    this.modalRef = this.modalService.show(addTag, {
      class: 'right-modal modal-350 ip-modal-unset'
    });
  }

  updateFlag() {
    if (!this.addTagForm?.valid || !this.selectedCustomFlag) {
      validateAllFormFields(this.addTagForm);
      this.iconError = !this.selectedCustomFlag;
      return;
    }

    const form: any = this.addTagForm?.value;

    this.store.dispatch(new UpdateTag({
      "name": form?.iconName,
      "activeImagePath": this.selectedCustomFlag?.activeImagePath,
      "inactiveImagePath": this.selectedCustomFlag?.inActiveImagePath,
      "notes": form?.iconDescription,
      "module": "leads",
      "isActive": true,
      "id": this.selectedFlag?.id
    }));
    this.selectedCustomFlag = null;
    this.customIconSearchTerm = null;
    this.addTagForm.reset();
    this.hideModal();
  }

  hideModal() {
    if (this.isEditModal) {
      this.addTagForm.reset();
      this.selectedCustomFlag = null;
      this.isEditModal = false;
    }
    this.modalRef.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}