<ng-container>
    <div class="pt-12 px-30 position-relative">
        <div class="flex-between">
            <div class="pt-12 align-center">
                <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/global-config'></div>
                <span class="icon ic-folder-transfer ic-sm ic-black mr-8"></span>
                <h5 class="fw-600">Data Migration</h5>
            </div>
            <div class="btn-full-dropdown btn-w-100">
                <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2">
                    <span class="ic-tracker icon ic-xxs"></span>
                    <span class="ml-8 ip-d-none">Tracker</span>
                </div>
                <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
                    [(ngModel)]="selectedTrackerOption" (click)="openMigrationTracker()">
                    <ng-option (click)="selectedTrackerOption = null" value="leadMigration">
                        <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                        Lead Tracker
                    </ng-option>
                    <ng-option (click)="selectedTrackerOption = null" value="dataMigration">
                        <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                        Data Tracker
                    </ng-option>
                </ng-select>
            </div>
        </div>
        <div class="bg-white px-20 py-16 mt-20 flex-between br-6">
            <div>
                <h5 class="fw-600">Lead Migration</h5>
                <h6 class="text-dark-gray pt-4">you can upload your previous CRM lead data to {{getAppName()}} CRM via
                    excel upload</h6>
            </div>
            <div class="btn-coal ml-10" id="btnUpdateBulkLead" data-automate-id="btnUpdateBulkLead"
                (click)="navigateToLeadMigration()">
                <span class="ic-upload icon ic-xxs"></span>
                <span class="ml-8 ip-d-none">{{ 'LEADS.upload' | translate }}</span>
            </div>
        </div>
        <div class="bg-white px-20 py-16 mt-20 flex-between br-6">
            <div>
                <h5 class="fw-600">Data Migration</h5>
                <h6 class="text-dark-gray pt-4">you can upload your previous CRM raw data to {{getAppName()}} CRM via
                    excel upload</h6>
            </div>
            <div class="btn-coal ml-10" id="btnUpdateBulkData" data-automate-id="btnUpdateBulkData"
                (click)="navigateToDataMigration()">
                <span class="ic-upload icon ic-xxs"></span>
                <span class="ml-8 ip-d-none">{{ 'LEADS.upload' | translate }}</span>
            </div>
        </div>
    </div>
    <ng-template #changePopup>
        <div class="p-20">
            <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
            <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
            <div class="flex-end mt-30">
                <button class="btn-gray mr-20" (click)="closePopup()" id="clkSettingsNo" data-automate-id="clkSettingsNo">
                    {{ 'GLOBAL.no' | translate }}</button>
                <button class="btn-green" (click)="onSave()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
                    {{ 'GLOBAL.yes' | translate }}</button>
            </div>
        </div>
    </ng-template>
</ng-container>