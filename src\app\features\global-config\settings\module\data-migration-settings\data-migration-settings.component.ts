import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { AppState } from 'src/app/app.reducer';
import { getAppName } from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { FetchDataMigrateExcelUploadedList } from 'src/app/reducers/data/data-management.actions';
import { FetchMigrateExcelUploadedList } from 'src/app/reducers/lead/lead.actions';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'data-migration-settings',
  templateUrl: './data-migration-settings.component.html',
})
export class DataMigrationSettingsComponent {
  selectedTrackerOption: string;
  isGlobalConfigMigration: boolean = false;
  getAppName = getAppName;

  constructor(private router: Router,
    private _store: Store<AppState>,
    public modalService: BsModalService,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
  ) { }

  ngOnInit(): void {
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
  }

  openMigrationTracker() {
    let initialState: any = {};

    if (this.selectedTrackerOption === 'dataMigration') {
      initialState.fieldType = 'Data Migration';

      this._store.dispatch(new FetchDataMigrateExcelUploadedList(1, 10));
      this.modalService.show(ExcelUploadedStatusComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
    } else if (this.selectedTrackerOption === 'leadMigration') {
      initialState.fieldType = 'Lead Migration';

      this._store.dispatch(new FetchMigrateExcelUploadedList(1, 10));
      this.modalService.show(ExcelUploadedStatusComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
    }

    this.selectedTrackerOption = '';
  }
  
  checkUrl() {
    const currentUrl = this.router.url;
    if (currentUrl === '/global-config/migration') {
      this.isGlobalConfigMigration = true;
    } else {
      this.isGlobalConfigMigration = false;
    }
  }

  navigateToLeadMigration() {
    this.router.navigate(['global-config/leads-migration'])
  }

  navigateToDataMigration() {
    this.router.navigate(['global-config/data-migration'])
  }
}

