import { Compo<PERSON>, EventE<PERSON>ter, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import {
  UpdateGlobalSettings
} from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'data-settings',
  templateUrl: './data-settings.component.html',
})
export class DataSettingsComponent implements OnInit, <PERSON><PERSON><PERSON>roy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  canView: boolean;
  canUpdate: boolean;
  dataSettingsForm: FormGroup;
  message: string;
  notes: string;
  settingData: any;

  constructor(
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    public metaTitle: Title
  ) { }

  ngOnInit(): void {
    this.dataSettingsForm = this.fb.group({
      exportData: [null],
    });
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canView = permissionsSet.has('Permissions.GlobalSettings.View');
        this.canUpdate = permissionsSet.has(
          'Permissions.GlobalSettings.Update'
        );
      });
    this.patchValues();
  }

  patchValues() {
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.settingData = data;
        this.dataSettingsForm.patchValue({
          exportData: data.isExportDataEnabled,
        });
      });
  }

  openConfirmModal(changePopup: any, settingType: string) {
    this.modalRef = this.modalService.show(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    switch (settingType) {
      case 'exportData':
        if (this.dataSettingsForm?.value?.exportData) {
          this.message =
            'Are you sure you want to disable the “Export” option?';
          this.notes =
            'Users without export permission will not be able to export data anymore.';
        } else {
          this.message = 'Are you sure you want to enable the “Export” option?';
          this.notes =
            'Users with export permission will be able to export data.';
        }
        break;
    }
  }

  onSave() {
    const settingsData: any = this.dataSettingsForm?.value;
    let payload: any = {
      ...this.settingData,
      isExportDataEnabled: settingsData.exportData,
    };
    this._store.dispatch(new UpdateGlobalSettings(payload));
    this.modalRef.hide();
  }

  closePopup() {
    this.patchValues();
    this.modalRef.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
