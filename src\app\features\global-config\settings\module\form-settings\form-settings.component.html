<ng-container>
    <div class="pt-12 position-relative px-20">
        <div class="flex-between align-center">
            <div class="pt-12 align-center">
                <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/global-config'></div>
                <span class="icon ic-user-filter ic-large ic-accent-green cursor-pointer mr-8"></span>
                <h5 class="fw-600">Manage Forms</h5>
            </div>
        </div>
        <form [formGroup]="customSettingsForm">
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Customise your CRM Forms</h5>
                    <h6 class="text-dark-gray pt-4">You can customise forms of the CRM based on your requirement</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{customSettingsForm.get('customForm').value == true ? 'on' : 'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="openConfirmModal(changePopup, 'customForm')" formControlName="customForm"
                        id="chkCustomForm" name="customForm">
                    <label for="chkCustomForm" class="switch-label"></label>
                </div>
            </div>
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Manage Lead Form</h5>
                    <h6 class="text-dark-gray pt-4">You can customise lead form based on your requirement</h6>
                </div>
                <ng-container *ngIf="customSettingsForm.get('customForm').value == true">
                    <div class="align-center mr-50 ph-mr-20 ml-20 cursor-pointer"
                        [routerLink]="['/global-config/lead-customization']">
                        <span class="text-sm mr-10">Click to Manage</span>
                        <span class="icon ic-black ic-xxxs ic-triangle-up rotate-90"></span>
                    </div>
                </ng-container>
            </div>
        </form>
    </div>
    <ng-template #changePopup>
        <div class="p-20">
            <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
            <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
            <div class="flex-end mt-30">
                <button class="btn-gray mr-20" (click)="closePopup()" id="clkSettingsNo"
                    data-automate-id="clkSettingsNo">
                    {{ 'GLOBAL.no' | translate }}</button>
                <button class="btn-green" (click)="onSave()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
                    {{ 'GLOBAL.yes' | translate }}</button>
            </div>
        </div>
    </ng-template>
</ng-container>