import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { UpdateGlobalSettings } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'form-settings',
  templateUrl: './form-settings.component.html',
})
export class FormSettingsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  customSettingsForm: FormGroup;
  message: string;
  notes: string;
  settingData: any;

  constructor(private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private store: Store<AppState>,
  ) { }

  ngOnInit(): void {
    this.customSettingsForm = this.fb.group({
      customForm: [null],
    });
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this.patchValues();
  }

  openConfirmModal(changePopup: any, settingType: string) {
    this.modalRef = this.modalService.show(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    switch (settingType) {
      case 'customForm':
        if (this.customSettingsForm.value.customForm) {
          this.message =
            'Are you sure you want to disable "Custom Lead Form"?';
          this.notes =
            'This will remove the current form and default form will be activated removed or unavailable fields will be affected in integration';
        } else {
          this.message =
            'Are you sure you want to enable "Custom Lead Form"?';
          this.notes =
            'You can edit,add remove fields which are available in lead form';
        }
        break;
    }
  }

  patchValues() {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.settingData = data;
        this.customSettingsForm.patchValue({
          ...this.settingData,
          customForm: this.settingData?.isConfiguredLeadFormEnabled,

        })
      })
  }

  onSave() {
    let settingsData = this.customSettingsForm.value;

    let payload: any = {
      ...this.settingData,
      isConfiguredLeadFormEnabled: settingsData.customForm,
    };
    this.store.dispatch(new UpdateGlobalSettings(payload));
    this.modalRef.hide();
  }

  closePopup() {
    this.patchValues();
    this.modalRef.hide();

  }
}
