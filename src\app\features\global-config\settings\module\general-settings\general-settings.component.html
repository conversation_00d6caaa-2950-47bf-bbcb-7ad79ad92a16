<ng-container *ngIf="canView">
    <div class="pt-12 position-relative px-20">
        <div class="flex-between align-center">
            <div class="pt-12 align-center">
                <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/global-config'></div>
                <span class="icon ic-hexagon ic-sm ic-black mr-8"></span>
                <h5 class="fw-600">{{ 'SIDEBAR.general' | translate }} {{ 'GLOBAL.settings' | translate }}</h5>
            </div>
        </div>
        <form [formGroup]="leadSettingsForm" [ngClass]="{'pe-none blinking' : isGlobalSettingLoading}">
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'SETTINGS.international-number' | translate }}</h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.international-description'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('internationalNo').value == true ? 'on' :
                        'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'internationalNo') : ''"
                        formControlName="internationalNo" id="chkInternationalNo" name="internationalNo"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkInternationalNo" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Call Notification</h5>
                    <h6 class="text-dark-gray pt-4">you can send a notification to a mobile device to initiate a
                        call with a lead/data.</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('mobileCall').value == true ? 'on' :
                        'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'mobileCall') : ''"
                        formControlName="mobileCall" id="chkmobileCall" name="mobileCall"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkmobileCall" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <div class="mt-12 bg-white mb-12 br-6">
                <div class="bg-white pl-20 py-16  flex-between br-6">
                    <div>
                        <h5 class="fw-600">Call Detection
                        </h5>
                        <h6 class="text-dark-gray pt-4">you can enable or disable the call detection for your tenant
                        </h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="text-xs mr-8">{{leadSettingsForm.get('callDetection').value == true ? 'on' :
                            'off'}}
                        </div>
                        <input type="checkbox" class="toggle-switch toggle-active-sold"
                            (click)="canUpdate ? openConfirmModal(changePopup, 'callDetection') : ''"
                            formControlName="callDetection" id="chkCallDetection" name="callDetection"
                            [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="chkCallDetection" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>
                <ng-container *ngIf="leadSettingsForm.get('callDetection').value">
                    <div class="border-top">
                        <h6 class="text-dark-gray px-30 py-10">which lead calls would you like to track?</h6>
                        <div class="px-30 pb-10 ph-px-16">
                            <div class="d-flex">
                                <div class="flex-wrap ph-mr-4 mr-10 px-16  br-20 align-center text-nowrap bg-slate py-10"
                                    (click)="IsAssignedCallLogsEnabled = false;onSave()">
                                    <div [class.checked]="IsAssignedCallLogsEnabled === false"
                                        class="radio-button cursor-pointer">

                                    </div>
                                    <label class="ml-6 align-center fw-semi-bold text-black cursor-pointer">All</label>
                                </div>
                                <div class="flex-wrap ph-mr-4 mr-10 px-16  br-20 align-center text-nowrap bg-slate py-10"
                                    (click)="IsAssignedCallLogsEnabled = true;onSave()">
                                    <div [class.checked]="IsAssignedCallLogsEnabled === true"
                                        class="radio-button cursor-pointer">
                                    </div>
                                    <label
                                        class="ml-6 align-center fw-semi-bold text-black cursor-pointer">Assigned</label>

                                </div>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
            <!-- <div class="py-16 my-12 br-6 bg-white">
                <div class="pl-20 flex-between pb-8">
                    <div>
                        <h5 class="fw-600">Set Time-Zone Automatically <span
                                *ngIf="leadSettingsForm.get('timeZone')?.value && false"
                                class="dot dot-sm text-white bg-coal br-50 ml-6 text-sm">?</span></h5>
                        <h6 class="text-dark-gray pt-4">You can enable or disable automatic time-zone</h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="text-xs mr-8">{{leadSettingsForm.get('timeZone')?.value == true ? 'on' :
                            'off'}}</div>
                        <input type="checkbox" class="toggle-switch toggle-active-sold"
                            (click)="canUpdate ? openConfirmModal(changePopup, 'timeZone') : ''"
                            formControlName="timeZone" id="timeZone" name="timeZone" (change)="onToggleTimeZone()"
                            [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="timeZone" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>
                <div *ngIf="leadSettingsForm.get('timeZone')?.value == false" class="border-top">
                    <div class="py-16 flex-between" [ngClass]="{'border-bottom': !last}">
                        <label class="checkbox-container">
                            <h5 class="fw-60 text-dark-800">Select the default time Zone</h5>
                            <div class="text-dark-gray text-xs">
                                You can select timeZone
                            </div>
                        </label>
                        <div class="w-160 mr-50">
                            <ng-select (change)="onSave()" [virtualScroll]="true" [items]="timezones"
                                [closeOnSelect]="true" placeholder="Select" bindValue="timeZone"
                                formControlName="timeZone">
                            </ng-select>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Default Unit</h5>
                    <h6 class="text-dark-gray pt-4">you can select the default unit</h6>
                </div>
                <div class="w-160 mr-50">
                    <ng-select [virtualScroll]="true" [items]="areaSizeUnits"
                        *ngIf="!areaUnitsIsLoading else fieldLoader" ResizableDropdown placeholder="ex. sq.feet."
                        bindLabel="unit" bindValue="id" formControlName="areaSizeUnit" (change)="onSave($event)">
                    </ng-select>
                </div>
            </div>
            <div class="mt-12 bg-white mb-12 br-6">
                <div class="flex-between py-16 pl-20 cursor-pointer"
                    (click)="isCurrencyListOpen = !isCurrencyListOpen; copyCurrency();"
                    [ngClass]="{'border-bottom' : isCurrencyListOpen}">
                    <div>
                        <h5 class="fw-600">Currency
                            <span *ngIf="isCurrencyListOpen && false"
                                class="dot dot-sm text-white bg-coal br-50 ml-6 text-sm">?</span>
                        </h5>
                        <h6 class="text-dark-gray mt-4">select multiple currencies from the dropdown list to enable
                            and
                            select default out of them.</h6>
                    </div>
                    <div class="flex-between align-center ml-30">
                        <div class="mr-10 flex-between align-center ph-flex-wrap">
                            <h6 class="text-dark-gray mt-4 mr-4">Default Currency:</h6>
                            <div class="ph-mt-10 ph-mr-16 bg-secondary px-30 py-10 br-20 align-center text-nowrap">
                                <div class=" align-center cursor-pointer fw-600">{{defaultCurrency}}
                                    ({{defaultSymbol}})</div>
                            </div>
                        </div>
                        <div class="icon ic-xxs ic-coal mr-50 ph-mr-20"
                            [ngClass]="isCurrencyListOpen ? 'ic-triangle-up' : 'ic-triangle-down'"></div>
                    </div>
                </div>
                <ng-container *ngIf="isCurrencyListOpen">
                    <div class="border-bottom">
                        <div class="px-30 py-20 ph-px-16">
                            <div class="d-flex ip-flex-col">
                                <div>
                                    <h6 class="text-dark-gray mb-10">Add more currencies</h6>
                                    <div class="align-center">
                                        <ng-select [virtualScroll]="true" [items]="currencyList" ResizableDropdown
                                            (change)="canUpdate ? updateSelectedCurrencies($event) : ''"
                                            [multiple]="true" [closeOnSelect]="false" bindLabel="currency"
                                            name="currency" formControlName="currency" class="bg-white w-250 ph-w-100"
                                            placeholder="{{ 'GLOBAL.select' | translate }} currency"
                                            [ngClass]="{'pe-none' : !canUpdate}">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <div class="flex-between">
                                                    <div class="checkbox-container">
                                                        <input type="checkbox" id="item-{{index}}"
                                                            data-automate-id="item-{{index}}"
                                                            [checked]="item$.selected">
                                                        <span class="checkmark"></span>
                                                        <span>
                                                            {{item.currency}} ({{item.symbol}})
                                                        </span>
                                                    </div>
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                        <div *ngIf="selectedCurrencies?.length > 0"
                                            class="border-bottom w-10 mx-10 ip-d-none">
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <h6 *ngIf="selectedCurrencies?.length > 0"
                                        class="text-dark-gray mb-10 ml-10 ip-ml-0 ip-mt-10">select
                                        default from this added currencies</h6>
                                    <div class="d-flex flex-wrap ml-6 scrollbar max-h-100px ip-ml-0">
                                        <div *ngFor="let currency of selectedCurrencies"
                                            class="flex-wrap ph-mr-4 mr-10 bg-secondary px-10 py-12 br-20 align-center text-nowrap mb-20"
                                            (click)="canUpdate ? selectCurrency(currency.symbol,currency.currency): ''">
                                            <div [class.checked]="defaultSymbol === currency.symbol"
                                                class="radio-button"></div>
                                            <label
                                                class="ml-10 align-center fw-600 cursor-pointer">{{currency.currency}}
                                                ({{ currency.symbol }})</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex-end px-20 pb-16 pt-12" *ngIf="isChangesMade">
                        <div class="text-decoration-underline cursor-pointer mr-10" (click)="onCancel()">
                            {{ 'BUTTONS.cancel' | translate }}</div>
                        <div class="btn-coal" (click)="onSave()">{{ 'BUTTONS.save' | translate }}</div>
                    </div>
                </ng-container>
            </div>
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6"
                *ngIf="leadSettingsForm.get('internationalNo').value">
                <div>
                    <h5 class="fw-600">Country Code</h5>
                    <h6 class="text-dark-gray pt-4">you can select the country code</h6>
                </div>
                <div class="w-160 mr-50">
                    <ng-select (change)="onCountryCodeChange($event)" formControlName="countryCode"
                        [ngClass]="{'pe-none' : !canUpdate}" ResizableDropdown>
                        <ng-option *ngFor="let code of countryCodeList" [value]="code.countryCode">
                            {{code.name}}
                        </ng-option>
                    </ng-select>
                </div>
            </div>

            <!-- watermark settings -->
            <div class="bg-white py-16 mt-20">
                <div class="pl-20 justify-between">
                    <div>
                        <h5 class="fw-600">Watermark Settings</h5>
                        <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.water-mark-project-description'| translate }}
                        </h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="text-xs mr-8">{{leadSettingsForm.get('isProjectLogo').value == true ? 'on' :
                            'off'}}</div>
                        <input type="checkbox" class="toggle-switch toggle-active-sold"
                            (click)="canUpdate ? onSaveWatermark() : ''" formControlName="isProjectLogo"
                            id="isProjectLogo" name="isProjectLogo" [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="isProjectLogo" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>
            </div>
            <div *ngIf="leadSettingsForm.get('isProjectLogo')?.value" class="border-top mb-20">
                <div class="d-flex tb-flex-col">
                    <div class="w-60pr tb-w-100 mr-6">
                        <div class="justify-center bg-white py-10 w-100 fw-semi-bold">
                            <u>Watermark Preview</u>
                        </div>
                        <div class="flex-center bg-watermark mt-6 py-4">
                            <div class="w-70 h-330 py-10 relative-container">
                                <div
                                    class="h-100 w-100 br-25 border border-5 watermark-main-image position-relative overflow-hidden">
                                    <img src="{{ waterMarkSettingsObj.defaultLogoType === 'UserOrganizationLogo' ? waterMarkSettingsObj.profilePic : waterMarkSettingsObj.isImage ? waterMarkSettingsObj.ImagePathUrl : waterMarkSettingsObj.profilePic}}"
                                        [ngClass]="waterMarkSettingsObj.currentPosition"
                                        [style.opacity]="waterMarkSettingsObj.watermarkOpacity"
                                        [style.height.%]="waterMarkSettingsObj.watermarkSize"
                                        class="position-absolute" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-40pr tb-w-100 h-375 bg-white">
                        <div class="w-100 scrollbar px-20 py-16 h-330">
                            <div class="field-label mt-0">Set Watermark Image</div>
                            <div class="d-flex ph-flex-col">
                                <div *ngFor="let option of logoOptions"
                                    [ngClass]="{'pe-none disabled': option?.disabled}"
                                    class="flex-wrap ph-mr-4 ph-mt-6 mr-10 p-10 br-20 align-center text-nowrap bg-slate"
                                    (click)="canUpdate ? selectLogoType(option.key): ''">
                                    <div [class.checked]="waterMarkSettingsObj.defaultLogoType === option.key"
                                        class="radio-button"
                                        [ngClass]="waterMarkSettingsObj.defaultLogoType !== option.key?'text-gray cursor-pointer':'text-black'">
                                    </div>
                                    <label class="ml-6 align-center fw-semi-bold"
                                        [ngClass]="waterMarkSettingsObj.defaultLogoType !== option.key?'text-gray cursor-pointer':'text-black'">{{
                                        option.label }}</label>
                                </div>
                            </div>
                            <div *ngIf="waterMarkSettingsObj.defaultLogoType === 'UploadFile'"
                                class="flex-between bg-slate mt-16 br-6 p-4">
                                <div *ngIf="!waterMarkSettingsObj.selectedFile" (click)="triggerFileInput()"
                                    class="flex-between w-100 cursor-pointer">
                                    <span class="text-black-200 align-center">
                                        <div class="icon ic-sm ic-gray ic-paper-clip mr-6"></div>
                                        Select An Image
                                    </span>
                                    <div class="d-block br-4 bg-black flex-center px-6 py-10 text-white">
                                        Upload Image
                                    </div>
                                </div>
                                <div *ngIf="waterMarkSettingsObj.selectedFile" class="py-10 w-100 justify-between">
                                    <div class="text-black-200 break-all text-truncate-1 mr-4">
                                        {{formatFileName(waterMarkSettingsObj.selectedFile) }}
                                    </div>
                                    <div>
                                        <span class="mr-12 text-accent-green cursor-pointer"
                                            (click)="triggerFileInput()">
                                            <u>Replace</u>
                                        </span>
                                        <span class="mr-10 text-red cursor-pointer"
                                            (click)="waterMarkSettingsObj.selectedFile = null; waterMarkSettingsObj.ImagePathUrl = null">
                                            <u>Delete</u>
                                        </span>
                                    </div>
                                </div>
                                <input type="file" #fileInput (change)="onFileSelected($event)" />
                            </div>
                            <div class="field-label">Set Watermark Positioning</div>
                            <div class="text-xs text-dark-gray">placement of your watermark over the image</div>
                            <div class="d-flex overflow-auto ip-w-100-70 scrollbar scroll-hide mt-8">
                                <div *ngFor="let item of waterMarkSettingsObj.positions; let i = index"
                                    class="text-center align-center-col mr-28" (click)="selectPosition(item, i, false)">
                                    <div class="h-24 w-24 br-4 position-relative border overflow-hidden cursor-pointer">
                                        <div [ngClass]="[getCssClass(item.position), waterMarkSettingsObj.selectedPositionIndex === i ? 'bg-accent-green' : 'bg-gray-dark']"
                                            class="w-8 h-8 position-absolute br-2"></div>
                                    </div>
                                    <div class="mt-2 text-sm cursor-pointer"
                                        [ngClass]="waterMarkSettingsObj.selectedPositionIndex === i ? 'text-dark fw-semi-bold' : 'text-muted'">
                                        {{item.position}}</div>
                                </div>
                            </div>
                            <div class="field-label">Image Opacity</div>
                            <div class="align-center mt-12">
                                <div class="border-black-dashed br-50 w-16 h-16"></div>
                                <input type="range" class="w-80pr mx-12 custom-slider"
                                    [value]="waterMarkSettingsObj.watermarkOpacity * 100"
                                    (input)="changeOpacity($event)" />
                                <div class="dot bg-coal"></div>
                            </div>
                            <div class="field-label">Image Size</div>
                            <div class="align-center mt-12">
                                <div class="icon ic-xs w-16 h-16 ic-degree-camera text-dark"></div>
                                <input min="7" type="range" class="w-80pr mx-12 custom-slider"
                                    [value]="waterMarkSettingsObj.watermarkSize"
                                    (input)="changeWatermarkSize($event.target.value)" />
                                <div class="icon w-16 h-16 ic-degree-camera text-dark"></div>
                            </div>
                        </div>
                        <div class="flex-end border-top px-20 py-8 bg-white">
                            <u class="mr-20 text-black-200 text-large fw-bold cursor-pointer mr-10"
                                (click)="gotoGlobalConfig()">Cancel</u>
                            <div class="btn-coal" (click)="saveWaterMark()">Save Settings</div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <ng-template #changePopup>
        <div class="p-20">
            <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
            <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
            <div class="flex-end mt-30">
                <button class="btn-gray mr-20" (click)="closePopup()" id="clkSettingsNo"
                    data-automate-id="clkSettingsNo">
                    {{ 'GLOBAL.no' | translate }}</button>
                <button class="btn-green" (click)="onSave()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
                    {{ 'GLOBAL.yes' | translate }}</button>
            </div>
        </div>
    </ng-template>
</ng-container>