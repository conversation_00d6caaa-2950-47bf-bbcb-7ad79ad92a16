<div [ngClass]="{'pe-none blinking': allAcountListLoading }" class="h-100vh bg-light-pearl">
    <div class="bg-dark flex-center flex-between p-12 h-40 position-relative">
        <h4 class="text-white fw-400">Configuration</h4>
        <span (click)="modalService.hide()"
            class="icon ic-close ic-sm position-absolute top-7 right-8 cursor-pointer"></span>
    </div>

    <div class="flex-between mt-20 ml-12">
        <div class="align-center ml-4">
            <img [appImage]="selectedSource?.imageURL ? s3BucketUrl + selectedSource?.imageURL : ''"
                [type]="'defaultAvatar'" height="27px" width="27px" alt="Logo" />
            <h3 class="fw-600 ml-8">{{ selectedSource?.displayName }}</h3>
        </div>
        <ng-container *ngIf="allAcountList?.length && !isAdding">
            <div *ngIf="permissions?.has('Permissions.ListingIntegration.Create')" class="flex-end mr-16">
                <div class="btn-left-dropdown" (click)="showForm()">
                    <span class="ic-add icon ic-xxs"></span>
                    <span class="ml-8 ip-d-none">Add Acount</span>
                </div>
                <div class="btn-right-dropdown btn-w-30 black-100">
                    <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
                        [(ngModel)]="selectedOption" (click)="openBulkUpload()">
                        <ng-option (click)="selectedOption = null" value="bulkUpload">
                            {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
                        <ng-option (click)="selectedOption = null" value="tracker">
                            {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }} Tracker</ng-option>
                    </ng-select>
                </div>
            </div>
        </ng-container>
    </div>
    <ng-container *ngIf="!allAcountList?.length || isAdding; else showTable">
        <form [formGroup]="addConfigForm" class="px-10">
            <div class="flex-col px-10 w-100 mt-10 pb-12">
                <div class="w-100 gap-2 d-flex">
                    <div class="w-50">
                        <div class="field-label-req">Account Name</div>
                        <form-errors-wrapper [control]="addConfigForm.controls['accountName']" label="name">
                            <input type="text" required id="inpname" data-automate-id="inpname"
                                formControlName="accountName" autocomplete="off" placeholder="e.g., Shanu" />
                        </form-errors-wrapper>
                    </div>
                    <div class="w-50">
                        <div class="field-label-req">URL</div>
                        <form-errors-wrapper [control]="addConfigForm.controls['baseUri']" label="url">
                            <input type="text" required id="inpUrl" data-automate-id="inpUrl" formControlName="baseUri"
                                autocomplete="off" placeholder="e.g., propertyfinder.com" />
                        </form-errors-wrapper>
                    </div>
                </div>
                <div class="w-100 gap-2 flex-between">
                    <div class="w-50">
                        <div class="field-label">Enter Secret Key</div>
                        <div class="form-group">
                            <input type="text" id="inpSecretKey" data-automate-id="inpSecretKey" autocomplete="off"
                                formControlName="secretKey" placeholder="Enter secret key" />
                        </div>
                    </div>
                    <div class="w-50">
                        <div class="field-label-req">Enter API Key</div>
                        <form-errors-wrapper [control]="addConfigForm.controls['apiKey']" label="apiKey">
                            <input type="text" required id="inpApiKey" data-automate-id="inpApiKey"
                                formControlName="apiKey" autocomplete="off" placeholder="Enter API key" />
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
        </form>
        <ng-container *ngIf="allAcountList?.length">
            <h5 class="fw-semi-bold m-16">Added Accounts</h5>
            <div class="bg-white px-16 scrollbar table-scrollbar scroll-hide">
                <table class="table standard-table no-vertical-border">
                    <thead>
                        <tr class="w-100 text-nowrap">
                            <th class="w-190">{{ 'SIDEBAR.account' | translate }}</th>
                            <th class="w-130">{{ 'GLOBAL.actions' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary fw-semi-bold scrollbar table-scrollbar h-100-400">
                        <ng-container *ngFor="let list of allAcountList">
                            <tr class="w-100">
                                <td class="w-190">
                                    <div [title]="list?.accountName" class="text-truncate w-100">
                                        {{ list.accountName }}
                                    </div>
                                </td>
                                <td class="w-130">
                                    <div class="align-center">
                                        <div *ngIf="permissions?.has('Permissions.ListingIntegration.Update')"
                                            title="Edit" class="bg-accent-green icon-badge" (click)="onEdit(list)">
                                            <span class="icon ic-pen ic-xxxs"></span>
                                        </div>
                                        <div *ngIf="permissions?.has('Permissions.ListingIntegration.Delete')"
                                            title="Delete" class="bg-light-red icon-badge"
                                            (click)="onDelete(list?.id, list?.accountName)">
                                            <span class="icon ic-trash ic-xxxs"></span>
                                        </div>
                                        <div title="Copy" class="bg-brown icon-badge"
                                            (click)="copyUrl(list?.listingApiUrl)">
                                            <span class="icon ic-copy-clipboard ic-xxxs"></span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
        </ng-container>
        <div class="w-100 flex-end p-10 gap-4 position-absolute bottom-0 bg-white">
            <h6 class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer"
                (click)="allAcountList?.length ? onCancel() : modalService.hide()">
                {{ 'BUTTONS.cancel' | translate }}
            </h6>
            <button (click)="onSave()" class="btn-coal mr-20">Save</button>
        </div>
    </ng-container>
    <ng-template #showTable>
        <h5 class="fw-semi-bold m-16">Added Accounts</h5>
        <div class="bg-white px-16 scrollbar table-scrollbar">
            <table class="table standard-table no-vertical-border">
                <thead>
                    <tr class="w-100 text-nowrap">
                        <th class="w-190">{{ 'SIDEBAR.account' | translate }}</th>
                        <th class="w-130">{{ 'GLOBAL.actions' | translate }}</th>
                    </tr>
                </thead>
                <tbody class="text-secondary fw-semi-bold scrollbar table-scrollbar  h-100-200">
                    <ng-container *ngFor="let list of allAcountList">
                        <tr class="w-100">
                            <td class="w-190">
                                <div [title]="list?.accountName" class="text-truncate w-100">
                                    {{ list.accountName }}
                                </div>
                            </td>
                            <td class="w-130">
                                <div class="align-center">
                                    <div *ngIf="permissions?.has('Permissions.ListingIntegration.Update')" title="Edit"
                                        class="bg-accent-green icon-badge" (click)="onEdit(list)">
                                        <span class="icon ic-pen ic-xxxs"></span>
                                    </div>
                                    <div *ngIf="permissions?.has('Permissions.ListingIntegration.Delete')"
                                        title="Delete" class="bg-light-red icon-badge"
                                        (click)="onDelete(list?.id, list?.accountName)">
                                        <span class="icon ic-trash ic-xxxs"></span>
                                    </div>
                                    <div title="Copy" class="bg-brown icon-badge"
                                        (click)="copyUrl(list?.listingApiUrl)">
                                        <span class="icon ic-copy-clipboard ic-xxxs"></span>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
    </ng-template>
</div>