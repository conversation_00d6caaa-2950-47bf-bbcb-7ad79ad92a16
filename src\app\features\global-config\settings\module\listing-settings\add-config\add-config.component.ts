import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { AddListingAcounts, DeleteListingAcounts, FetchExcelTrackerList, FetchListingAcounts, UpdateListingAcounts } from 'src/app/reducers/listing-site/listing-site.actions';
import { getListingAcount, getListingSiteLoaders } from 'src/app/reducers/listing-site/listing-site.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment as env } from 'src/environments/environment';
@Component({
  selector: 'add-config',
  templateUrl: './add-config.component.html',
})
export class AddConfigComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  s3BucketUrl = env.s3ImageBucketURL
  addConfigForm: FormGroup;
  selectedSource: any;
  formFields: any = {
    accountName: [null, Validators.required],
    baseUri: [null, Validators.required],
    apiKey: [null, Validators.required],
    secretKey: [null],
  };
  allAcountList: any;
  selectedAccount: any;
  isEditing: boolean;
  editingAccountId: any;
  isAdding: boolean;
  allAcountListLoading: any;
  selectedOption: string;
  canUpdate: any;
  permissions: Set<unknown>;

  constructor(
    private fb: FormBuilder,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private _translateService: TranslateService,
    private _notificationService: NotificationsService,
    private router: Router,
  ) { }

  ngOnInit(): void {
    this.initializeForms();
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.permissions = new Set(permissions);
        this.canUpdate = permissions?.includes('Permissions.GlobalSettings.Update');
      });

    this.store.dispatch(new FetchListingAcounts(this.selectedSource?.id))
    this.store
      .select(getListingAcount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allAcountList = data
      })

    this.store
      .select(getListingSiteLoaders)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allAcountListLoading = data?.allAcountsListed
      })
  }

  initializeForms() {
    this.addConfigForm = this.fb.group({});
    Object.keys(this.formFields).forEach((key) => {
      const [initialValue, ...validators] = this.formFields[key];
      this.addConfigForm.addControl(key, new FormControl(initialValue, validators));
    });
  }

  onSave() {
    if (!this.addConfigForm.valid) {
      validateAllFormFields(this.addConfigForm);
      return;
    }
    const payload = {
      listingSourceId: this.selectedSource?.id,
      ...this.addConfigForm.value
    }
    if (this.isEditing && this.editingAccountId) {
      this.store.dispatch(
        new UpdateListingAcounts({
          id: this.editingAccountId,
          ...payload,
        })
      );
    } else {
      this.store.dispatch(new AddListingAcounts(payload));
    }
    this.onReset();
  }

  onEdit(account: any): void {
    this.selectedAccount = account;
    this.editingAccountId = account?.id;
    this.isAdding = true;
    this.isEditing = true;
    this.addConfigForm.patchValue({
      ...account
    });
  }

  onDelete(id: string, acountName: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: acountName,
      fieldType: 'acount',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteListingAcounts({ id: id, sourceId: this.selectedSource.id }));
          this.addConfigForm.reset();
        }
      });
    }
  }

  copyUrl(apiKey: any): void {
    navigator.clipboard?.writeText(apiKey);
    this._notificationService.success(
      this._translateService.instant('GLOBAL.link-copied')
    );
  }

  openBulkUpload() {
    const queryParams = { id: this.selectedSource?.id };
    if (this.selectedOption === 'bulkUpload') {
      this.router.navigate(['properties/bulk-address'], { queryParams });
      this.modalRef.hide();
    } else if (this.selectedOption === 'tracker') {
      let initialState: any = {
        fieldType: 'address',
      };
      this.store.dispatch(new FetchExcelTrackerList(1, 10));
      this.modalService.show(ExcelUploadedStatusComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
      this.modalRef.hide();
    }
    this.selectedOption = '';
  }

  showForm(): void {
    this.isAdding = true;
    this.addConfigForm.reset();
  }

  onCancel(): void {
    this.isAdding = false;
  }

  onReset() {
    this.addConfigForm.reset();
    this.isEditing = false;
    this.isAdding = false;
    this.selectedAccount = null;
  }
}
