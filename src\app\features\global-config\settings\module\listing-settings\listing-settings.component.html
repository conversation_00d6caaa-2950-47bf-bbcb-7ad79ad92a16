<ng-container *ngIf="permissions?.has('Permissions.GlobalSettings.View')">
    <div class="pt-12 px-30 position-relative">
        <div class="flex-between">
            <div class="pt-12 align-center">
                <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/global-config'></div>
                <span class="icon ic-converter ic-sm ic-black cursor-pointer mr-8"></span>
                <h5 class="fw-600">Listing configuration</h5>
            </div>
        </div>
        <form [formGroup]="listingSettingsForm">
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'REPORTS.export' | translate }} Property</h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.export-description'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{listingSettingsForm.get('exportProperty').value == true ? 'on' : 'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="permissions?.has('Permissions.GlobalSettings.Update') ? openConfirmModal(changePopup, 'exportProperty') : ''"
                        formControlName="exportProperty" id="chkexportProperty" name="exportProperty"
                        [ngClass]="{'pe-none' : !permissions?.has('Permissions.GlobalSettings.Update')}">
                    <label for="chkexportProperty" class="switch-label"
                        [ngClass]="{'pe-none' : !permissions?.has('Permissions.GlobalSettings.Update')}"></label>
                </div>
            </div>
            <div *ngIf="permissions?.has('Permissions.ListingIntegration.View')"
                class="bg-white pl-20 py-16 mt-20 flex-between br-6" (click)="isListingOpen = !isListingOpen">
                <div>
                    <h5 class="fw-600">Listing management</h5>
                    <h6 class="text-dark-gray pt-4">Add, list and delist your property </h6>
                </div>
                <div class="align-center cursor-pointer ph-mr-20 ml-20">
                    <div class="icon ic-xxxs ic-coal mr-50 ph-mr-20"
                        [ngClass]="isListingOpen ? 'ic-triangle-up' : 'ic-triangle-down'"></div>
                </div>
            </div>
            <div *ngIf="isListingOpen" [ngClass]="{ 'pe-none blinking' : listingSourceLoading }"
                class="border-top bg-white">
                <div *ngFor="let source of listingSource;" class="mx-20 py-16">
                    <div class="flex-between">
                        <div class="align-center">
                            <img [appImage]="source?.imageURL? s3BucketUrl+source?.imageURL: ''"
                                [type]="'defaultAvatar'" alt="logo" height="18px" width="18px" class="mr-8">
                            <h5 class="fw-600 text-coal">{{source.displayName}}</h5>
                        </div>
                        <div class="align-center mr-12" (click)="openConfig(source)">
                            <h6 class="cursor-pointer mt-12 text-sm text-black-100 fw-400">Click to manage<span
                                    class="ml-20 rotate-90 icon ic-black ic-triangle-up ic-x-xs"></span></h6>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <ng-template #changePopup>
        <div class="p-20">
            <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
            <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
            <div class="flex-end mt-30">
                <button class="btn-gray mr-20" (click)="closePopup()" id="clkSettingsNo" data-automate-id="clkSettingsNo">
                    {{ 'GLOBAL.no' | translate }}</button>
                <button class="btn-green" (click)="onSave()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
                    {{ 'GLOBAL.yes' | translate }}</button>
            </div>
        </div>
    </ng-template>
</ng-container>