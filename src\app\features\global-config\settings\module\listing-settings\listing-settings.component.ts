import {
  Component,
  EventEmitter,
  OnInit
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import {
  UpdateListing
} from 'src/app/reducers/global-settings/global-settings.actions';
import {
  getGlobalSettingsAnonymous
} from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchListingSource } from 'src/app/reducers/listing-site/listing-site.actions';
import { getListingSiteLoaders, getListingSources } from 'src/app/reducers/listing-site/listing-site.reducer';
import { getPropertyTypes } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { environment as env } from 'src/environments/environment';
import { AddConfigComponent } from './add-config/add-config.component';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
@Component({
  selector: 'listing-settings',
  templateUrl: './listing-settings.component.html',
})
export class ListingSettingsComponent implements OnInit {

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  listingSettingsForm: FormGroup;
  message: string;
  notes: string;
  settingProperty: any;
  listingSource: any[];
  formFields: any = {
    exportProperty: [null],
  };
  s3BucketUrl: string = env.s3ImageBucketURL;
  listingSourceLoading: any;
  isListingOpen: boolean = false;
  permissions: any;

  constructor(
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    public metaTitle: Title
  ) { }

  ngOnInit(): void {
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this.initializeForms();
    this.initializeDispatch();
    this.initializeSubscriptions();
  }


  openConfirmModal(changePopup: any, settingType: string) {
    const settings: any = {
      exportProperty: {
        enabledMessage: 'Are you sure you want to enable the “Export” option?',
        disabledMessage: 'Are you sure you want to disable the “Export” option?',
        enabledNotes: 'Users with export permission will be able to export property.',
        disabledNotes: 'Users without export permission will not be able to export property anymore.'
      },
    };

    const setting = settings[settingType];
    if (!setting) {
      return;
    }
    const settingData = this.listingSettingsForm?.value[settingType];
    const isEnabled = settingData ? 'disabled' : 'enabled';

    this.message = setting[`${isEnabled}Message`];
    this.notes = setting[`${isEnabled}Notes`];
    this.openModal(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
  }


  openConfig(source: any) {
    this.openModal(AddConfigComponent, {
      class: 'right-modal modal-400 ph-modal-unset',
      initialState: { selectedSource: source },
    });
  }

  onSave() {
    const listingSettingsForm: any = this.listingSettingsForm?.value;
    let payload: any = {
      ...this.settingProperty,
      isPropertiesExportEnabled: listingSettingsForm?.exportProperty,
    };
    this._store.dispatch(new UpdateListing(payload));
    this.modalRef.hide();

    this._store
      .select(getPropertyTypes)
      .pipe(filter((propertyTypes: any) => !!propertyTypes))
      .subscribe((propertyTypes: any) => {
        localStorage.setItem('propertyType', JSON.stringify(propertyTypes));
      });
  }

  closePopup() {
    this.listingSettingsForm.patchValue({
      exportProperty: this.settingProperty?.isPropertiesExportEnabled,
    });
    this.modalRef.hide();
  }

  initializeForms() {
    this.listingSettingsForm = this.fb.group({});
    Object.keys(this.formFields).forEach((key) => {
      const [initialValue] = this.formFields[key];
      this.listingSettingsForm.addControl(key, new FormControl(initialValue));
    });
  }

  initializeDispatch() {
    this._store.dispatch(new FetchListingSource())
  }

  initializeSubscriptions() {
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.permissions = new Set(permissions);
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.settingProperty = data;
        this.listingSettingsForm.patchValue({
          exportProperty: data?.isPropertiesExportEnabled,
        });
      });
    this._store
      .select(getListingSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.listingSource = [...data]
      })

    this._store
      .select(getListingSiteLoaders)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.listingSourceLoading = data?.listingSiteSources
      })
  }

  openModal(component: any, data: any) {
    this.modalRef = this.modalService.show(component, {
      ...data
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
