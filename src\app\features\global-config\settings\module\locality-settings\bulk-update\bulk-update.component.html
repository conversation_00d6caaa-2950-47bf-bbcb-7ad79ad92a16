<div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
        <h3>{{ 'LEADS.bulk' | translate }} Update</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="px-12">
        <div class="field-label mb-10">Selected {{entityData?.moduleName}}</div>
        <div class="flex-column scrollbar max-h-100-250"> <!-- need to change mobile resp -->
            <ng-container *ngFor="let item of entityData?.name">
                <div class="p-12 bg-white border-bottom flex-between">
                    <div class="fw-600 text-sm  text-secondary">{{ item }} </div>
                    <div (click)="deleteItem(i)" class="bg-light-red icon-badge"><span
                            class="icon ic-cancel m-auto ic-xx-xs"></span></div>
                </div>
            </ng-container>
        </div>
        <ng-container *ngIf="entityData?.updateItem == 'city'">
            <div class="field-label">{{'GLOBAL.select' | translate}}
                {{'LOCATION.city'|translate }}</div>
            <ng-select [virtualScroll]="true" [items]="entityData?.updateData" [(ngModel)]="selectedCity"
                ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="name" bindValue="id">
            </ng-select>
        </ng-container>
        <ng-container *ngIf="entityData?.updateItem == 'zone'">
            <div class="field-label">{{'GLOBAL.select' | translate}}
                {{'LOCATION.zone'|translate }}</div>
            <ng-select [virtualScroll]="true" [items]="entityData?.updateData" [(ngModel)]="selectedZone"
                ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="name" bindValue="id">
            </ng-select>
        </ng-container>
        <div class="flex-center mt-20">
            <button class="btn-gray" (click)="modalRef.hide()">{{'BUTTONS.cancel'| translate }}</button>
            <button class="btn-coal ml-20" (click)="bulkUpdate()">{{'BUTTONS.save'| translate }}</button>
        </div>
    </div>
</div>