import { Component, EventEmitter, OnDestroy, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AppState } from 'src/app/app.reducer';
import {
  BulkUpdateCity,
  BulkUpdateZone,
} from 'src/app/reducers/site/site.actions';

@Component({
  selector: 'bulk-update',
  templateUrl: './bulk-update.component.html',
})
export class BulkUpdateComponent implements OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Output() saveClicked: EventEmitter<void> = new EventEmitter<void>();
  entityData: any;
  selectedZone: any;
  selectedCity: any;

  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) { }


  bulkUpdate() {
    let payload: any = {
      ids: this.entityData?.id,
    };

    if (this.entityData?.updateItem == 'zone' && this.selectedZone) {
      payload = {
        ...payload,
        zoneId: this.selectedZone,
      };
      this.store.dispatch(new BulkUpdateZone(payload));
      this.selectedZone = null;
    } else if (this.entityData?.updateItem == 'city' && this.selectedCity) {
      payload = {
        ...payload,
        cityId: this.selectedCity,
      };
      this.store.dispatch(new BulkUpdateCity(payload));
      this.selectedCity = null;
    }
    this.saveClicked.emit();
    this.modalRef.hide();
  }
  deleteItem(i: number) {
    this.entityData.name.splice(i, 1);
    this.entityData.id.splice(i,1);
    if(!this.entityData.name.length && !this.entityData.id.length){
      this.modalRef.hide();
      this.saveClicked.emit();
    }
}


  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
