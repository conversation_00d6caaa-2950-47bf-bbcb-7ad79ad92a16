<div class="p-30">
  <div class="flex-center-col">
    <div class="header-3 text-coal mb-20">Importing localities becomes more easier</div>
    <img src="../../../../assets/images/file-upload.svg">
    <div class="fw-semi-bold text-large text-black-100 mt-2">Upload your localities</div>
  </div>
  <ng-container *ngIf="currentStep == 1 || currentStep == 2">
    <div class="mt-20 p-20 bg-white br-4">
      <div class="fw-700 header-3 text-black-100">{{'BULK_LEAD.importing-description' | translate }}</div>
      <div class="d-flex mt-20">
        <span class="dot dot-xxs bg-slate-250 mr-8 mt-6"></span>
        <span class="text-nowrap fw-700 text-dark-gray text-large mr-10">step 1 :</span>
        <a [href]="locationTemplatePath" class="d-flex">
          <div class="border-accent-green br-50 mr-4">
            <span class="icon ic-down-to-line ic-accent-green ic-x-xs m-4"></span>
          </div>
          <span class="fw-700 text-accent-green text-large text-decoration-underline">Download template</span>
        </a>
      </div>
      <div class="fw-semi-bold text-sm text-dark-gray mt-6 ml-60">
        {{'BULK_LEAD.download-description' | translate}}</div>
      <div class="border-bottom-slate-20 mt-12 ml-60"></div>
      <div class="d-flex mt-12">
        <span class="dot dot-xxs bg-slate-250 mr-8 mt-6"></span>
        <span class="text-nowrap fw-700 text-dark-gray text-large mr-10">step 2 :</span>
        <div>
          <div class="fw-700 text-black-200 text-large">{{'BULK_LEAD.prepare-import-file'| translate }}</div>
          <div class="fw-semi-bold text-sm text-dark-gray mt-6">change the dummy data in the sample file to your
            localities details</div>
        </div>
      </div>
      <div class="border-bottom-slate-20 mt-12 ml-60"></div>
      <div class="d-flex mt-12">
        <span class="dot dot-xxs bg-slate-250 mr-8 mt-6"></span>
        <span class="text-nowrap fw-700 text-dark-gray text-large mr-10">step 3 :</span>
        <div>
          <div class="fw-700 text-black-200 text-large">{{'BULK_LEAD.upload-your-file'| translate }}</div>
          <div class="fw-semi-bold text-sm text-dark-gray mt-6">{{'BULK_LEAD.upload-description' | translate
            }}</div>
        </div>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="currentStep == 1">
    <div class="version-two">
      <browse-drop-upload [allowedFileType]="'excel'" [isExcelFile]="true"
        (uploadedFile)="onFileSelection($event)"></browse-drop-upload>
    </div>
  </ng-container>
  <ng-container *ngIf="currentStep == 2">
    <div class="bg-white px-20 py-40">
      <div class="border-green-dashed-2 flex-center-col bg-green-150">
        <div class="align-center-col py-40 text-black-200">
          <div class="fw-semi-bold header-4 text-center">{{'BULK_LEAD.successfully-upload' | translate }}</div>
          <div class="mt-4 fw-700 header-3 text-truncate-1 break-all">{{selectedFile?.name}}</div>
          <a class="align-center fw-600 text-large mt-10 text-lowercase">
            <div class="text-red-450 mr-10 text-decoration-underline" (click)="currentStep = 1">{{'BUTTONS.delete' |
              translate }}</div>
            <div class="text-aqua-750 text-decoration-underline" (click)="replaceFile()">{{'BUTTONS.replace' | translate
              }}</div>
          </a>
          <input type="file" #fileInput (change)="onFileSelection($event.target.files[0])" />
        </div>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="currentStep == 1 || currentStep == 2">
    <div class="border-bottom-slate-20"></div>
    <div class="flex-end p-10 bg-white">
      <a class="fw-600 text-large text-black-200 text-decoration-underline mr-20"
        routerLink="/global-config/locality-settings">
        {{'BUTTONS.cancel' | translate}} Import</a>
      <span [ngClass]="currentStep == 1 ? 'btn-gray pe-none' : 'btn-green'" (click)="uploadFile()">{{'BUTTONS.proceed' |
        translate }}</span>
    </div>
  </ng-container>
</div>