import { Component, ElementRef, EventEmitter, On<PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { ALLOWED_EXCEL_FILE_TYPES, LOCATION_EXCEL_TEMPLATE } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { LocationExcelUpload } from 'src/app/reducers/site/site.actions';
import { getLocationExcel } from 'src/app/reducers/site/site.reducer';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'bulk-upload',
  templateUrl: './bulk-upload.component.html',
})
export class BulkUploadComponent implements  OnD<PERSON>roy {
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  currentStep: number = 1;
  locationTemplatePath: string = LOCATION_EXCEL_TEMPLATE;
  selectedFile: File;
  allowedFileTypes: string = ALLOWED_EXCEL_FILE_TYPES;
  s3ImageBucket: string = environment.s3ImageBucketURL;

  constructor(private router: Router,
    private store: Store<AppState>) { }



  onFileSelection(file: File) {
    this.selectedFile = file;
    this.currentStep = this.currentStep < 2 ? this.currentStep + 1 : this.currentStep;
  }


  uploadFile() {
    this.store.dispatch(new LocationExcelUpload(this.selectedFile));
    this.router.navigate(['global-config/locality-settings']);
    this.store
      .select(getLocationExcel)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          this.downloadExcelFile(this.s3ImageBucket + res);
        }
      });
  }

  replaceFile() {
    this.fileInput.nativeElement.click();
  }

  downloadExcelFile(url: string): void {
    const link = document.createElement('a');
    link.href = url;
    link.download = 'file.xlsx';
    link.target = '_blank';
    link.click();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
