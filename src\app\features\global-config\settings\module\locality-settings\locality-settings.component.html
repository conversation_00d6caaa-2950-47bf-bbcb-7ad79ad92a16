<div routerLink='/global-config' [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
    class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<div class="p-24">
    <div class="bg-white px-20 py-16 flex-between br-6">
        <div>
            <h5 class="fw-600 text-coal">Turn ON / OFF the Google Map API Service</h5>
            <h6 class="text-dark-gray pt-4">this will be used in leads, properties and projects locations options.</h6>
        </div>
        <div class="align-center">
            <div class="mr-8">{{isGoogleMapApi == true ? 'on' : 'off'}}</div>
            <input type="checkbox" id="chkGoogleApi" name="googleApi" class="toggle-switch toggle-active-sold"
                [ngClass]="{'pe-none' : !canUpdate}" (click)="googleMapApi()" [(ngModel)]="isGoogleMapApi">
            <label for="chkGoogleApi" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
        </div>
    </div>
    <div class="mt-20">
        <div class="flex-between w-100 ph-flex-col ph-flex-start">
            <div class="border br-20 bg-white align-center user">
                <!-- <div class="activation ip-d-flex" [ngClass]="{'active' : selectedSection == 'Country'}"
                    (click)="selectedSection = 'Country'">
                    <span class="icon ic-earth ic-sm mr-8"
                        [ngClass]="{'active' : selectedSection !== 'Country'}"></span>
                    {{'LOCATION.country'| translate }}
                </div> -->

                <!-- <div class="activation ip-d-flex" [ngClass]="{'active' : selectedSection == 'State'}"
                    (click)="selectedSection = 'State'">
                    <span class="icon ic-map-location ic-sm mr-8"
                        [ngClass]="{'active' : selectedSection !== 'State'}"></span>
                    {{'LOCATION.state'| translate }}
                </div> -->

                <div class="activation ip-d-flex" [ngClass]="{'active' : selectedSection == 'City'}"
                    (click)="selectedSection = 'City'">
                    <span class="icon ic-location-map ic-sm mr-8"
                        [ngClass]="{'active' : selectedSection !== 'City'}"></span>
                    {{'LOCATION.city'| translate }}
                </div>

                <div class="activation ip-d-flex" [ngClass]="{'active' : selectedSection == 'Zone'}"
                    (click)="selectedSection = 'Zone'">
                    <span class="icon ic-compass ic-sm mr-8" [ngClass]="{'active' : selectedSection !== 'Zone'}"></span>
                    {{'LOCATION.zone'| translate }}
                </div>

                <div class="activation ip-d-flex" [ngClass]="{'active' : selectedSection == 'Locality'}"
                    (click)="selectedSection = 'Locality'">
                    <span class="icon ic-location-solid ic-sm mr-8"
                        [ngClass]="{'active' : selectedSection !== 'Locality'}"></span>
                    {{'LOCATION.locality'| translate }}
                </div>
            </div>
            <!-- state  -->
            <div *ngIf="selectedSection == 'State'" (click)="openAddState()" class="btn-coal ph-mt-10">
                <span class="icon ic-add ic-sm mr-6 ic-white"></span>
                <span class="text-white fw-semi-bold text-large">{{'SIDEBAR.add' | translate }}
                    {{'LOCATION.state' | translate }}</span>
            </div>
            <!-- city  -->
            <div *ngIf="selectedSection == 'City'" (click)="openAddCity()" class="btn-coal ph-mt-10">
                <span class="icon ic-add ic-xxs mr-8"></span>{{'SIDEBAR.add' | translate }} New
                {{'LOCATION.city' | translate }}
            </div>
            <div *ngIf="selectedSection == 'Zone'" (click)="openAddZone()" class="btn-coal ph-mt-10">
                <span class="icon ic-add ic-xxs mr-8"></span>{{'SIDEBAR.add' | translate }}
                {{'GLOBAL.new' | translate }} {{'LOCATION.zone'|translate }}
            </div>
            <div class="d-flex ph-mt-10" *ngIf="selectedSection == 'Locality'">
                <div class="btn-left-dropdown" (click)="openAddLocality()">
                    <span class="icon ic-add ic-xxs"></span>
                    <span class="ml-8 ip-d-none">{{'SIDEBAR.add' | translate }} New
                        {{'LOCATION.locality' | translate }}</span>
                </div>
                <span class="btn-right-dropdown btn-w-30 black-100">
                    <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
                        [(ngModel)]="selectedOption" ResizableDropdown class="bg-black-200 dropdown-black"
                        (click)="navigateToBulkUpload()">
                        <ng-option (click)="selectedOption = null" value="bulkUpload">
                            <span class="ic-upload icon ic-xxs mr-8"></span>
                            <span class="text-white"> {{ 'LEADS.bulk' | translate }}
                                {{ 'LEADS.upload' | translate }}</span></ng-option>
                    </ng-select>
                </span>
            </div>
            <!-- Country -->
            <div *ngIf="selectedSection == 'Country'" (click)="openAddCountry()" class="btn-coal w-140 ph-mt-10">
                <span class="icon ic-add ic-sm mr-6 ic-white"></span>
                <span class="text-white fw-semi-bold text-large">{{'SIDEBAR.add' | translate }}
                    {{'GLOBAL.new' | translate }} {{'LOCATION.country'|translate }}</span>
            </div>
        </div>
        <div class="mt-20">
            <ng-container *ngIf="selectedSection == 'City'">
                <manage-city [allUserList]="allUserList"></manage-city>
            </ng-container>
            <ng-container *ngIf="selectedSection == 'State'">
                <manage-state [allUserList]="allUserList"></manage-state>
            </ng-container>
            <ng-container *ngIf="selectedSection == 'Country'">
                <manage-country [allUserList]="allUserList"></manage-country>
            </ng-container>
            <ng-container *ngIf="selectedSection == 'Zone'">
                <manage-zone [allUserList]="allUserList"></manage-zone>
            </ng-container>
            <ng-container *ngIf="selectedSection == 'Locality'">
                <manage-locality [allUserList]="allUserList"></manage-locality>
            </ng-container>
        </div>
    </div>
</div>