import { Component, <PERSON><PERSON><PERSON>ter, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs/operators';

import { AppState } from 'src/app/app.reducer';
import { AddCityComponent } from 'src/app/features/global-config/settings/module/locality-settings/manage-city/add-city/add-city.component';
import { AddLocalityComponent } from 'src/app/features/global-config/settings/module/locality-settings/manage-locality/add-locality/add-locality.component';
import { AddZoneComponent } from 'src/app/features/global-config/settings/module/locality-settings/manage-zone/add-zone/add-zone.component';
import { FetchPriorityList } from 'src/app/reducers/automation/automation.actions';
import { UpdateGlobalSettings } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchLocationList,
  FetchZoneList,
} from 'src/app/reducers/site/site.actions';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { BaseGridComponent } from 'src/app/shared/components/base-grid/base-grid.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { AddCountryComponent } from './manage-country/add-country/add-country.component';
import { AddStateComponent } from './manage-state/add-state/add-state.component';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'locality-settings',
  templateUrl: './locality-settings.component.html',
})
export class LocalitySettingsComponent
  extends BaseGridComponent
  implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  selectedSection: string = 'City';
  allUserList: any;
  selectedOption: string;
  canUpdate: boolean;
  isGoogleMapApi: boolean;
  globalSettingsData: any;
  showLeftNav: boolean;

  constructor(
    private headerTitle: HeaderTitleService,
    private store: Store<AppState>,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private shareDataService: ShareDataService,
    private router: Router,
    public metaTitle: Title
  ) {
    super();
    this.metaTitle.setTitle('CRM | Settings');
    this.headerTitle.setLangTitle('Manage Locations');
    this.store.dispatch(new FetchUsersListForReassignment());
    this.store.dispatch(new FetchZoneList());
    this.store.dispatch(new FetchLocationList());
    this.store.dispatch(new FetchPriorityList());
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.GlobalSettings.Update'))
          this.canUpdate = true;
      });
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
        this.isGoogleMapApi = data?.isGoogleMapLocationEnabled;
      });
  }
  ngOnInit(): void {
    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
      });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
  }

  openAddState() {
    this.modalService.show(
      AddStateComponent,
      Object.assign(
        {},
        {
          class: 'top-modal modal-400 ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  openAddCity() {
    this.modalService.show(
      AddCityComponent,
      Object.assign(
        {},
        {
          class: 'top-modal modal-600 ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  openAddZone() {
    this.modalService.show(
      AddZoneComponent,
      Object.assign(
        {},
        {
          class: 'top-modal modal-600 ip-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  openAddLocality() {
    this.modalService.show(
      AddLocalityComponent,
      Object.assign(
        {},
        {
          class: 'top-modal modal-600 ip-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  openAddCountry() {
    this.modalService.show(
      AddCountryComponent,
      Object.assign(
        {},
        {
          class: 'top-modal modal-400 ip-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  navigateToBulkUpload() {
    if (this.selectedOption === 'bulkUpload') {
      this.router.navigate(['global-config/bulk-upload-locality']);
    }
    this.selectedOption = '';
  }

  googleMapApi() {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: this.isGoogleMapApi ? 'disable' : 'enable',
      title: 'Google Map API Service',
      fieldType: 'option',
    };
    if (this.modalService.getModalsCount() === 0) {
      this.modalRef = this.modalService.show(
        UserConfirmationComponent,
        Object.assign(
          {},
          {
            class: 'modal-350 top-modal ph-modal-unset',
            initialState,
          }
        )
      );
    }
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        let payload: any = {
          ...this.globalSettingsData,
          isGoogleMapLocationEnabled: this.isGoogleMapApi
        };
        this.modalRef.hide();
        if (reason == 'confirmed') {
          this.store.dispatch(new UpdateGlobalSettings(payload));
        } else {
          this.isGoogleMapApi = this.globalSettingsData?.isGoogleMapLocationEnabled;
        }
      });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
