import { Component, EventEmitter, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAssignedToDetails,
} from 'src/app/core/utils/common.util';
import {
  FetchUserAssignmentByEntity,
  UpdateMultiUserAssignment,
  UpdateUserAssignment,
} from 'src/app/reducers/automation/automation.actions';
import {
  getPriorityList,
  getUserAssignmentByEntity,
} from 'src/app/reducers/automation/automation.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'location-user-assignment',
  templateUrl: './location-user-assignment.component.html',
})
export class LocationUserAssignmentComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Output() saveClicked: EventEmitter<void> = new EventEmitter<void>();

  assignedUser: FormControl = new FormControl('');
  assignedUserDetails: Array<string> = [];
  getAssignedToDetails = getAssignedToDetails;

  moduleId: string;
  canAssignToAny: boolean;
  allActiveUsers: any;
  allUserList: any;
  userList: any;
  activeUsers: any;
  entityData: any;
  constructor(
    private store: Store<AppState>,
    public modalService: BsModalService,
    private modalRef: BsModalRef
  ) {
    this.assignedUser.valueChanges.subscribe((data: any) => {
      this.assignedUserDetails = data;
    });
  }

  ngOnInit(): void {
    if (!Array.isArray(this.entityData?.name)) {
      this.entityData.name = [this.entityData?.name];
      this.entityData.id = [this.entityData.id];
    }
    if (!this.entityData?.multipleAssign && this.entityData?.id) {
      this.store.dispatch(new FetchUserAssignmentByEntity(this.entityData?.id));
    }

    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter(
          (item) => item.name === this.entityData?.module
        );
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });

    this.store
      .select(getUserAssignmentByEntity)
      .pipe(takeUntil(this.stopper))
      .subscribe((res) => {
        if (!this.entityData?.multipleAssign) {
          this.assignedUser.patchValue(res?.userIds);
          this.assignedUserDetails = res?.userIds;
        }
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
          this.store.dispatch(new FetchUsersListForReassignment());
        } else {
          this.store.dispatch(new FetchAdminsAndReportees());
        }
      });

    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      });
  }

  userAssignment() {
    let payload: any = {
      userIds: this.assignedUser.value,
      moduleId: this.moduleId,
    };
    if (this.entityData?.multipleAssign) {
      payload = {
        ...payload,
        entityIds: this.entityData.id,
      };
      this.store.dispatch(new UpdateMultiUserAssignment(payload, this.entityData?.module));
    } else {
      payload = {
        ...payload,
        entityId: this.entityData.id[0],
      };
      this.store.dispatch(new UpdateUserAssignment(payload, this.entityData?.module));
    }
    this.saveClicked.emit();
    this.modalRef.hide();
  }
  deleteItem(i: number) {
    this.entityData.name.splice(i, 1);
    this.entityData.id.splice(i, 1);
    if (!this.entityData.name.length && !this.entityData.id.length) {
      this.modalRef.hide();
      this.saveClicked.emit();
    }
  }


  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
