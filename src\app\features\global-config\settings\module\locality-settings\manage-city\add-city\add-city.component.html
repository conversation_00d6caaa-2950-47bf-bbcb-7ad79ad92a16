<h5 class="text-white fw-600 bg-black px-20 py-12">
    {{ (selectedCity ? 'GLOBAL.edit' : 'SIDEBAR.add') | translate }}
    {{'LOCATION.city' | translate}}
</h5>
<form [formGroup]="addCityForm" autocomplete="off" class="pb-20 px-30">
    <div class="d-flex flex-wrap w-100 ph-flex-col">
        <!-- <div class="w-50 ph-w-100">
            <div class="mr-20 ph-mr-0">
                <div class="field-label">
                    {{'GLOBAL.select' | translate}} {{'LOCATION.country' | translate }}
                </div>
                <form-errors-wrapper>
                    <ng-select [virtualScroll]="true" [items]="countryList" formControlName="country" ResizableDropdown
                        placeholder="{{'GLOBAL.select' | translate}}" bindLabel="name" bindValue="id">
                    </ng-select>
                </form-errors-wrapper>
            </div>
        </div> -->

        <!-- <div class="w-50 ph-w-100">
            <div class="mr-20 ph-mr-0">
                <div class="field-label">
                    {{'GLOBAL.select' | translate}} {{'LOCATION.state' | translate }}
                </div>
                <form-errors-wrapper>
                    <ng-select [virtualScroll]="true" [items]="stateList" formControlName="state"
                        placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown bindLabel="name" bindValue="id">
                    </ng-select>
                </form-errors-wrapper>
            </div>
        </div> -->

        <div class="w-50 ph-w-100">
            <div class="mr-20 ph-mr-0">
                <div class="field-label-req">
                    {{'LOCATION.city' | translate}} {{'GLOBAL.name' | translate }}
                </div>
                <form-errors-wrapper [control]="addCityForm.controls['name']"
                    label="{{'LOCATION.city' | translate}} {{'GLOBAL.name' | translate }}">
                    <input type="text" required formControlName="name" placeholder="ex. Bengaluru"
                        (keyup.enter)="focusable.click()" tabindex="1">
                </form-errors-wrapper>
            </div>
        </div>
    </div>

    <div class="flex-end mt-30">
        <button class="btn-gray mr-20" id="addLocalityCancel" data-automate-id="addLocalityCancel"
            (click)="modalRef.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
        <button #focusable class="btn-coal" id="addCity" data-automate-id="addCity" (click)="addCity()">
            {{ (selectedCity ? 'save' : 'SIDEBAR.add') | translate }}
        </button>
    </div>
</form>
