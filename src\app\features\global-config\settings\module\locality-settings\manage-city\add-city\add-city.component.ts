import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { AddCity, FetchCountry, FetchState, UpdateCity } from 'src/app/reducers/site/site.actions';
import { getCountryList, getStateList } from 'src/app/reducers/site/site.reducer';

@Component({
  selector: 'add-city',
  templateUrl: './add-city.component.html',
})
export class AddCityComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  addCityForm: FormGroup;
  selectedCity: any;
  countryList: any[];
  stateList: any[];

  constructor(
    private formBuilder: FormBuilder,
    private store: Store<AppState>,
    public modalRef: BsModalRef
  ) {
    // this.store.dispatch(new FetchCountry());
    // this.store.dispatch(new FetchState());
    this.addCityForm = this.formBuilder.group({
      country: [null],
      state: [null],  
      name: [null, Validators.required]
    });
  }

  ngOnInit(): void {
    if (this.selectedCity) {
      this.addCityForm.patchValue({
        country: this.selectedCity.state.country?.id,
        state: this.selectedCity.state?.id,
        name: this.selectedCity.name
      });
    }

    this.store
      .select(getCountryList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data?.items?.slice()
        .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this.store
      .select(getStateList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.stateList = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });
  }

  addCity() {
    if (!this.addCityForm.valid) {
      validateAllFormFields(this.addCityForm);
      return;
    }

    const formValue = this.addCityForm.value;

    const addCityPayload = {
      name: formValue.name,
      state: this.stateList?.find(s => s.id === formValue.state)?.name || '',
      country: this.countryList?.find(c => c.id === formValue.country)?.name || '',
      stateId: formValue.state,
      countryId: formValue.country
    };

    if (this.selectedCity) {
      this.store.dispatch(new UpdateCity(this.selectedCity.id, addCityPayload));
    } else {
      this.store.dispatch(new AddCity(addCityPayload));
    }
    this.modalRef.hide();
  }
}
