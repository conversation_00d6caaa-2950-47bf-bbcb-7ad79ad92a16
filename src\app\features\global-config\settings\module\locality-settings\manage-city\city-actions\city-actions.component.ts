import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { DeleteCity } from 'src/app/reducers/site/site.actions';
import { LocationUserAssignmentComponent } from '../../location-user-assignment/location-user-assignment.component';
import { AddCityComponent } from '../add-city/add-city.component';

@Component({
  selector: 'city-actions',
  templateUrl: './city-actions.component.html',
})
export class CityActionsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canUpdate: boolean;
  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) { }

  ngOnInit(): void {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('GlobalSettings')) {
          this.canUpdate = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  editCity(city: any): void {
    const initialState = {
      selectedCity: city,
    };
    this.modalRef = this.modalService.show(AddCityComponent, {
      class: 'modal-600 top-modal ip-modal-unset',
      initialState,
    });
  }

  deleteCity(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.name,
      fieldType: 'city',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteCity(data?.id));
        }
      });
    }
  }

  openAssignmentModal() {
    let initialState: any = {
      entityData: {
        ...this.params?.data,
        module: 'City',
      },
    };
    this.modalService.show(
      LocationUserAssignmentComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-400 ph-modal-unset', initialState }
      )
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
