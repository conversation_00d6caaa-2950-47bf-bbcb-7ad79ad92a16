import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, takeUntil } from 'rxjs';
import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getAssignedToDetails,
  getPages,
  onFilterChanged,
} from 'src/app/core/utils/common.util';
import {
  DeleteCities,
  FetchCityList,
  UpdateCityFiltersPayload,
} from 'src/app/reducers/site/site.actions';
import {
  getCityFiltersPayload,
  getCityList,
} from 'src/app/reducers/site/site.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { LocationUserAssignmentComponent } from '../location-user-assignment/location-user-assignment.component';
import { CityActionsComponent } from './city-actions/city-actions.component';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';

@Component({
  selector: 'manage-city',
  templateUrl: './manage-city.component.html',
})
export class ManageCityComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();

  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: 10,
    path: 'site/city'
  };
  appliedFilter: any;

  gridApi: any;
  gridColumnApi: any;
  gridOptions: any;
  defaultColDef: any;

  allCityData: any;
  @Input() allUserList: any;
  getPages = getPages;
  onFilterChanged = onFilterChanged;
  canBulkAssignment: boolean = false;
  canBulkDelete: boolean = false;

  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
  }

  ngOnInit(): void {
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canBulkAssignment = permissionsSet.has(
          'Permissions.GlobalSettings.BulkAssignment'
        );
        this.canBulkDelete = permissionsSet.has(
          'Permissions.GlobalSettings.BulkDelete'
        );
        this.cityFilterFunction();
        this.cityGridSettings();
      });
    this.store
      .select(getCityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allCityData = data;
      });

    this.store
      .select(getCityFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.currOffset = this.filtersPayload?.pageNumber - 1;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          SearchText: this.filtersPayload?.SearchText,
        };
      });
    this.searchTermSubject.subscribe(() => {
      this.cityFilterFunction();
    });
  }

  cityGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'City Name',
        field: 'City Name',
        valueGetter: (params: any) => [params.data?.name],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Assigned To',
        field: 'Assigned To',
        valueGetter: (params: any) => [
          params.data?.userAssignment?.userIds
            ? params.data.userAssignment.userIds?.map(
              (id: any) =>
                getAssignedToDetails(id, this.allUserList, true) || ''
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Actions',
        maxWidth: 110,
        filter: false,
        cellRenderer: CityActionsComponent,
      },
    ];
    if (this.canBulkAssignment || this.canBulkDelete) {
      this.gridOptions.columnDefs.unshift({
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        maxWidth: 50,
      });
    }
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.store.dispatch(new UpdateCityFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchCityList());
    this.currOffset = 0;
  }

  cityFilterFunction() {
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: 1,
      pageSize: this.pageSize,
      SearchText: this.searchTerm,
    };
    this.store.dispatch(new UpdateCityFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchCityList());
    this.currOffset = 0;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.store.dispatch(new UpdateCityFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchCityList());
  }

  deselectOptions() {
    let selectedNodes = this.gridApi?.getSelectedNodes();
    selectedNodes.forEach((node: any) => node.setSelected(false));
  }

  deleteCities() {
    let selectedIds: any;
    if (this.gridApi) {
      let selectedNodes = this.gridApi?.getSelectedNodes();
      selectedIds = selectedNodes?.map((node: any) => node.data?.id);
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      fieldType: 'these cities',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    let payload = {
      ids: selectedIds,
    };
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteCities(payload));
        }
      });
    }
  }

  userAssignment() {
    let selectedNodesNames: any;
    let selectedIds: any;

    if (this.gridApi) {
      let selectedNodes = this.gridApi?.getSelectedNodes();
      selectedNodesNames = selectedNodes?.map((node: any) => node.data?.name);
      selectedIds = selectedNodes?.map((node: any) => node.data?.id);
    }
    let initialState: any = {
      entityData: {
        name: selectedNodesNames,
        id: selectedIds,
        module: 'City',
        moduleName: 'Cities',
        multipleAssign: true,
      },
    };
    this.modalRef = this.modalService.show(
      LocationUserAssignmentComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-400 ph-modal-unset', initialState }
      )
    );
    if (this.modalRef.content) {
      this.modalRef.content.saveClicked.subscribe(() => {
        this.deselectOptions();
      });
    }
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
