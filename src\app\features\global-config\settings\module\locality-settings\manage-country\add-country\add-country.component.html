<h5 class="text-white fw-600 bg-black px-20 py-12">{{ (selectedCountry ? 'GLOBAL.edit' : 'SIDEBAR.add' ) | translate }}
    {{'LOCATION.country' | translate}}</h5>
<form [formGroup]="addStateForm" autocomplete="off" class="pb-20 px-30">
    <div class="field-label-req">
        {{'LOCATION.country' | translate}} {{'GLOBAL.name'|translate }}</div>
    <form-errors-wrapper [control]="addStateForm.controls['country']"
        label="{{'LOCATION.country' | translate}} {{'GLOBAL.name' | translate }}">
        <input type="text" required formControlName="country" placeholder="ex. Bengaluru" (keyup.enter)="focusable.click()"
            tabindex="1">
    </form-errors-wrapper>
    <div class="flex-end mt-30">
        <button class="btn-no mr-20" id="addLocalityCancel" data-automate-id="addLocalityCancel"
            (click)="modalRef.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
        <button #focusable class="btn-coal" id="addcountry" data-automate-id="addcountry" (click)="addCountry()">
            {{ (selectedCountry ? 'save' : 'SIDEBAR.add' ) | translate }}</button>
    </div>
</form>