import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { AddCountry, UpdateCountry } from 'src/app/reducers/site/site.actions';

@Component({
  selector: 'add-country',
  templateUrl: './add-country.component.html',
})
export class AddCountryComponent implements OnInit {

  addStateForm: FormGroup;
  selectedCountry: any;

  constructor(
    private formBuilder: FormBuilder,
    private store: Store<AppState>,
    public modalRef: BsModalRef
  ) {
    this.addStateForm = this.formBuilder.group({
      country: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    if (this.selectedCountry) {
      this.addStateForm.patchValue({
        country: this.selectedCountry.name,
      });
    }
  }

  addCountry() {
    if (!this.addStateForm.valid) {
      validateAllFormFields(this.addStateForm);
      return;
    }

    const countryData = this.selectedCountry
      ? { id: this.selectedCountry.id, name: this.addStateForm.value.country }
      : { country: this.addStateForm.value.country };

    if (this.selectedCountry) {
      this.store.dispatch(new UpdateCountry(this.selectedCountry.id, countryData));
    } else {
      this.store.dispatch(new AddCountry(countryData));
    }

    this.modalRef.hide();
  }
}