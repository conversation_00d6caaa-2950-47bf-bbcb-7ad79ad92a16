import { Component, EventEmitter, OnInit } from '@angular/core';
import { LocationUserAssignmentComponent } from '../../location-user-assignment/location-user-assignment.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { AddCountryComponent } from '../add-country/add-country.component';
import { AppState } from 'src/app/app.reducer';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { takeUntil } from 'rxjs';
import { DeleteCountry } from 'src/app/reducers/site/site.actions';

@Component({
  selector: 'country-actions',
  templateUrl: './country-actions.component.html',
})
export class CountryActionsComponent implements OnInit {

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canUpdate: boolean;
  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) { }

  ngOnInit(): void {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('GlobalSettings')) {
          this.canUpdate = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  editCountry(country: any): void {
    const initialState = {
      selectedCountry: country,
    };
    this.modalRef = this.modalService.show(AddCountryComponent, {
      class: 'modal-600 top-modal ip-modal-unset',
      initialState,
    });
  }

  deleteCountry(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.name,
      fieldType: 'country',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteCountry(data?.id));
        }
      });
    }
  }

  openAssignmentModal() {
    let initialState: any = {
      entityData: {
        ...this.params?.data,
        module: 'Country',
      },
    };
    this.modalService.show(
      LocationUserAssignmentComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-400 ph-modal-unset', initialState }
      )
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
