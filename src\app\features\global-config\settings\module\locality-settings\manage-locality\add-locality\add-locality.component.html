<h5 class="text-white fw-600 bg-black px-20 py-12">{{ (selectedLocation ? 'GLOBAL.edit' : 'SIDEBAR.add' ) | translate }}
    {{'LOCATION.locality' | translate}}</h5>
<form [formGroup]="addLocationForm" autocomplete="off" class="pb-20 px-30">
    <div class="d-flex flex-wrap w-100 ph-flex-col">
        <!-- <div class="w-50 ph-w-100">
            <div class="mr-20 ph-mr-0">
                <div class="field-label">
                    {{'GLOBAL.select' | translate}} {{'LOCATION.country'|translate }}
                </div>
                <form-errors-wrapper>
                    <input type="text" formControlName="country" placeholder="country" >
                </form-errors-wrapper>
            </div>
        </div> -->

        <!-- <div class="w-50 ph-w-100">
            <div class="ph-mr-0 mr-20 ph-mt-20">
                <div class="field-label">
                    {{'GLOBAL.select' | translate}} {{'LOCATION.state'|translate }}
                </div>
                <form-errors-wrapper>
                    <input type="text" formControlName="state" placeholder="state" />
                </form-errors-wrapper>
            </div>
        </div> -->

        <div class="w-50 ph-w-100">
            <div class="mr-20 ph-mr-0">
                <div class="field-label">{{'GLOBAL.select' | translate}}
                    {{'LOCATION.city'|translate }}</div>
                <form-errors-wrapper>
                    <ng-select (change)="onSelectCity($event)" [virtualScroll]="true" [items]="allCityData"
                        formControlName="cityId" ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                        bindLabel="name" bindValue="id" (change)="updateZoneList(); removeZone()">
                    </ng-select>
                </form-errors-wrapper>
            </div>
        </div>
        <div class="w-50 ph-w-100">
            <div class="ph-mt-20 mr-20 ph-ml-0">
                <div class="field-label">{{'GLOBAL.select' | translate}}
                    {{'LOCATION.zone'|translate }}</div>
                <form-errors-wrapper>
                    <ng-select [virtualScroll]="true" [items]="zoneList" formControlName="zoneId"
                        placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown bindLabel="name" bindValue="id"
                        [readonly]="addLocationForm.controls['cityId']?.value ? false : true">
                    </ng-select>
                </form-errors-wrapper>
            </div>
        </div>

        <div class="w-50 ph-w-100">
            <div class="mr-20 ph-mr-0">
                <div class="field-label-req">
                    {{'LOCATION.locality' | translate}} {{'GLOBAL.name'|translate }}</div>
                <form-errors-wrapper [control]="addLocationForm.controls['locality']"
                    label="{{'LOCATION.locality' | translate}} {{'GLOBAL.name' | translate }}">
                    <div class="field-tag">
                        <ng-select [virtualScroll]="true" formControlName="locality"
                            (search)="searchPlaceTerm$.next($event.term)" ResizableDropdown
                            placeholder="Search for Location/Create Location" [addTag]="true" addTagText="Add"
                            class="bg-white">
                            <ng-option *ngFor="let places of placesList" [value]="places">
                                {{places.localityDisplayText}}{{places?.city && places?.localityDisplayText !==
                                places?.city ? ', ' + places.city : ''}}</ng-option>
                        </ng-select>
                        <div class="search icon ic-search ic-sm ic-coal"></div>
                    </div>
                </form-errors-wrapper>
            </div>
        </div>

        <!-- <div class="w-50 ph-w-100">
            <div class="ph-mt-20 mr-20 ph-ml-0">
                <div class="field-label">
                    {{'LOCATION.pinCode' | translate}} {{'GLOBAL.name'|translate }}
                </div>
                <form-errors-wrapper>
                    <input type="number" formControlName="pinCode" placeholder="ex. 560002" tabindex="1">
                </form-errors-wrapper>
            </div>
        </div> -->

    </div>
    <div class="flex-end mt-30">
        <button class="btn-gray mr-20" id="addLocalityCancel" data-automate-id="addLocalityCancel"
            (click)="modalRef.hide()">
            {{ 'BUTTONS.cancel' | translate }}</button>
        <button class="btn-coal" id="addLocality" data-automate-id="addLocality" (click)="addLocation()">
            {{ (selectedLocation ? 'save' : 'SIDEBAR.add' ) | translate }}</button>
    </div>
</form>