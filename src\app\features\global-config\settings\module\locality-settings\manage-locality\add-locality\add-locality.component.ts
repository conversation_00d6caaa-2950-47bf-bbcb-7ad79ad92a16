import { Component, EventEmitter, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import {
  BehaviorSubject,
  debounceTime,
  distinctUntilChanged,
  filter,
  takeUntil,
} from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { FetchPlacesList } from 'src/app/reducers/places/places.actions';
import {
  AddLocation,
  FetchAllCityList,
  FetchCountry,
  FetchState,
  UpdateLocation,
} from 'src/app/reducers/site/site.actions';
import {
  getAllCityList,
  getAllZoneList,
  getCountryList,
  getStateList,
} from 'src/app/reducers/site/site.reducer';

@Component({
  selector: 'add-locality',
  templateUrl: './add-locality.component.html',
})
export class AddLocalityComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  addLocationForm: FormGroup;
  selectedLocation: any;
  allCityData: any[];
  allZoneData: any[];
  zoneList: any[];
  placesList: Array<any>;
  stateList: any[];
  countryList: any[]

  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    private formBuilder: FormBuilder,
    private _notificationService: NotificationsService,
  ) {
    // this.store.dispatch(new FetchCountry());
    // this.store.dispatch(new FetchState())
    this.store.dispatch(new FetchAllCityList());
    this.addLocationForm = this.formBuilder.group({
      country: [null],
      locality: [null, Validators.required],
      cityId: [null],
      zoneId: [null],
      state: [null],
      pinCode: [null],
    });
  }

  ngOnInit(): void {
    this.store
      .select(getAllCityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allCityData = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this.store
      .select(getStateList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.stateList = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this.store
      .select(getCountryList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data?.items;
      });

    this.store
      .select(getAllZoneList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allZoneData = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
        this.zoneList = this.allZoneData;
      });
    if (this.selectedLocation) {
      this.addLocationForm.patchValue({
        ...this.selectedLocation,
        country: this.selectedLocation?.country?.name,
        state: this.selectedLocation?.state?.name,
        cityId: this.selectedLocation?.city?.id,
        zoneId: this.selectedLocation?.zone?.id,
        pinCode: this.selectedLocation?.postalCode,
      });
    }

    this.addLocationForm.controls['locality'].valueChanges.subscribe(
      (val: string) => {
        this.addLocationForm.patchValue({
          selectedPlace: this.placesList.filter(
            (place: any) => val == place.placeId
          ),
        });
      }
    );

    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => searchStr.length > 2)
      )
      .subscribe((searchStr: string) => {
        this.store.dispatch(new FetchPlacesList(searchStr));
      });

    this.store
      .select((state) => state.places)
      .pipe(takeUntil(this.stopper))
      .subscribe(
        (data: any) =>
        (this.placesList = data?.placesList.filter(
          (place: any, index: any, self: any) => {
            return (
              index ===
              self.findIndex((p: any) => p.placeId === place.placeId)
            );
          }
        ))
      );
    this.updateZoneList();
  }

  updateZoneList() {
    if (this.addLocationForm.value.cityId || this.selectedLocation?.city?.id) {
      this.zoneList = this.allZoneData.filter(
        (zone: any) => (this.addLocationForm.value.cityId || this.selectedLocation?.city?.id) == zone.city?.id
      );
    }
  }

  removeZone() {
    this.addLocationForm.patchValue({
      zoneId: null,
    });
  }

  addLocation() {
    if (!this.addLocationForm.valid) {
      validateAllFormFields(this.addLocationForm);
      return;
    }
    const locationData = this.addLocationForm.value;
    let addLocationForm = {
      ...(this.selectedLocation && {
        id: this.selectedLocation.id,
      }),
      ...locationData,
      locality: locationData?.locality?.label
        ? locationData.locality.label
        : this.selectedLocation?.locality ||
        locationData.locality?.localityDisplayText
        || null,
      placeId: locationData?.locality?.placeId
        ? locationData.locality.placeId
        : null,
    };
    if (this.selectedLocation) {
      this.store.dispatch(
        new UpdateLocation(this.selectedLocation.id, addLocationForm)
      );
    } else {
      this.store.dispatch(new AddLocation(addLocationForm));
    }
    this.modalRef.hide();
  }

  onSelectCity(data: any) {
    this.addLocationForm.patchValue({
      state: data ? data.state?.name : null,
      country: data ? data.state?.country?.name : null,
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
