import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { LocationUserAssignmentComponent } from '../../location-user-assignment/location-user-assignment.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { AppState } from 'src/app/app.reducer';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { takeUntil } from 'rxjs';
import { DeleteLocation } from 'src/app/reducers/site/site.actions';
import { AddLocalityComponent } from '../add-locality/add-locality.component';

@Component({
  selector: 'locality-actions',
  templateUrl: './locality-actions.component.html',
})
export class LocalityActionsComponent implements OnInit, OnD<PERSON>roy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canUpdate: boolean;
  constructor(private store: Store<AppState>, public modalRef: BsModalRef,
    private modalService: BsModalService) {
  }

  ngOnInit(): void {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('GlobalSettings')) {
          this.canUpdate = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  editLocation(Location: any): void {
    const initialState = {
      selectedLocation: Location,
    };
    this.modalRef = this.modalService.show(AddLocalityComponent, {
      class: 'modal-600 top-modal ip-modal-unset',
      initialState,
    });
  }

  deleteLocation(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.locality,
      fieldType: 'Location',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    let payload = {
      ids: [data?.id],
    };
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteLocation(payload));
        }
      });
    }

  }

  openAssignmentModal() {
    let initialState: any = {
      entityData: {
        ...this.params?.data,
        name: this.params?.data?.locality,
        module: 'Location'
      }
    };
    this.modalService.show(
      LocationUserAssignmentComponent,
      Object.assign({}, { class: 'right-modal modal-400 ph-modal-unset', initialState })
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
