import {
  Component,
  EventEmitter,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, takeUntil } from 'rxjs';
import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getAssignedToDetails,
  getPages,
  onFilterChanged,
} from 'src/app/core/utils/common.util';
import {
  DeleteLocation,
  FetchAllCityList,
  FetchAllZoneList,
  FetchLocationList,
  UpdateLocationFiltersPayload,
} from 'src/app/reducers/site/site.actions';
import {
  getAllCityList,
  getAllZoneList,
  getLocationFiltersPayload,
  getLocationList,
} from 'src/app/reducers/site/site.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { BulkUpdateComponent } from '../bulk-update/bulk-update.component';
import { LocationUserAssignmentComponent } from '../location-user-assignment/location-user-assignment.component';
import { LocalityActionsComponent } from './locality-actions/locality-actions.component';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';

@Component({
  selector: 'manage-locality',
  templateUrl: './manage-locality.component.html',
})
export class ManageLocalityComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  @Input() allUserList: any;

  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: 10,
  };
  appliedFilter: any;

  gridApi: any;
  gridColumnApi: any;
  gridOptions: any;
  defaultColDef: any;

  allLocationData: any;
  allZoneData: any[];
  allCityData: any[];
  zoneList: any[];
  getPages = getPages;
  onFilterChanged = onFilterChanged;
  canBulkAssignment: boolean = false;
  canBulkUpdate: boolean = false;
  canBulkDelete: boolean = false;
  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.store.dispatch(new FetchAllCityList());
    this.store.dispatch(new FetchAllZoneList());
  }

  ngOnInit(): void {
    this.store
    .select(getPermissions)
    .pipe(takeUntil(this.stopper))
    .subscribe((permissions: any) => {
      const permissionsSet = new Set(permissions);
      this.canBulkAssignment = permissionsSet.has('Permissions.GlobalSettings.BulkAssignment');
      this.canBulkDelete = permissionsSet.has('Permissions.GlobalSettings.BulkDelete');
      this.canBulkUpdate  = permissionsSet.has('Permissions.GlobalSettings.BulkUpdate');
    });
    this.store
      .select(getLocationList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allLocationData = data;
      });

    this.store
      .select(getAllCityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allCityData = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this.store
      .select(getAllZoneList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allZoneData = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
        this.zoneList = this.allZoneData;
      });

    this.store
      .select(getLocationFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.currOffset = this.filtersPayload?.pageNumber - 1;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          SearchText: this.filtersPayload?.SearchText,
          cityIds: this.filtersPayload?.cityIds,
        };
      });
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.locationFilterFunction();
    });
    this.locationFilterFunction();
    this.localityGridSettings();

  }

  localityGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Locality Name',
        field: 'Locality Name',
        valueGetter: (params: any) => [params.data?.locality],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Zone',
        field: 'Zone',
        valueGetter: (params: any) => [params.data?.zone?.name],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<p>${params.value} 
            </p>`;
        },
      },
      {
        headerName: 'City',
        field: 'City',
        valueGetter: (params: any) => [params.data?.city?.name],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<p>${params.value} 
            </p>`;
        },
      },
      {
        headerName: 'Assigned To',
        field: 'Assigned To',
        valueGetter: (params: any) => [
          params.data?.userAssignment?.userIds
            ? params.data.userAssignment.userIds?.map(
              (id: any) =>
                getAssignedToDetails(id, this.allUserList, true) || ''
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Actions',
        maxWidth: 110,
        filter: false,
        cellRenderer: LocalityActionsComponent,
      },
    ];
    if (
      this.canBulkAssignment || this.canBulkDelete || this.canBulkUpdate
    ) {
      this.gridOptions.columnDefs.unshift({
          showRowGroup: true,
          cellRenderer: 'agGroupCellRenderer',
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          filter: false,
          maxWidth: 50,
      });
    }
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.store.dispatch(new UpdateLocationFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchLocationList());
    this.currOffset = 0;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.store.dispatch(new UpdateLocationFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchLocationList());
  }

  locationFilterFunction() {
    this.appliedFilter.pageNumber = 1;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      SearchText: this.searchTerm,
      cityIds: this.appliedFilter.cityIds,
      zoneIds: this.appliedFilter.zoneIds,
    };
    this.store.dispatch(new UpdateLocationFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchLocationList());
    this.currOffset = 0;
  }

  updateZoneList() {
    if (this.appliedFilter?.cityIds) {
      this.zoneList = this.allZoneData.filter(
        (zone: any) => this.appliedFilter.cityIds == zone.city?.id
      );
    } else {
      this.zoneList = this.allZoneData;
    }
    this.appliedFilter.zoneIds = null;
    this.locationFilterFunction();
  }

  deselectOptions() {
    let selectedNodes = this.gridApi?.getSelectedNodes();
    selectedNodes.forEach((node: any) => node.setSelected(false));
  }

  deleteLocalities() {
    let selectedIds: any;
    if (this.gridApi) {
      let selectedNodes = this.gridApi?.getSelectedNodes();
      selectedIds = selectedNodes?.map((node: any) => node.data?.id);
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      fieldType: 'these localities',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    let payload = {
      ids: selectedIds,
    };
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteLocation(payload));
        }
      });
    }
  }

  userAssignment() {
    let selectedNodesNames: any;
    let selectedIds: any;

    if (this.gridApi) {
      let selectedNodes = this.gridApi?.getSelectedNodes();
      selectedNodesNames = selectedNodes?.map(
        (node: any) => node.data?.locality
      );
      selectedIds = selectedNodes?.map((node: any) => node.data?.id);
    }
    let initialState: any = {
      entityData: {
        name: selectedNodesNames,
        id: selectedIds,
        module: 'Location',
        moduleName: 'Localities',
        multipleAssign: true,
      },
    };
    this.modalRef = this.modalService.show(
      LocationUserAssignmentComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-400 ph-modal-unset', initialState }
      )
    );
    if (this.modalRef.content) {
      this.modalRef.content.saveClicked.subscribe(() => {
        this.deselectOptions();
      });
    }
  }

  bulkUpdateZone() {
    let selectedNodesNames: any;
    let selectedIds: any;

    if (this.gridApi) {
      let selectedNodes = this.gridApi?.getSelectedNodes();
      selectedNodesNames = selectedNodes?.map(
        (node: any) => node.data?.locality
      );
      selectedIds = selectedNodes?.map((node: any) => node.data?.id);
    }
    let initialState: any = {
      entityData: {
        name: selectedNodesNames,
        id: selectedIds,
        moduleName: 'Localities',
        updateItem: 'zone',
        updateData: this.allZoneData,
      },
    };
    this.modalRef = this.modalService.show(
      BulkUpdateComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-400 ph-modal-unset', initialState }
      )
    );
    if (this.modalRef.content) {
      this.modalRef.content.saveClicked.subscribe(() => {
        this.deselectOptions();
      });
    }
  }

  bulkUpdateCity() {
    let selectedNodesNames: any;
    let selectedIds: any;

    if (this.gridApi) {
      let selectedNodes = this.gridApi?.getSelectedNodes();
      selectedNodesNames = selectedNodes?.map(
        (node: any) => node.data?.locality
      );
      selectedIds = selectedNodes?.map((node: any) => node.data?.id);
    }
    let initialState: any = {
      entityData: {
        name: selectedNodesNames,
        id: selectedIds,
        moduleName: 'Localities',
        updateItem: 'city',
        updateData: this.allCityData,
      },
    };
    this.modalRef = this.modalService.show(
      BulkUpdateComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-400 ph-modal-unset', initialState }
      )
    );
    if (this.modalRef.content) {
      this.modalRef.content.saveClicked.subscribe(() => {
        this.deselectOptions();
      });
    }
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
