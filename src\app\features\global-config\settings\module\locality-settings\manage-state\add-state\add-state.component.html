<h5 class="text-white fw-600 bg-black px-20 py-12">
    {{ (selectedState ? 'GLOBAL.edit' : 'SIDEBAR.add' ) | translate }} {{ 'LOCATION.state' | translate }}
</h5>
<form [formGroup]="addStateForm" autocomplete="off" class="pb-20 px-30">
    <div class="field-label">
        {{ 'GLOBAL.select' | translate }} {{ 'LOCATION.country' | translate }}
    </div>
    <form-errors-wrapper>
        <ng-select [virtualScroll]="true" [items]="countryList" formControlName="country"
            placeholder="{{ 'GLOBAL.select' | translate }}" ResizableDropdown bindLabel="name" bindValue="id">
        </ng-select>
    </form-errors-wrapper>

    <div class="field-label-req">
        {{ 'LOCATION.state' | translate }} {{ 'GLOBAL.name' | translate }}
    </div>
    <form-errors-wrapper [control]="addStateForm.controls['state']"
        label="{{ 'LOCATION.state' | translate }} {{ 'GLOBAL.name' | translate }}">
        <input type="text" required formControlName="state" placeholder="ex. Maharashtra"
            (keyup.enter)="focusable.click()" tabindex="1">
    </form-errors-wrapper>

    <div class="flex-end mt-30">
        <button class="btn-no mr-20" id="addLocalityCancel" data-automate-id="addLocalityCancel"
            (click)="modalRef.hide()">
            {{ 'BUTTONS.cancel' | translate }}
        </button>
        <button #focusable class="btn-coal" id="addstate" data-automate-id="addstate" (click)="addState()">
            {{ (selectedState ? 'save' : 'SIDEBAR.add' ) | translate }}
        </button>
    </div>
</form>