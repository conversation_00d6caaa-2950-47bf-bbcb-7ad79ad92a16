import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { AddState, UpdateState } from 'src/app/reducers/site/site.actions';
import { getCountryList } from 'src/app/reducers/site/site.reducer';

@Component({
  selector: 'add-state',
  templateUrl: './add-state.component.html',
})
export class AddStateComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  addStateForm: FormGroup;
  selectedState: any;
  countryList: any[];

  constructor(
    private formBuilder: FormBuilder,
    private store: Store<AppState>,
    public modalRef: BsModalRef
  ) {
    this.addStateForm = this.formBuilder.group({
      country: [null, Validators.required],
      state: [null, Validators.required],
    });
  }

  ngOnInit(): void {
    if (this.selectedState) {
      this.addStateForm.patchValue({
        country: this.selectedState.country?.id,
        state: this.selectedState.name,
      });
    }

    this.store
      .select(getCountryList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });
  }

  addState() {
    if (!this.addStateForm.valid) {
      validateAllFormFields(this.addStateForm);
      return;
    }

    const selectedCountryId = this.addStateForm.value.country;
    const selectedCountry = this.countryList.find(country => country.id === selectedCountryId);

    const stateData = this.selectedState
      ? { 
          id: this.selectedState.id, 
          name: this.addStateForm.value.state 
        }
      : { 
          state: this.addStateForm.value.state, 
          countryId: selectedCountryId,
          country: selectedCountry?.name 
        };

    if (this.selectedState) {
      this.store.dispatch(new UpdateState(this.selectedState.id, stateData));
    } else {
      this.store.dispatch(new AddState(stateData));
    }

    this.modalRef.hide();
  }
}
