import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, takeUntil } from 'rxjs';
import { EMPTY_GUID, PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getAssignedToDetails, getPages, onFilterChanged } from 'src/app/core/utils/common.util';
import { getStateFiltersPayload, getStateList } from 'src/app/reducers/site/site.reducer';
import { StateActionsComponent } from './state-actions/state-actions.component';
import { DeleteState, FetchState, UpdateStateFiltersPayload } from 'src/app/reducers/site/site.actions';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { LocationUserAssignmentComponent } from '../location-user-assignment/location-user-assignment.component';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';

@Component({
  selector: 'manage-state',
  templateUrl: './manage-state.component.html',
})
export class ManageStateComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();

  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: 10,
  };
  appliedFilter: any;

  gridApi: any;
  gridColumnApi: any;
  gridOptions: any;
  defaultColDef: any;

  allStateData: any;
  @Input() allUserList: any;
  getPages = getPages;
  onFilterChanged = onFilterChanged

  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.stateGridSettings();
  }

  ngOnInit(): void {
    this.store
      .select(getStateList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allStateData = data;
      });

    this.store
      .select(getStateFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.currOffset = this.filtersPayload?.pageNumber - 1;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          SearchText: this.filtersPayload?.SearchText,
        };
      });
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.stateFilterFunction();
    });

    this.stateFilterFunction();
  }

  stateGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        maxWidth: 50,
      },
      {
        headerName: 'State Name',
        field: 'State Name',
        valueGetter: (params: any) => [params.data?.name],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Assigned To',
        field: 'Assigned To',
        valueGetter: (params: any) => [
          params.data?.userAssignment?.userIds
            ? params.data.userAssignment.userIds?.map((id: any) =>
              id !== EMPTY_GUID
                ? ' ' + (getAssignedToDetails(id, this.allUserList)?.firstName || '') + ' ' +
                (getAssignedToDetails(id, this.allUserList)?.lastName || '')
                : '') : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Actions',
        maxWidth: 110,
        filter: false,
        cellRenderer: StateActionsComponent,
      },
    ];
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.store.dispatch(new UpdateStateFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchState());
    this.currOffset = 0;
  }

  stateFilterFunction() {
    this.appliedFilter.pageNumber = 1;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      SearchText: this.searchTerm,
    };
    this.store.dispatch(new UpdateStateFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchState());
    this.currOffset = 0;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.store.dispatch(new UpdateStateFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchState());
  }

  deselectOptions() {
    let selectedNodes = this.gridApi.getSelectedNodes();
    selectedNodes.forEach((node: any) => node.setSelected(false));
  }

  deleteStates() {
    let selectedIds: any
    if (this.gridApi) {
      let selectedNodes = this.gridApi.getSelectedNodes();
      selectedIds = selectedNodes?.map((node: any) => node.data?.id);
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      fieldType: 'these states',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteState(selectedIds));
        }
      });
    }
  }

  userAssignment() {
    let selectedNodesNames: any
    let selectedIds: any

    if (this.gridApi) {
      let selectedNodes = this.gridApi.getSelectedNodes();
      selectedNodesNames = selectedNodes?.map((node: any) => node.data?.name);
      selectedIds = selectedNodes?.map((node: any) => node.data?.id);

    }
    let initialState: any = {
      entityData: {
        name: selectedNodesNames,
        id: selectedIds,
        module: 'State',
        moduleName: 'States',
        multipleAssign: true
      },
    };
    this.modalRef = this.modalService.show(
      LocationUserAssignmentComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-400 ph-modal-unset', initialState }
      )
    );
    if (this.modalRef.content) {
      this.modalRef.content.saveClicked.subscribe(() => {
        this.deselectOptions();
      })
    }
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm === '' || this.searchTerm === null) {
        return
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
