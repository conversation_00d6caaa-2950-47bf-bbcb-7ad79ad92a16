import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { AddStateComponent } from '../add-state/add-state.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { LocationUserAssignmentComponent } from '../../location-user-assignment/location-user-assignment.component';
import { DeleteState } from 'src/app/reducers/site/site.actions';

@Component({
  selector: 'state-actions',
  templateUrl: './state-actions.component.html',
})
export class StateActionsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canUpdate: boolean;
  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) { }

  ngOnInit(): void {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('GlobalSettings')) {
          this.canUpdate = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  editState(state: any): void {
    const initialState = {
      selectedState: state,
    };
    this.modalRef = this.modalService.show(AddStateComponent, {
      class: 'modal-600 top-modal ip-modal-unset',
      initialState,
    });
  }

  deleteState(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.name,
      fieldType: 'state',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteState(data?.id));
        }
      });
    }
  }

  openAssignmentModal() {
    let initialState: any = {
      entityData: {
        ...this.params?.data,
        module: 'State',
      },
    };
    this.modalService.show(
      LocationUserAssignmentComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-400 ph-modal-unset', initialState }
      )
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
