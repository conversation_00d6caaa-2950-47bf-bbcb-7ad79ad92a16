<h5 class="text-white fw-600 bg-black px-20 py-12">{{ (selectedZone ? 'GLOBAL.edit' : 'SIDEBAR.add' ) | translate }}
    {{'LOCATION.zone' | translate}}</h5>
<form [formGroup]="addZoneForm" autocomplete="off" class="pb-20 px-30">
    <div class="d-flex flex-wrap w-100 ph-flex-col">
        <!-- <div class="w-50 ph-w-100">
            <div class="mr-20 ph-mr-0">
                <div class="field-label">
                    {{'GLOBAL.select' | translate}} {{'LOCATION.country'|translate }}
                </div>
                <form-errors-wrapper>
                    <input type="text" formControlName="country" readonly placeholder="country" style="cursor: none;"/>
                </form-errors-wrapper>
            </div>
        </div> -->

        <!-- <div class="w-50 ph-w-100">
            <div class="mr-20 ph-mr-0 ph-mt-20">
                <div class="field-label">
                    {{'GLOBAL.select' | translate}} {{'LOCATION.state'|translate }}
                </div>
                <form-errors-wrapper>
                    <input type="text" placeholder="state" formControlName="state" readonly style="cursor: none;"/>
                </form-errors-wrapper>
            </div>
        </div> -->

        <!-- <div class="w-50 ph-w-100">
            <div class="mr-20 ph-mr-0">
                <div class="field-label">{{'GLOBAL.select' | translate}} {{'LOCATION.city'|translate }}</div>
                <form-errors-wrapper>
                    <ng-select [virtualScroll]="true" [items]="allCityData" [readonly]="selectedZone ? true : false"
                    (change)="onSelectCity($event)"
                        ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="name" bindValue="id"
                        formControlName="cityId">
                    </ng-select>
                </form-errors-wrapper>
            </div>
        </div> -->
        <div class="w-50 ph-w-100">
            <div class="mr-20 ph-mr-0 ph-mt-20">
                <div class="field-label-req">
                    {{'LOCATION.zone' | translate}} {{'GLOBAL.name'|translate }}
                </div>
                <form-errors-wrapper [control]="addZoneForm.controls['names']"
                    label="{{'LOCATION.zone' | translate}} {{'GLOBAL.name' | translate }}">
                    <input type="text" required formControlName="names" placeholder="ex. Central">
                </form-errors-wrapper>
            </div>
        </div>
    </div>
    <div class="flex-end mt-30">
        <button class="btn-gray mr-20" id="addLocalityCancel" data-automate-id="addLocalityCancel"
            (click)="modalRef.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
        <button class="btn-coal" id="addLocality" data-automate-id="addLocality" (click)="addZone()">
            {{ (selectedZone ? 'save' : 'SIDEBAR.add' ) | translate }}</button>
    </div>
</form>