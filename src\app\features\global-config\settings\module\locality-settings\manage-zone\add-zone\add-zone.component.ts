import { state } from '@angular/animations';
import { Component, EventEmitter, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { AddZone, FetchCountry, FetchState, UpdateZone } from 'src/app/reducers/site/site.actions';
import { getAllCityList, getCountryList, getStateList } from 'src/app/reducers/site/site.reducer';

@Component({
  selector: 'add-zone',
  templateUrl: './add-zone.component.html',
})
export class AddZoneComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  addZoneForm: FormGroup;
  selectedZone: any;
  allCityData: any[];
  stateList: any[];
  countryList: any[];

  constructor(
    private formBuilder: FormBuilder,
    private store: Store<AppState>,
    public modalRef: BsModalRef
  ) {
    // this.store.dispatch(new FetchCountry());
    // this.store.dispatch(new FetchState)
    this.addZoneForm = this.formBuilder.group({
      country: [null],
      cityId: [null],
      names: [null, Validators.required],
      state: [null],
    });
  }

  ngOnInit(): void {
    if (this.selectedZone) {
      this.addZoneForm.patchValue({
        country: this.selectedZone.city?.state?.country?.name,
        state: this.selectedZone.city?.state?.name,
        cityId: this.selectedZone.city?.id,
        names: this.selectedZone.editableName,
      });
    }

    this.store
      .select(getAllCityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allCityData = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

      this.store
      .select(getStateList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.stateList = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

      this.store
      .select(getCountryList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data?.items;
      });
  }

  addZone() {
    if (!this.addZoneForm.valid) {
      validateAllFormFields(this.addZoneForm);
      return;
    }

    let addZoneForm = {
      ...this.addZoneForm.value,
      names: [this.addZoneForm.value.names],
      ...(this.selectedZone && {
        id: this.selectedZone.id,
        zoneName: this.addZoneForm.value.names
      }),
    };
    if (this.selectedZone) {
      this.store.dispatch(new UpdateZone(this.selectedZone.id, addZoneForm));
    } else {
      this.store.dispatch(new AddZone(addZoneForm));
    }
    this.modalRef.hide();
  }

  onSelectCity(data: any) {
    this.addZoneForm.patchValue({
      state: data ? data.state?.name : null,
      country: data ? data.state?.country?.name : null,
    });
  }  

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
