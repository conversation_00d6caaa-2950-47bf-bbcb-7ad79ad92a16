import {
  Component,
  EventEmitter,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, takeUntil } from 'rxjs';

import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getAssignedToDetails,
  getPages,
  onFilterChanged,
} from 'src/app/core/utils/common.util';
import { LocationUserAssignmentComponent } from 'src/app/features/global-config/settings/module/locality-settings/location-user-assignment/location-user-assignment.component';
import { ZoneActionsComponent } from 'src/app/features/global-config/settings/module/locality-settings/manage-zone/zone-actions/zone-actions.component';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  DeleteZone,
  Fetch<PERSON>ll<PERSON>ityList,
  Fetch<PERSON>oneList,
  UpdateZoneFiltersPayload,
} from 'src/app/reducers/site/site.actions';
import {
  getAllCityList,
  getZoneFiltersPayload,
  getZoneList,
} from 'src/app/reducers/site/site.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'manage-zone',
  templateUrl: './manage-zone.component.html',
})
export class ManageZoneComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  @Input() allUserList: any;

  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: 10,
  };
  appliedFilter: any;

  gridApi: any;
  gridColumnApi: any;
  gridOptions: any;
  defaultColDef: any;

  allZoneData: any;
  allCityData: any[];
  getPages = getPages;
  onFilterChanged = onFilterChanged;
  canBulkAssignment: boolean = false;
  canBulkDelete: boolean = false;
  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.store.dispatch(new FetchAllCityList());
  }

  ngOnInit(): void {

    this.store
    .select(getPermissions)
    .pipe(takeUntil(this.stopper))
    .subscribe((permissions: any) => {
      const permissionsSet = new Set(permissions);
      this.canBulkAssignment = permissionsSet.has('Permissions.GlobalSettings.BulkAssignment');
      this.canBulkDelete = permissionsSet.has('Permissions.GlobalSettings.BulkDelete');
    });
    this.store
      .select(getZoneList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allZoneData = data;
      });

    this.store
      .select(getAllCityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allCityData = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this.store
      .select(getZoneFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.currOffset = this.filtersPayload?.pageNumber - 1;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          SearchText: this.filtersPayload?.SearchText,
          cityIds: this.filtersPayload?.cityIds,
        };
      });
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.zoneFilterFunction();
    });

    this.zoneFilterFunction();
    this.zoneGridSettings();
  }

  zoneGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Zone',
        field: 'Zone',
        valueGetter: (params: any) => [params.data?.name],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<p>${params.value} </p>`;
        },
      },
      {
        headerName: 'Assigned To',
        field: 'Assigned To',
        valueGetter: (params: any) => [
          params.data?.userAssignment?.userIds
            ? params.data.userAssignment.userIds?.map(
              (id: any) =>
                getAssignedToDetails(id, this.allUserList, true) || ''
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Actions',
        maxWidth: 110,
        filter: false,
        cellRenderer: ZoneActionsComponent,
      },
    ];
    if (
      this.canBulkAssignment || this.canBulkDelete
    ) {
      this.gridOptions.columnDefs.unshift({
          showRowGroup: true,
          cellRenderer: 'agGroupCellRenderer',
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          filter: false,
          maxWidth: 50,
      });
    }
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.store.dispatch(new UpdateZoneFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchZoneList());
    this.currOffset = 0;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.store.dispatch(new UpdateZoneFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchZoneList());
  }

  zoneFilterFunction() {
    this.appliedFilter.pageNumber = 1;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      SearchText: this.searchTerm,
      cityIds: this.appliedFilter.cityIds,
    };
    this.store.dispatch(new UpdateZoneFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchZoneList());
    this.currOffset = 0;
  }

  deselectOptions() {
    let selectedNodes = this.gridApi?.getSelectedNodes();
    selectedNodes.forEach((node: any) => node.setSelected(false));
  }

  deleteZones() {
    let selectedIds: any;
    if (this.gridApi) {
      let selectedNodes = this.gridApi?.getSelectedNodes();
      selectedIds = selectedNodes?.map((node: any) => node.data?.id);
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      fieldType: 'these Zones',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    let payload = {
      zoneIds: selectedIds,
    };
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteZone(payload));
        }
      });
    }
  }

  userAssignment() {
    let selectedNodesNames: any;
    let selectedIds: any;

    if (this.gridApi) {
      let selectedNodes = this.gridApi?.getSelectedNodes();
      selectedNodesNames = selectedNodes?.map((node: any) => node.data?.name);
      selectedIds = selectedNodes?.map((node: any) => node.data?.id);
    }
    let initialState: any = {
      entityData: {
        name: selectedNodesNames,
        id: selectedIds,
        module: 'Zone',
        moduleName: 'Zones',
        multipleAssign: true,
      },
    };
    this.modalRef = this.modalService.show(
      LocationUserAssignmentComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-400 ph-modal-unset', initialState }
      )
    );
    if (this.modalRef.content) {
      this.modalRef.content.saveClicked.subscribe(() => {
        this.deselectOptions();
      });
    }
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
