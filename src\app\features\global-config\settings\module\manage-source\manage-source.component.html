<div class="pt-20 px-30">
  <div class="bg-light-pearl">
    <div class="flex-between">
      <div class="align-center">
        <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-12" (click)="goBack()"></div>
        <span class="icon ic-circle-nodes ic-sm ic-blue-1150 mr-8"></span>
        <div class="fw-600 header-3">Manage Source</div>
      </div>
      <!-- Enable/Disable Selected Button -->
      <div *ngIf="hasSelectedSources" class="d-flex align-items-center position-absolute right-30">
        <button class="btn-coal" [disabled]="isToggleInProgress" [ngClass]="{'opacity-50': isToggleInProgress}"
          (click)="saveSourceVisibility()">
          {{ isEnablingSelected ? 'Enable Selected' : 'Toggle Selected' }}</button>
      </div>
    </div>
    <div class="pt-16">
      <div class="bg-white w-100 border-gray">
        <form autocomplete="off" class="align-center py-10 px-12 no-validation">
          <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
          <input placeholder="type to search" class="border-0 outline-0 w-100" autocomplete="off"
            [(ngModel)]="searchTerm" name="searchTerm" id="inpSearchCustomFlag"
            (ngModelChange)="updateFilteredSources()">
          <span *ngIf="searchTerm" class="icon ic-x-circle ic-sm ic-slate-90 cursor-pointer"
            (click)="clearSearch()"></span>
        </form>
      </div>
      <div class="px-16 py-12 bg-white">
        <div class="flex-between mb-16">
          <!-- <div class="text-large fw-600 text-decoration-underline">Sources</div> -->
          <!-- <div class="align-center">
            <input type="checkbox" class="form-check-input w-16 h-16 br-5" [(ngModel)]="selectAll"
              name="selectAllCheckbox" (change)="toggleSelectAll()">
            <span class="ml-8 text-large fw-600 cursor-pointer tb-ml-4 ph-ml-2" (click)="toggleSelectAll()">select
              all</span>
          </div> -->
        </div>
        <div *ngIf="!isSourcesLoading; else loadingTemplate">
          <div class="row scrollbar max-h-100-210">
            <div *ngFor="let source of filterSources()" class="w-25 tb-w-33 ip-w-50 ph-w-100 mb-3">
              <div class="border br-6 d-flex align-items-center  justify-content-between bg-white h-52 px-12"
                [ngClass]="{'border-bottom-black': source.isEnabled}">
                <div class="d-flex align-items-center scrollbar">
                  <!-- <input type="checkbox" class="form-check-input w-16 h-16 mr-8 br-5" [(ngModel)]="source.selected"
    name="sourceCheckbox_{{source.id}}" (change)="toggleSourceSelection(source)"> -->
                  <ng-container *ngIf="source?.imageURL; else sourceInitial">
                    <img [type]="'leadrat'" [appImage]="s3BucketUrl + source?.imageURL" alt="source logo" width="24"
                      height="24" class="mx-2">
                  </ng-container>
                  <ng-template #sourceInitial>
                    <div
                      class="rounded-circle border bg-light-gray w-24 h-24 d-flex align-items-center justify-content-center">
                      {{ source.displayName.charAt(0).toUpperCase() }}
                    </div>
                  </ng-template>
                  <div class="fw-600 text-truncate-1 break-all ml-8">
                    {{ source.displayName }}
                  </div>
                </div>
                <div [title]="NON_DISABLEABLE_SOURCES.includes(source.value) ?
                  (source.value === 0 ? 'Direct source cannot be disabled' :
                   source.value === 23 ? 'QR Code source cannot be disabled' :
                   source.displayName + ' source cannot be disabled') :
                  (source.isEnabled ? 'Enabled (Click to disable)' : 'Disabled (Click to enable)')">
                  <input type="checkbox" class="toggle-switch toggle-active-sold" [checked]="source.isEnabled"
                    (click)="$event.preventDefault(); !NON_DISABLEABLE_SOURCES.includes(source.value) && toggleSourceVisibility(source)"
                    [ngClass]="{'pe-none': NON_DISABLEABLE_SOURCES.includes(source.value) || isToggleInProgress || (!canHide && source.isEnabled) || (!canUnhide && !source.isEnabled)}">
                  <label class="switch-label"
                    (click)="$event.preventDefault(); !NON_DISABLEABLE_SOURCES.includes(source.value) && toggleSourceVisibility(source)"
                    [ngClass]="{'pe-none': NON_DISABLEABLE_SOURCES.includes(source.value) || isToggleInProgress || (!canHide && source.isEnabled) || (!canUnhide && !source.isEnabled), 'opacity-50': isToggleInProgress}"></label>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="filterSources().length === 0" class="flex-col flex-center h-100-270">
            <img src="assets/images/layered-cards.svg" alt="No data found" width="160" height="140">
            <div class="fw-semi-bold text-xl text-mud">No sources found!</div>
            <p *ngIf="searchTerm" class="text-center mt-2">
              No sources match the search term "{{searchTerm}}". <a href="javascript:void(0)" (click)="clearSearch()"
                class="text-accent-green">Clear search</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #loadingTemplate>
  <div class="row">
    <div *ngFor="let i of [1,2,3,4,5,6,7,8]" class="w-25 tb-w-33 ip-w-50 ph-w-100 mb-3">
      <div class="border br-6 position-relative d-flex align-items-center h-52">
        <span class="icon w-100 h-100 icon__skeleton shimmer-multiple"></span>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #sourceWarningPopup>
  <div class="br-4 pb-20">
    <h2 class="h-100px align-center text-white fw-800 pl-20 brtr-4 brtl-4 waring-bg-pattern">
      <span *ngIf="!isBulkOperation">Are you sure you want to {{currentSource?.isEnabled ? 'disable' : 'enable'}}
        {{currentSource?.displayName}} source?</span>
      <span *ngIf="isBulkOperation">Are you sure you want to hide selected sources?</span>
    </h2>
    <div class="text-black-200 m-10 p-10 text-large br-4">
      Before proceeding, please assign
      <ng-container *ngIf="sourceCountData?.leadCount > 0">
        <span class="text-accent-green fw-600 text-decoration-underline cursor-pointer"
          (click)="navigateToLead()">{{sourceCountData?.leadCount}}</span> leads
      </ng-container>
      <ng-container *ngIf="sourceCountData?.leadCount > 0 && sourceCountData?.prospectCount > 0">
        and
      </ng-container>
      <ng-container *ngIf="sourceCountData?.prospectCount > 0">
        <span class="text-accent-green fw-600 text-decoration-underline cursor-pointer"
          (click)="navigateToData()">{{sourceCountData?.prospectCount}}</span> data
      </ng-container>
      currently associated with
      <span *ngIf="!isBulkOperation">this source</span>
      <span *ngIf="isBulkOperation">these sources</span>
      to a different source.
    </div>
    <div class="m-10">
      <h4 class="fw-600 text-red text text-decoration-underline ml-10">Important notes:</h4>
      <ul class="pl-20 pt-8">
        <li class="d-flex"><span class="dot dot-xxs bg-dark-700 mt-6 mx-4 mr-12"></span>
          <div>
            Once hidden, no new leads can be created using
            <span *ngIf="!isBulkOperation">this source or its</span>
            <span *ngIf="isBulkOperation">these sources or their</span>
            associated sub-sources via integration.
          </div>
        </li>
        <li class="d-flex"><span class="dot dot-xxs bg-dark-700 mt-6 mx-4 mr-12"></span>
          <div>
            New Leads/Data cannot be created using
            <span *ngIf="!isBulkOperation">this source or its</span>
            <span *ngIf="isBulkOperation">these sources or their</span>
            sub-sources.
          </div>
        </li>
        <li class="d-flex"><span class="dot dot-xxs bg-dark-700 mt-6 mx-4 mr-12"></span>
          <div>
            <span *ngIf="!isBulkOperation">This source and its</span>
            <span *ngIf="isBulkOperation">These sources and their</span>
            sub-sources will also be excluded from all related reports.
          </div>
        </li>
      </ul>
    </div>
    <div class="flex-center mt-30 pr-10">
      <div class="p-8 fw-600 px-20 rounded bg-white border border-black mr-20 cursor-pointer"
        [ngClass]="{'opacity-50 pe-none': isToggleInProgress}" (click)="modalRef.hide()">
        Cancel
      </div>
      <div (click)="confirmSourceDisable()"
        class="p-8 fw-600 px-20 rounded bg-white border border-black cursor-pointer mr-20"
        [ngClass]="{'opacity-50 pe-none': isToggleInProgress}" data-automate-id="disable">
        Disable
      </div>
      <div (click)="convertToDirect()"
        class="btn btn-sm text-mud w-125 br-5 fw-600 flex-center text-large bg-black text-white"
        [ngClass]="{'opacity-50 pe-none': isToggleInProgress}" data-automate-id="convertYes">
        Convert To Direct
      </div>
    </div>
  </div>
</ng-template>