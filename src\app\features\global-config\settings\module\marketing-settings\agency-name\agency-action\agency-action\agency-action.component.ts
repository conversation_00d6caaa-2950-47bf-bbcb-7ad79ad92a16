import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { AddAgencyComponent } from '../../add-agency-name/add-agency/add-agency.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { DeleteAgency } from 'src/app/reducers/manage-marketing/marketing.action';

@Component({
  selector: 'agency-action',
  templateUrl: './agency-action.component.html',
})
export class AgencyActionComponent implements OnInit {

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canUpdate: boolean;
  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) { }

  ngOnInit(): void {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('GlobalSettings')) {
          this.canUpdate = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  editAgent(agent: any): void {
    const initialState = {
      selectedAgent: agent,
    };
    this.modalRef = this.modalService.show(AddAgencyComponent, {
      class: 'right-modal modal-350 ip-modal-unset',
      initialState,
    });
  }

  deleteAgent(agent: any) {
    let initialState: any = {
      type: 'manageMarketingDelete',
      data: {
        buttonContent: 'Delete Agency',
        fieldType: 'Delete',
        heading: `Deleting Agency Name?`,
        agencyData: agent,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteAgency([agent?.id]));
        }
      });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
