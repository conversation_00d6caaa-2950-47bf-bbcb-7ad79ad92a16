<h5 class="text-white fw-600 bg-black px-20 py-12">Geo-fence radius
</h5>
<form [formGroup]="geoFencingForm" autocomplete="off" class="pb-20 px-30 ng-select-sm input-sm">
    <div class="field-label">Select Property </div>
    <form-errors-wrapper [control]="geoFencingForm.controls['PropertyIds']" label="Property">
        <ng-select [virtualScroll]="true" [ngClass]="{'pe-none blinking': propertyListIsLoading}" [items]="propertyList"
            ResizableDropdown [multiple]="true" [closeOnSelect]="false" (change)="onPropertyChange($event)"
            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="propertyName" bindValue="propertyId"
            formControlName="PropertyIds">
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container" [ngClass]="{'pe-none': !item.placeId}"><input type="checkbox"
                        id="item-{{index}}" data-automate-id="item-{{index}}" [checked]="item$.selected"
                        [disabled]="!item.placeId">
                    <span class="checkmark"></span><span class="text-truncate-1 break-all"
                        [ngClass]="{'text-muted': !item.placeId}">
                        {{item.propertyName}}</span>
                    <span class="error-message-custom top-13" *ngIf="!item.placeId">(Manual Location)</span>
                </div>
            </ng-template>
        </ng-select>
    </form-errors-wrapper>
    <div class="field-label">Select Project </div>
    <form-errors-wrapper [control]="geoFencingForm.controls['ProjectIds']" label="Project">
        <ng-select [virtualScroll]="true" [items]="projectList" [ngClass]="{'pe-none blinking': projectListIsLoading}"
            ResizableDropdown [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
            bindLabel="projectName" bindValue="projectId" formControlName="ProjectIds"
            (change)="onProjectChange($event)">
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container" [ngClass]="{'pe-none': !item.placeId}"><input type="checkbox"
                        id="item-{{index}}" data-automate-id="item-{{index}}" [checked]="item$.selected"
                        [disabled]="!item.placeId">
                    <span class="checkmark"></span><span class="text-truncate-1 break-all"
                        [ngClass]="{'text-muted': !item.placeId}" [disabled]="!item.placeId">
                        {{item.projectName}}</span>
                    <span class="error-text" *ngIf="!item.placeId">(Manual Location)</span>
                </div>
            </ng-template>
        </ng-select>
    </form-errors-wrapper>

    <div class="field-label-req">Set Geo Fence Radius</div>
    <div class="flex-between align-center">
        <form-errors-wrapper [control]="geoFencingForm.controls['GeoFenceRadius']" label="radius">
            <div class="flex-grow-1 mr-10">
                <input type="number" min="0" formControlName="GeoFenceRadius" placeholder="Enter radius" tabindex="1">
            </div>
        </form-errors-wrapper>
        <div class="d-flex cursor-pointer">
            <div class="flex-center border px-12 py-8 brbl-12 brtl-12"
                [ngClass]="selectedUnit === 'Meter' ? 'bg-dark  text-white' : ' text-dark'" (click)="setUnit('Meter')">
                m
            </div>
            <div class="flex-center border px-12 py-8 brbr-12 brtr-12"
                [ngClass]="selectedUnit === 'Kilometer' ? 'bg-dark  text-white' : ' text-dark'"
                (click)="setUnit('Kilometer')">
                km
            </div>
        </div>
    </div>
    <div class="flex-end mt-30">
        <button class="btn-gray mr-20" id="addCancel" data-automate-id="addCancel" (click)="modalRef.hide()">{{
            'BUTTONS.cancel' | translate }}</button>
        <button #focusable class="btn-coal" id="add" data-automate-id="add" (click)="onSubmit()">
            {{ 'Save' }}
        </button>
    </div>
</form>