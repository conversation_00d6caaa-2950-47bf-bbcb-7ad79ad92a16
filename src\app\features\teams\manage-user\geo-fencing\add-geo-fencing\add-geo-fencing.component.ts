import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { NgSelectComponent } from '@ng-select/ng-select';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { Radius } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { FetchProjectWithGoogleLocation } from 'src/app/reducers/project/project.action';
import { getProjectWithGoogleLocation, getProjectWithGoogleLocationIsLoading } from 'src/app/reducers/project/project.reducer';
import { FetchPropertyWithGoogleLocation } from 'src/app/reducers/property/property.actions';
import { getPropertyWithGoogleLocation, getPropertyWithGoogleLocationLoading } from 'src/app/reducers/property/property.reducer';
import { FetchGeoFencingList, UpdateGeoFencing } from 'src/app/reducers/teams/teams.actions';
import { getGeoFencingList, getGeoFencingListIsLoading } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'add-geo-fencing',
  templateUrl: './add-geo-fencing.component.html',
})
export class AddGeoFencingComponent implements OnInit, OnDestroy {
  @ViewChild('propertySelect') propertySelect: NgSelectComponent;
  @ViewChild('projectSelect') projectSelect: NgSelectComponent;

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  propertyList: any[] = [];
  propertyListIsLoading: boolean = true;
  projectList: any[] = [];
  projectListIsLoading: boolean = true;
  geoFencingForm: FormGroup;
  selectedUnit: any = 'Meter';
  geoFencingData: any;
  userId: string = '';
  isEditMode: boolean = false;
  isGeoFencingLoading: boolean = true;

  atLeastOneLocationValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const propertyIds = control.get('PropertyIds')?.value;
      const projectIds = control.get('ProjectIds')?.value;

      const hasProperty = propertyIds && Array.isArray(propertyIds) && propertyIds.length > 0;
      const hasProject = projectIds && Array.isArray(projectIds) && projectIds.length > 0;

      return !hasProperty && !hasProject ? { noLocation: true } : null;
    };
  }

  constructor(
    public modalRef: BsModalRef,
    private fb: FormBuilder,
    private store: Store<AppState>,
    private notificationService: NotificationsService
  ) { }

  ngOnInit(): void {
    this.geoFencingForm = this.fb.group({
      PropertyIds: [null],
      ProjectIds: [null],
      GeoFenceRadius: [null, Validators.required],
      RadiusUnit: [this.selectedUnit]
    }, {
      validators: this.atLeastOneLocationValidator()
    });
    this.isEditMode = !!this.geoFencingData;

    this.store.dispatch(new FetchPropertyWithGoogleLocation());
    this.store.dispatch(new FetchProjectWithGoogleLocation());
    if (this.userId) {
      this.store.dispatch(new FetchGeoFencingList(this.userId));
    }
    this.store
      .select(getGeoFencingList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.geoFencingData = data;
        this.patchFormValues();
      });

    this.store
      .select(getGeoFencingListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isGeoFencingLoading = data;
      });

    this.store
      .select(getPropertyWithGoogleLocation)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = this.sortItemsByPlaceId(data);
      });

    this.store
      .select(getPropertyWithGoogleLocationLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.propertyListIsLoading = data;
      });

    this.store
      .select(getProjectWithGoogleLocation)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = this.sortItemsByPlaceId(data);
      });

    this.store
      .select(getProjectWithGoogleLocationIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.projectListIsLoading = data;
      });
  }

  patchFormValues(): void {
    if (this.geoFencingData) {
      const radiusUnit = this.geoFencingData.radiusUnit;
      let unitString = 'Meter'; // default
      if (radiusUnit === Radius.Kilometer || radiusUnit === 2) {
        unitString = 'Kilometer';
      } else if (radiusUnit === Radius.Meter || radiusUnit === 1) {
        unitString = 'Meter';
      }
      this.selectedUnit = unitString;
      this.geoFencingForm.patchValue({
        PropertyIds: this.geoFencingData.properties?.map((property: any) => property?.propertyId) || [],
        ProjectIds: this.geoFencingData.projects?.map((project: any) => project?.projectId) || [],
        GeoFenceRadius: this.geoFencingData.geoFenceRadius,
        RadiusUnit: unitString
      });
    }
  }

  onPropertyChange(event: any) {
    if (event && Array.isArray(event)) {
      const validItems = event?.filter(item => item.placeId);
      if (validItems.length !== event.length) {
        const validPropertyIds = validItems?.map(item => item.propertyId);
        this.geoFencingForm.get('PropertyIds')?.setValue(validPropertyIds);
      }

      if (validItems.length > 0) {
        this.notificationService.remove();
      }
    }
  }

  onProjectChange(event: any) {
    if (event && Array.isArray(event)) {
      const validItems = event.filter(item => item.placeId);
      if (validItems.length !== event.length) {
        const validProjectIds = validItems?.map(item => item.projectId);
        this.geoFencingForm.get('ProjectIds').setValue(validProjectIds);
      }

      if (validItems.length > 0) {
        this.notificationService.remove();
      }
    }
  }

  sortItemsByPlaceId(items: any[]): any[] {
    if (!items || !Array.isArray(items)) {
      return items;
    }

    return [...items].sort((a, b) => {
      if (a.placeId && !b.placeId) {
        return -1;
      }
      if (!a.placeId && b.placeId) {
        return 1;
      }

      const aName = a.propertyName || a.projectName || '';
      const bName = b.propertyName || b.projectName || '';
      return aName.localeCompare(bName);
    });
  }

  setUnit(RadiusUnit: 'Meter' | 'Kilometer'): void {
    this.selectedUnit = RadiusUnit;
    this.geoFencingForm.get('RadiusUnit')?.setValue(RadiusUnit);
    this.geoFencingForm.get('RadiusUnit')?.markAsTouched();
  }

  onSubmit() {
    if (!this.geoFencingForm.valid) {
      validateAllFormFields(this.geoFencingForm);

      if (this.geoFencingForm.hasError('noLocation')) {
        this.notificationService.error(
          '',
          'You must select at least one property or project with a valid location.',
        );
      }
      return;
    }
    let radiusUnitValue = Radius.Meter;
    if (this.selectedUnit === 'Kilometer') {
      radiusUnitValue = Radius.Kilometer;
    } else if (this.selectedUnit === 'Meter') {
      radiusUnitValue = Radius.Meter;
    }

    let payload = {
      ...this.geoFencingForm.value,
      RadiusUnit: radiusUnitValue,
      userId: this.userId || this.geoFencingData.userId
    };

    this.store.dispatch(new UpdateGeoFencing(payload));

    this.modalRef.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
