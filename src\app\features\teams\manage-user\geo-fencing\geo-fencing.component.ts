import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { ToggleGeoFencing } from 'src/app/reducers/teams/teams.actions';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'geo-fencing',
  templateUrl: './geo-fencing.component.html',
})
export class GeoFencingComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  params: any;
  status: boolean;
  canEditUser: boolean;
  constructor(private modalService: BsModalService,
    private modalRef: BsModalRef,
    private store: Store<AppState>,) { }

  ngOnInit(): void {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('Users')) {
          this.canEditUser = true;
        }
      });
  }
  agInit(params: any): void {
    this.params = params;
    this.status = this.params.data.isGeoFenceActive;
  }

  addGeoFencing(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'change the geo fencing of',
      title: data.firstName + ' ' + data.lastName,
      fieldType: 'user',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason === 'confirmed') {
          let payload = {
            userId: this.params.data.userId,
            isGeoFenceActive: this.status
          };
          this.store.dispatch(new ToggleGeoFencing(payload));
        } else {
          this.status = this.params.data.isGeoFenceActive;
        }
      });
    }

  }
}
