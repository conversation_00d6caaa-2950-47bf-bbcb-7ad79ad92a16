import { Action } from '@ngrx/store';

export enum ProductsActionTypes {
  FETCH_PRODUCTS = '[PRODUCTS] Fetch Products',
  FETCH_PRODUCTS_SUCCESS = '[PRODUCTS] Fetch Products Success',
}

export class FetchProducts implements Action {
  readonly type: string = ProductsActionTypes.FETCH_PRODUCTS;
  constructor() { }
}
export class FetchProductsSuccess implements Action {
  readonly type: string = ProductsActionTypes.FETCH_PRODUCTS_SUCCESS;
  constructor(public resp: any = '') { }
}