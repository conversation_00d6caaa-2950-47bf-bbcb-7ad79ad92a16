import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { FetchProducts, FetchProductsSuccess, ProductsActionTypes } from './products.actions';
import { ProductsService } from 'src/app/services/controllers/this/products.service';

@Injectable()
export class ProductsEffects {
  getProducts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProductsActionTypes.FETCH_PRODUCTS),
      switchMap((action: FetchProducts) => {
        return this.api.getProducts().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProductsSuccess(resp.data);
            }
            return new FetchProductsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: ProductsService,
  ) { }
}
