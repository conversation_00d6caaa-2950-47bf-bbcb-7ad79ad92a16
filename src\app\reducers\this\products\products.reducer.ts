import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { FetchProductsSuccess, ProductsActionTypes } from './products.actions';

export type ProductsState = {
  products?: any;
  isProductsLoading: boolean;
};
const initialState: ProductsState = {
  products: [],
  isProductsLoading: true,
};
export function productsReducer(
  state: ProductsState = initialState,
  action: Action
): ProductsState {
  switch (action.type) {
    case ProductsActionTypes.FETCH_PRODUCTS:
      return {
        ...state,
        isProductsLoading: true,
      };
    case ProductsActionTypes.FETCH_PRODUCTS_SUCCESS:
      return {
        ...state,
        products: (action as FetchProductsSuccess).resp,
        isProductsLoading: false,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.products;

export const getProducts = createSelector(
  selectFeature,
  (state: ProductsState) => state.products
);

export const getProductsIsLoading = createSelector(
  selectFeature,
  (state: ProductsState) => state.isProductsLoading
);