import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { getTenantName } from 'src/app/core/utils/common.util';
import { BaseService } from '../../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class ProductsService extends BaseService<any> {
  serviceBaseUrl: string;

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.thisBaseURL}api${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return '/products';
  }

  getProducts() {
    return this.http.get(`${this.serviceBaseUrl}?tenant=${getTenantName()}&isActive=true`);
  }
}
