// quality-score-chart.component.ts
import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';

@Component({
  selector: 'quality-score-chart',
  templateUrl: './quality-score-chart.component.html',
})
export class QualityScoreChartComponent implements OnChanges {
  @Input() score: number = 0;

  radius: number = 25;
  circumference: number = 2 * Math.PI * this.radius;
  segments: number = 8;
  gapAngle: number = 8;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['score']) {
      this.score = Math.max(0, Math.min(100, this.score));
    }
  }

  getScoreColor(): string {
    if (this.score <= 49) {
      return '#EA4335';
    } else if (this.score <= 75) {
      return '#FAC107';
    } else {
      return '#50BEA7';
    }
  }

  getActiveSegments(): number {
    return Math.floor((this.score / 100) * this.segments);
  }

  getSegmentsArray(): number[] {
    return Array.from({ length: this.segments }, (_, i) => i);
  }

  getSegmentPath(index: number): string {
    const segmentAngle = (360 - (this.segments * this.gapAngle)) / this.segments;
    const startAngle = index * (segmentAngle + this.gapAngle);
    const endAngle = startAngle + segmentAngle;

    const startAngleRad = (startAngle * Math.PI) / 180;
    const endAngleRad = (endAngle * Math.PI) / 180;

    const centerX = 30;
    const centerY = 30;

    const x1 = centerX + this.radius * Math.cos(startAngleRad);
    const y1 = centerY + this.radius * Math.sin(startAngleRad);
    const x2 = centerX + this.radius * Math.cos(endAngleRad);
    const y2 = centerY + this.radius * Math.sin(endAngleRad);

    const largeArcFlag = segmentAngle > 180 ? 1 : 0;

    return `M ${x1} ${y1} A ${this.radius} ${this.radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`;
  }

  isSegmentActive(index: number): boolean {
    return index < this.getActiveSegments();
  }
}